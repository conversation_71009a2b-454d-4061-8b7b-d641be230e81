<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.system.dal.mysql.statistic.StatisticInfoMapper">

    <select id="getDayCount" resultType="java.lang.Integer">
        select ifnull(sum(day_total_num),0) as dayTotalNum from system_statistic_info
    </select>

    <select id="getDataBySystemId" resultType="cn.powerchina.bjy.cloud.system.dal.dataobject.statistic.StatisticInfoDO">
        SELECT * FROM system_statistic_info WHERE system_id = #{tenantId}
    </select>

    <select id="getUsedSysNum" resultType="java.lang.Integer">
        select count(*) from system_statistic_info where sys_total_num >0
    </select>

    <select id="getUsedSysNumByDay" resultType="java.lang.Integer">
        select count(*) from system_statistic_info where day_total_num >0
    </select>

</mapper>