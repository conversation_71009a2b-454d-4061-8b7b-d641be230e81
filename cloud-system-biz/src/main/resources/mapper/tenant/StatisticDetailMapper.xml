<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.system.dal.mysql.statistic.StatisticDetailMapper">

    <select id="getDataByDay" resultType="java.lang.Integer">
        SELECT COUNT(*) AS total FROM system_statistic_detail WHERE system_id = #{tenantId} AND TO_DAYS(collect_time) = TO_DAYS(NOW());
    </select>

    <select id="getDataBySystemId" resultType="cn.powerchina.bjy.cloud.system.dal.dataobject.statistic.StatisticDetailDO">
        SELECT * FROM system_statistic_detail WHERE system_id = #{tenantId}
    </select>

    <select id="getDataByMonth" resultType="java.lang.Integer">
        SELECT count(*)  FROM system_statistic_detail WHERE deleted = 0 AND system_id = #{tenantId} AND MONTH(collect_time) = MONTH(NOW())
    </select>

    <select id="getDataByYear" resultType="java.lang.Integer">
        SELECT count(*)  FROM system_statistic_detail WHERE deleted = 0 AND system_id = #{tenantId} AND YEAR(collect_time) = YEAR(NOW())
    </select>

</mapper>