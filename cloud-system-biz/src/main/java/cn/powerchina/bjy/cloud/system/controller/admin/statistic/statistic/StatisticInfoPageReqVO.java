package cn.powerchina.bjy.cloud.system.controller.admin.statistic.statistic;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 统计总表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StatisticInfoPageReqVO extends PageParam {

    @Schema(description = "系统名称", example = "")
    private String name;

    @Schema(description = "开始时间", example = "")
    private String startTime;

    @Schema(description = "结束时间", example = "")
    private String endTime;

}
