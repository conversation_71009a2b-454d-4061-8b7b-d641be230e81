package cn.powerchina.bjy.cloud.link.edge.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;

import cn.powerchina.bjy.cloud.link.edge.common.util.JsonUtil;
import cn.powerchina.bjy.cloud.link.edge.dto.EdgeCommandDTO;
import cn.powerchina.bjy.cloud.link.edge.service.EdgeCommandService;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 指令下发的逻辑
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_DEVICE_COMMAND, consumerGroup = IotTopicConstant.GROUP_DEVICE_COMMAND)
public class EdgeCommandReceiver implements RocketMQListener {

    @Autowired
    private EdgeCommandService edgeCommandService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            EdgeCommandDTO entityDTO = parseMessageBody(messageView);
            log.info("receiver message {}", JSONObject.toJSON(entityDTO));
            if (ObjectUtil.isNull(entityDTO)) {
                log.error("Invalid edge command: {}", entityDTO);
                return ConsumeResult.SUCCESS;
            }
            if (Objects.isNull(entityDTO.getCurrentTime()) || System.currentTimeMillis() > (entityDTO.getCurrentTime() + 1000 * 60 * 60 * 2L)) {
                log.warn("edgeCommandReceive--->timeout，not execute command，exit");
                return ConsumeResult.SUCCESS;
            }
            edgeCommandService.writeCommand(entityDTO);
        } catch (Exception e) {
            log.error("指令下发属性解析异常--->error,entityDTO={}", JsonUtils.toJsonString(messageView), e);
        }
        return ConsumeResult.SUCCESS;

    }


    /**
     * 解析消息体为实体类
     */
    private EdgeCommandDTO parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, EdgeCommandDTO.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
