<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.policy.dal.mysql.questionreply.QuestionReplyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <select id="countByQuestionIds" resultType="cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.questionreply.QuestionReplyCount">
        SELECT question_id, count(id) AS count
        FROM policy_question_reply
        <where>
            question_id IN
            <foreach collection="questionIds" open="(" item="questionId" separator="," close=")">
                #{questionId}
            </foreach>
            AND deleted = 0
            GROUP BY question_id
        </where>
    </select>

    <select id="queryByIdIgnoreDeleted" resultType="cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.questionreply.QuestionReplyDO">
        SELECT *
        FROM policy_question_reply
        WHERE id = #{id}
    </select>

    <select id="queryQuestionIdByReplyUserId" resultType="long">
        SELECT question_id
        FROM policy_question_reply
        WHERE reply_user_id = #{replyUserId} AND reply_user_deleted = 0 AND reply_id IS NULL
        GROUP BY question_id
    </select>

    <select id="queryByQuestionIdAndUserId" resultType="cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.questionreply.QuestionReplyDO">
        SELECT *
        FROM policy_question_reply
        <where>
            question_id IN
            <foreach collection="questionIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND reply_user_id = #{userId}
        </where>
    </select>

    <select id="queryByIds" resultType="cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.questionreply.QuestionReplyDO">
        SELECT *
        FROM policy_question_reply
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="queryCountByReplyUserId" resultType="long">
        SELECT count(id)
        FROM policy_question_reply
        WHERE reply_user_id = #{replyUserId} AND reply_user_deleted = 0 AND reply_id IS NULL
    </select>

</mapper>