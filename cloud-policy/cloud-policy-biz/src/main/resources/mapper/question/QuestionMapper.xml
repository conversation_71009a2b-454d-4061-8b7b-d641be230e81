<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.policy.dal.mysql.question.QuestionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <select id="selectForPageIgnoreDeleted" resultType="cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.question.QuestionDO">
        SELECT *
        FROM policy_question
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        ORDER BY id DESC
        LIMIT #{start}, #{offset}
    </select>

    <select id="selectIgnoreDeleted" resultType="cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.question.QuestionDO">
        SELECT *
        FROM policy_question
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        ORDER BY id DESC
    </select>

</mapper>