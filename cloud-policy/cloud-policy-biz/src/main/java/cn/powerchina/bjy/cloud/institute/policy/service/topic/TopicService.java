package cn.powerchina.bjy.cloud.institute.policy.service.topic;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.font.vo.TopicTree;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.topic.vo.TopicPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.topic.vo.TopicSaveReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.topic.vo.TopicSimpleVO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.topic.TopicDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 政策法规模块 Service 接口
 *
 * <AUTHOR>
 */
public interface TopicService {

    /**
     * 创建政策法规模块
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTopic(@Valid TopicSaveReqVO createReqVO);

    /**
     * 更新政策法规模块
     *
     * @param updateReqVO 更新信息
     */
    void updateTopic(@Valid TopicSaveReqVO updateReqVO);

    /**
     * 删除政策法规模块
     *
     * @param id 编号
     */
    void deleteTopic(Long id);

    /**
     * 获得政策法规模块
     *
     * @param id 编号
     * @return 政策法规模块
     */
    TopicDO getTopic(Long id);

    /**
     * 根据code获取类目结构
     *
     * @param code 类目code
     * @return 类目结构
     */
    List<TopicSimpleVO> getByCode(String code);

    /**
     * 获得政策法规模块分页
     *
     * @param pageReqVO 分页查询
     * @return 政策法规模块分页
     */
    PageResult<TopicDO> getTopicPage(TopicPageReqVO pageReqVO);

    /**
     * 首页栏目
     * @param userId 用户id，先兼容一下，后期可能有用
     */
    List<TopicTree> homeTopicTree(Long userId);

    /**
     * 查询子级根据code
     * @param topicCode 栏目code
     * @return
     */
    List<TopicDO>  childrenByCode(String topicCode);

    /**
     * 根据分类查询栏目
     * @param topicCode 栏目code
     * @return
     */
    TopicDO findByCode(String topicCode);
}