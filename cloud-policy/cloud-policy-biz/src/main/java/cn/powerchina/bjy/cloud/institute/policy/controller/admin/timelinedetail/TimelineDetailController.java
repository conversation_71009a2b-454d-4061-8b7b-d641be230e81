package cn.powerchina.bjy.cloud.institute.policy.controller.admin.timelinedetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.timelinedetail.vo.TimelineDetailPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.timelinedetail.vo.TimelineDetailRespVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.timelinedetail.vo.TimelineDetailSaveReqVO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.timelinedetail.TimelineDetailDO;
import cn.powerchina.bjy.cloud.institute.policy.service.timelinedetail.TimelineDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "政企-管理后台 - 时间轴、大事件详情")
@RestController
@RequestMapping("/plan/policy/timeline/detail")
@Validated
public class TimelineDetailController {

    @Resource
    private TimelineDetailService timelineDetailService;

//    @PostMapping("/create")
    @Operation(summary = "创建时间轴、大事件详情")
    @PreAuthorize("@ss.hasPermission('policy:timeline-detail:create')")
    public CommonResult<Long> createTimelineDetail(@Valid @RequestBody TimelineDetailSaveReqVO createReqVO) {
        return success(timelineDetailService.createTimelineDetail(createReqVO));
    }

//    @PutMapping("/update")
    @Operation(summary = "更新时间轴、大事件详情")
    @PreAuthorize("@ss.hasPermission('policy:timeline-detail:update')")
    public CommonResult<Boolean> updateTimelineDetail(@Valid @RequestBody TimelineDetailSaveReqVO updateReqVO) {
        timelineDetailService.updateTimelineDetail(updateReqVO);
        return success(true);
    }

//    @DeleteMapping("/delete")
    @Operation(summary = "删除时间轴、大事件详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('policy:timeline-detail:delete')")
    public CommonResult<Boolean> deleteTimelineDetail(@RequestParam("id") Long id) {
        timelineDetailService.deleteTimelineDetail(id);
        return success(true);
    }

//    @GetMapping("/get")
    @Operation(summary = "获得时间轴、大事件详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('policy:timeline-detail:query')")
    public CommonResult<TimelineDetailRespVO> getTimelineDetail(@RequestParam("id") Long id) {
        TimelineDetailDO timelineDetail = timelineDetailService.getTimelineDetail(id);
        return success(BeanUtils.toBean(timelineDetail, TimelineDetailRespVO.class));
    }

//    @GetMapping("/page")
    @Operation(summary = "获得时间轴、大事件详情分页")
    @PreAuthorize("@ss.hasPermission('policy:timeline-detail:query')")
    public CommonResult<PageResult<TimelineDetailRespVO>> getTimelineDetailPage(@Valid TimelineDetailPageReqVO pageReqVO) {
        PageResult<TimelineDetailDO> pageResult = timelineDetailService.getTimelineDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TimelineDetailRespVO.class));
    }

}