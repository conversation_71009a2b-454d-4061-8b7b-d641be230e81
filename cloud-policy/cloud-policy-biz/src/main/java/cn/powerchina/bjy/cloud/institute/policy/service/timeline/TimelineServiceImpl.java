package cn.powerchina.bjy.cloud.institute.policy.service.timeline;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.timeline.vo.TimelinePageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.timeline.vo.TimelineRespVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.timeline.vo.TimelineSaveReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.timelinedetail.vo.TimelineDetailRespVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.timelinedetail.vo.TimelineDetailSaveReqVO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.timeline.TimelineDO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.timelinedetail.TimelineDetailDO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.topic.TopicDO;
import cn.powerchina.bjy.cloud.institute.policy.dal.mysql.timeline.TimelineMapper;
import cn.powerchina.bjy.cloud.institute.policy.dal.mysql.timelinedetail.TimelineDetailMapper;
import cn.powerchina.bjy.cloud.institute.policy.dal.mysql.topic.TopicMapper;
import cn.powerchina.bjy.cloud.institute.policy.enums.TimelineStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.policy.enums.ErrorCodeConstants.TIMELINE_NOT_EXISTS;
import static cn.powerchina.bjy.cloud.institute.policy.enums.ErrorCodeConstants.TIMELINE_RELEASED;
import static cn.powerchina.bjy.cloud.institute.policy.enums.PublishStatusEnum.ONLINE;
import static cn.powerchina.bjy.cloud.institute.policy.enums.PublishStatusEnum.UNPUBLISHED;

/**
 * 时间轴、大事件 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class TimelineServiceImpl implements TimelineService {

    @Resource
    private TopicMapper topicMapper;

    @Resource
    private TimelineMapper timelineMapper;

    @Resource
    private TimelineDetailMapper timelineDetailMapper;

    @Override
    @Transactional
    public Long createTimeline(TimelineSaveReqVO createReqVO) {
        if (hasPublishExits(createReqVO)) {
            throw exception(TIMELINE_RELEASED);
        }
        // 插入
        TimelineDO timeline = BeanUtils.toBean(createReqVO, TimelineDO.class);
        TopicDO topicDO = topicMapper.selectById(createReqVO.getTopicId());
        timeline.setTopicName(topicDO.getName());
        if (TimelineStatusEnum.RELEASE.getCode().equals(createReqVO.getStatus())) {
            timeline.setPublishTime(LocalDateTime.now());
        }
        timelineMapper.insert(timeline);
        Long timelineId = timeline.getId();

        List<TimelineDetailSaveReqVO> data = createReqVO.getData();
        if (CollectionUtil.isEmpty(data)) {
            return timelineId;
        }
        List<TimelineDetailDO> detailDOS = new ArrayList<>(data.size());
        for (TimelineDetailSaveReqVO detailSaveReqVO : data) {
            detailSaveReqVO.setTimelineId(timelineId);
            detailDOS.add(BeanUtils.toBean(detailSaveReqVO, TimelineDetailDO.class));
        }
        timelineDetailMapper.insertBatch(detailDOS);
        // 返回
        return timelineId;
    }

    @Override
    @Transactional
    public void updateTimeline(TimelineSaveReqVO updateReqVO) {
        if (hasPublishExits(updateReqVO)) {
            throw exception(TIMELINE_RELEASED);
        }
        // 校验存在
        TimelineDO timelineDO = validateTimelineExists(updateReqVO.getId());
        // 更新
        TimelineDO updateObj = BeanUtils.toBean(updateReqVO, TimelineDO.class);
        if (timelineDO.getTopicId() != null && !timelineDO.getTopicId().equals(updateObj.getTopicId())) {
            TopicDO topicDO = topicMapper.selectById(updateReqVO.getTopicId());
            updateObj.setTopicName(topicDO.getName());
        }

        if (TimelineStatusEnum.RELEASE.getCode().equals(updateObj.getStatus())) {
            updateObj.setPublishTime(LocalDateTime.now());
        }
        if(updateObj.getStatus() == UNPUBLISHED.getCode()){
            updateObj.setPublishTime(null);
        }

        timelineMapper.updateById(updateObj);
        List<TimelineDetailSaveReqVO> data = updateReqVO.getData();
        if (CollectionUtil.isEmpty(data)) {
            return;
        }
        // 删除
        timelineDetailMapper.delete(new LambdaQueryWrapper<TimelineDetailDO>().eq(TimelineDetailDO::getTimelineId, timelineDO.getId()));
        List<TimelineDetailDO> detailDOS = new ArrayList<>(data.size());
        for (TimelineDetailSaveReqVO detailSaveReqVO : data) {
            detailSaveReqVO.setTimelineId(timelineDO.getId());
            detailSaveReqVO.setId(null);
            detailDOS.add(BeanUtils.toBean(detailSaveReqVO, TimelineDetailDO.class));
        }
        timelineDetailMapper.insertBatch(detailDOS);

    }

    @Override
    @Transactional
    public void deleteTimeline(List<Long> ids) {
        // 校验存在
        Set<Long> deletedIds = new HashSet<>(ids);
        for (Long id : deletedIds) {
            TimelineDO timelineDO = validateTimelineExists(id);
            // 只能删除未发布的
            if (TimelineStatusEnum.RELEASE.getCode().equals(timelineDO.getStatus())) {
                throw exception(TIMELINE_RELEASED);
            }
        }

        // 删除
        timelineMapper.deleteBatchIds(deletedIds);
        timelineDetailMapper.delete(new LambdaQueryWrapper<TimelineDetailDO>().in(TimelineDetailDO::getTimelineId, deletedIds));
    }

    private TimelineDO validateTimelineExists(Long id) {
        TimelineDO result = timelineMapper.selectById(id);
        if (result == null) {
            throw exception(TIMELINE_NOT_EXISTS);
        }

        return result;
    }

    private boolean hasPublishExits(TimelineSaveReqVO timelineReqVO) {
        Long topicId = timelineReqVO.getTopicId();
        Integer type = timelineReqVO.getType();
        if (null == topicId || null == type) {
            // TODO 抛异常
            throw exception(TIMELINE_NOT_EXISTS);
        }
        LambdaQueryWrapper<TimelineDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TimelineDO::getTopicId, topicId)
                .eq(TimelineDO::getType, type)
                .eq(TimelineDO::getStatus, TimelineStatusEnum.RELEASE.getCode());
        List<TimelineDO> publishTimelines = timelineMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(publishTimelines)) {
            log.info("该类目下不存在发布中的时间轴/大事件");
            return false;
        }
        if (publishTimelines.size() > 1) {
            log.info("该类目下已存在发布中的时间轴/大事件");
            return true;
        }

        TimelineDO timelineDO = publishTimelines.get(0);
        if (null == timelineReqVO.getId()) {
            if (TimelineStatusEnum.UN_RELEASE.getCode().equals(timelineReqVO.getStatus())) {
                return false;
            }
            log.info("[新增]该类目下已存在发布中的时间轴/大事件");
            return true;
        }
        // 校验修改的是否为上架中的，如果已存在发布中的，且不是自身，且自身为发布中状态
        if (!timelineDO.getId().equals(timelineReqVO.getId()) && TimelineStatusEnum.RELEASE.getCode().equals(timelineReqVO.getStatus())) {
            log.info("[修改非自身]该类目下已存在发布中的时间轴/大事件");
            return true;
        }
        return false;
    }

    @Override
    public TimelineRespVO getTimeline(Long id) {
        TimelineDO timelineDO = timelineMapper.selectById(id);
        if (null == timelineDO) {
            return null;
        }
        TimelineRespVO result = BeanUtils.toBean(timelineDO, TimelineRespVO.class);
        LambdaQueryWrapper<TimelineDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TimelineDetailDO::getTimelineId, timelineDO.getId()).orderByAsc(TimelineDetailDO::getSort);
        List<TimelineDetailDO> timelineDetailDOS = timelineDetailMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(timelineDetailDOS)) {
            return result;
        }
        result.setData(BeanUtils.toBean(timelineDetailDOS, TimelineDetailRespVO.class));
        return result;
    }

    @Override
    public void changeStatus(List<Long> ids, Integer type) {
//        TimelineDO timelineDO = validateTimelineExists(ids);
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        Set<Long> changeIds = new HashSet<>(ids);
        for (Long changeId : changeIds) {
            TimelineDO timelineDO = validateTimelineExists(changeId);
            // 上架
            if (1 == type && !TimelineStatusEnum.RELEASE.getCode().equals(timelineDO.getStatus())) {
                TimelineSaveReqVO onlineTimeline = BeanUtils.toBean(timelineDO, TimelineSaveReqVO.class);
                onlineTimeline.setStatus(TimelineStatusEnum.RELEASE.getCode());
                if (hasPublishExits(onlineTimeline)) {
                    throw exception(TIMELINE_RELEASED);
                }
                UpdateWrapper<TimelineDO> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(TimelineDO::getId, changeId);
                TimelineDO entity = new TimelineDO();
                entity.setStatus(TimelineStatusEnum.RELEASE.getCode());
                entity.setPublishTime(LocalDateTime.now());
                timelineMapper.update(entity, updateWrapper);
                continue;
            }

            // 下架-
            if (2 == type && TimelineStatusEnum.RELEASE.getCode().equals(timelineDO.getStatus())) {
//                UpdateWrapper<TimelineDO> updateWrapper = new UpdateWrapper<>();
//                updateWrapper.lambda().eq(TimelineDO::getId, changeId);
//                TimelineDO entity = new TimelineDO();
//                entity.setStatus(TimelineStatusEnum.VERB.getCode());
//                timelineMapper.update(entity, updateWrapper);
                timelineMapper.updateStatus(changeId, TimelineStatusEnum.VERB.getCode());
            }
        }
    }

    @Override
    public PageResult<TimelineRespVO> getTimelinePage(TimelinePageReqVO pageReqVO) {
        List<Long> topicIds = topicMapper.queryByCode(pageReqVO.getTopicCode());
        if (CollectionUtil.isEmpty(topicIds)) {
            return PageResult.empty();
        }
        pageReqVO.setTopicIds(topicIds);
        return BeanUtils.toBean(timelineMapper.selectPage(pageReqVO), TimelineRespVO.class);
    }

}