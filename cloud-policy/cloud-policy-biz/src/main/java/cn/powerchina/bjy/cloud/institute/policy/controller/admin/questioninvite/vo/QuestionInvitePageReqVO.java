package cn.powerchina.bjy.cloud.institute.policy.controller.admin.questioninvite.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 问题邀请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionInvitePageReqVO extends PageParam {

    @Schema(description = "问题id", example = "17819")
    private Long questionId;

    @Schema(description = "提问人id", example = "18544")
    private Long userId;

    @Schema(description = "被邀人id", example = "14242")
    private Long inviteUserId;

    @Schema(description = "邀请状态，1：默认无标签；2：已回答；3：已读（未回答）；4：忽略", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}