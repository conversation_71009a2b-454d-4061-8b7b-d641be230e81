package cn.powerchina.bjy.cloud.institute.policy.controller.admin.science.vo;

import cn.powerchina.bjy.cloud.institute.policy.controller.admin.article.vo.TopicTreeRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 技术规范 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ScienceRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17122")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "类型,1：图文 2：图片  3：视频", example = "1")
    @ExcelProperty("类型,1：图文 2：图片  3：视频")
    private Integer contentType;

    @Schema(description = "栏目id")
    @ExcelProperty("栏目id")
    private Long topicId;

    @Schema(description = "栏目名称")
    @ExcelProperty("栏目名称")
    private String topicName;

    @Schema(description = "模块类型")
    @ExcelProperty("模块类型")
    private String modelType;

    @Schema(description = "技术规范标题")
    @ExcelProperty("技术规范标题")
    private String title;

    @Schema(description = "技术规范封面")
    @ExcelProperty("技术规范封面")
    private String coverImage;

    @Schema(description = "技术规范摘要")
    @ExcelProperty("技术规范摘要")
    private String abstracts;

    @Schema(description = "技术规范内容")
    @ExcelProperty("技术规范内容")
    private String content;

    @Schema(description = "技术规范页面链接")
    @ExcelProperty("技术规范页面链接")
    private String href;

    @Schema(description = "状态 0: 编辑中  5:下线  10：展示", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态 0: 编辑中  5:下线  10：展示")
    private Integer status;

    @Schema(description = "发布时间")
    @ExcelProperty("发布时间")
    private LocalDateTime onlineTime;

    @Schema(description = "创建时间 ")
    @ExcelProperty("创建时间 ")
    private LocalDateTime createTime;

    @Schema(description = "栏目树形结构")
    @ExcelProperty("栏目树形结构")
    private List<TopicTreeRespVO> topicList;
}

