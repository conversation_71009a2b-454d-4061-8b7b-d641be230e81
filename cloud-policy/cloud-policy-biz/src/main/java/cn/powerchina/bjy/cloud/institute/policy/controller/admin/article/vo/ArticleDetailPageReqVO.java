package cn.powerchina.bjy.cloud.institute.policy.controller.admin.article.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 图文详细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ArticleDetailPageReqVO extends PageParam {

    @Schema(description = "解读部门id", example = "27012")
    private Long explainDeptId;

    @Schema(description = "解读部门名称", example = "李四")
    private String explainDeptName;

    @Schema(description = "解读用户id", example = "29370")
    private String explainUserId;

    @Schema(description = "解读用户名称", example = "王五")
    private String explainUserName;

    @Schema(description = "推送部门id", example = "8749")
    private Long pushDeptId;

    @Schema(description = "推送部门名称", example = "李四")
    private String pushDeptName;

    @Schema(description = "编辑信息")
    private String pushEditorName;

    @Schema(description = "解读内容")
    private String explainContent;

    @Schema(description = "创建时间 ")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}