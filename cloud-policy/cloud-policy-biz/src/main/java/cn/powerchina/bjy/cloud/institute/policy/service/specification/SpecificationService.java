package cn.powerchina.bjy.cloud.institute.policy.service.specification;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.specification.vo.SpecificationPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.specification.vo.SpecificationRespVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.specification.vo.SpecificationSaveReqVO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.article.ArticleMainDO;
import jakarta.validation.Valid;

import java.util.List;

public interface SpecificationService {
    Long createSpecification(@Valid SpecificationSaveReqVO createReqVO);

    Long publishSpecification(@Valid SpecificationSaveReqVO createReqVO);

    void updateSpecification(@Valid SpecificationSaveReqVO updateReqVO);

    void onlineSpecification(SpecificationSaveReqVO updateReqVO);

    void offlineSpecification(List<SpecificationSaveReqVO> updateReqVO);

    void deleteSpecification(List<Long> ids);

    ArticleMainDO getSpecification(Long id);

   PageResult<SpecificationRespVO> getSpecificationPage(SpecificationPageReqVO pageReqVO);

    List<ArticleMainDO> getSpecificationList();

    String getSpecificationHref(Long id);
}
