package cn.powerchina.bjy.cloud.institute.policy.service.questionreply;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.font.question.vo.FrontQuestionReplyReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.font.question.vo.MyPraisePageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.font.question.vo.MyReplyPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.question.vo.QuestionDetailVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.questionreply.vo.QuestionReplyPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.questionreply.vo.QuestionReplySaveReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.questionreply.vo.QuestionReplySimpleVO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.question.QuestionDO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.questionlikes.QuestionLikesDO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.questionreply.QuestionReplyDO;
import cn.powerchina.bjy.cloud.institute.policy.dal.mysql.question.QuestionMapper;
import cn.powerchina.bjy.cloud.institute.policy.dal.mysql.questionlikes.QuestionLikesMapper;
import cn.powerchina.bjy.cloud.institute.policy.dal.mysql.questionreply.QuestionReplyMapper;
import cn.powerchina.bjy.cloud.institute.policy.enums.ErrorCodeConstants;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.policy.enums.ErrorCodeConstants.QUESTION_REPLY_EXISTS;
import static cn.powerchina.bjy.cloud.institute.policy.enums.ErrorCodeConstants.QUESTION_REPLY_NOT_EXISTS;

/**
 * 问题回答 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class QuestionReplyServiceImpl implements QuestionReplyService {

    @Resource
    private QuestionReplyMapper questionReplyMapper;

    @Resource
    private QuestionLikesMapper likesMapper;

    @Resource
    private QuestionMapper questionMapper;

    @Override
    public Long createQuestionReply(QuestionReplySaveReqVO createReqVO) {
        // 插入
        QuestionReplyDO questionReply = BeanUtils.toBean(createReqVO, QuestionReplyDO.class);
        questionReplyMapper.insert(questionReply);
        // 返回
        return questionReply.getId();
    }

    @Override
    public void updateQuestionReply(QuestionReplySaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionReplyExists(updateReqVO.getId());
        // 更新
        QuestionReplyDO updateObj = BeanUtils.toBean(updateReqVO, QuestionReplyDO.class);
        questionReplyMapper.updateById(updateObj);
    }

    @Transactional
    @Override
    public void deleteQuestionReply(Long id) {
        // 校验存在
        QuestionReplyDO replyDO = validateQuestionReplyExists(id);
        // 删除
        questionReplyMapper.deleteById(id);
        likesMapper.delete(new LambdaQueryWrapper<QuestionLikesDO>().eq(QuestionLikesDO::getReplyId, replyDO.getId()));
    }

    @Transactional
    @Override
    public void frontDeleteQuestionReply(Long id) {
        // 校验存在
        QuestionReplyDO replyDO = questionReplyMapper.queryByIdIgnoreDeleted(id);
        if (replyDO == null) {
            throw exception(QUESTION_REPLY_NOT_EXISTS);
        }
        UpdateWrapper<QuestionReplyDO> replyUpdate = new UpdateWrapper<>();
        replyUpdate.lambda().eq(QuestionReplyDO::getId, id);
        QuestionReplyDO replyEntity = new QuestionReplyDO();
        replyEntity.setReplyUserDeleted(Boolean.TRUE);
        replyEntity.setDeleted(Boolean.TRUE);
        questionReplyMapper.update(replyEntity, replyUpdate);

        UpdateWrapper<QuestionLikesDO> likeUpdate = new UpdateWrapper<>();
        likeUpdate.lambda().eq(QuestionLikesDO::getReplyId, id);
        QuestionLikesDO likeEntity = new QuestionLikesDO();
        likeEntity.setUserDeleted(Boolean.TRUE);
        likeEntity.setDeleted(Boolean.TRUE);
        likesMapper.update(likeEntity, likeUpdate);

    }

    private QuestionReplyDO validateQuestionReplyExists(Long id) {
        QuestionReplyDO replyDO = questionReplyMapper.selectById(id);
        if (replyDO == null) {
            throw exception(QUESTION_REPLY_NOT_EXISTS);
        }

        return replyDO;
    }

    @Override
    public QuestionReplyDO getQuestionReply(Long id) {
        return questionReplyMapper.selectById(id);
    }

    @Override
    public PageResult<QuestionReplyDO> getQuestionReplyPage(QuestionReplyPageReqVO pageReqVO) {
        return questionReplyMapper.selectPage(pageReqVO);
    }

    @Override
    public void top(Long replyId, Integer type) {

        QuestionReplyDO replyDO = validateQuestionReplyExists(replyId);
        log.info("置顶操作，replyId={}，type={}，oldStatus={}", replyId, type, replyDO.getTop());
        // 置顶
        if (1 == type && replyDO.getTop() == 2) {
            UpdateWrapper<QuestionReplyDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(QuestionReplyDO::getId, replyId);
            QuestionReplyDO entity = new QuestionReplyDO();
            entity.setTop(1);
            entity.setTopDate(LocalDateTime.now());
            entity.setTopUserId(SecurityFrameworkUtils.getLoginUserId());
            entity.setTopUserName(SecurityFrameworkUtils.getLoginUserName());
            questionReplyMapper.update(entity, updateWrapper);
            return;
        }
        // 取消置顶
        if (2 == type && replyDO.getTop() == 1) {
            UpdateWrapper<QuestionReplyDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(QuestionReplyDO::getId, replyId);
            QuestionReplyDO entity = new QuestionReplyDO();
            entity.setTop(2);
            entity.setTopCancelDate(LocalDateTime.now());
            entity.setTopCancelUserId(SecurityFrameworkUtils.getLoginUserId());
            entity.setTopCancelUserName(SecurityFrameworkUtils.getLoginUserName());
            questionReplyMapper.update(entity, updateWrapper);
        }
    }

    @Override
    public void praise(Long replyId, Integer type) {
        QuestionReplyDO replyDO = validateQuestionReplyExists(replyId);
        log.info("置顶操作，replyId={}，type={}", replyId, type);
        LambdaQueryWrapper<QuestionLikesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionLikesDO::getQuestionId, replyDO.getQuestionId());
        queryWrapper.eq(QuestionLikesDO::getReplyId, replyDO.getId());
        queryWrapper.eq(QuestionLikesDO::getUserId, SecurityFrameworkUtils.getLoginUserId());

        QuestionLikesDO questionLikesDO = likesMapper.selectOne(queryWrapper);

        // 点赞
        if (1 == type && questionLikesDO == null) {
            QuestionLikesDO.QuestionLikesDOBuilder builder = QuestionLikesDO.builder();
            builder.questionId(replyDO.getQuestionId())
                    .replyId(replyId)
                    .userId(SecurityFrameworkUtils.getLoginUserId())
                    .type(1)
                    .optTime(LocalDateTime.now());
            likesMapper.insert(builder.build());
            return;
        }
        // 取消点赞
        if (2 == type && questionLikesDO != null) {
            likesMapper.deleteById(questionLikesDO.getId());
//            UpdateWrapper<QuestionLikesDO> updateWrapper = new UpdateWrapper<>();
//            updateWrapper.lambda().eq(QuestionLikesDO::getReplyId, replyId)
//                    .eq(QuestionLikesDO::getQuestionId, replyDO.getQuestionId())
//                    .eq(QuestionLikesDO::getUserId, SecurityFrameworkUtils.getLoginUserId());
//            QuestionLikesDO entity = new QuestionLikesDO();
//            entity.setType(2);
//            entity.setOptTime(LocalDateTime.now());
//            likesMapper.update(entity, updateWrapper);
        }
    }

    @Override
    public Long questionReply(FrontQuestionReplyReqVO replyReqVO) {
        QuestionDO questionDO = questionMapper.selectById(replyReqVO.getQuestionId());
        if (null == questionDO) {
            throw exception(ErrorCodeConstants.QUESTION_NOT_EXISTS);
        }
        LambdaQueryWrapper<QuestionReplyDO> countQuery = new LambdaQueryWrapper<>();
        countQuery.eq(QuestionReplyDO::getQuestionId, questionDO.getId())
                .eq(QuestionReplyDO::getReplyUserId, SecurityFrameworkUtils.getLoginUserId());
        Long count = questionReplyMapper.selectCount(countQuery);
        if (count != null && count > 0L) {
            throw exception(QUESTION_REPLY_EXISTS);
        }

        QuestionReplyDO.QuestionReplyDOBuilder builder = QuestionReplyDO.builder();
        builder.questionId(replyReqVO.getQuestionId())
                .replyContent(replyReqVO.getReplyContent())
                .replyDate(LocalDateTime.now())
                .replyUserId(SecurityFrameworkUtils.getLoginUserId())
                .replyUserName(SecurityFrameworkUtils.getLoginUserName())
                .replyImgUrls(JsonUtils.toJsonString(replyReqVO.getReplyImgUrls()))
                .replyVideoUrls(JsonUtils.toJsonString(replyReqVO.getReplyVideoUrls()));

        QuestionReplyDO replyDO = builder.build();
        questionReplyMapper.insert(replyDO);

        return replyDO.getId();
    }

    @Override
    public PageResult<QuestionDetailVO> myReply(MyReplyPageReqVO pageReqVO) {
        Long currUserId = SecurityFrameworkUtils.getLoginUserId();
        Integer start = (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize();
        // 包含管理后台删除，但是用户未主动删除的回复
        List<Long> questionIds = questionReplyMapper.queryQuestionIdByReplyUserId(currUserId);
        if (CollectionUtil.isEmpty(questionIds)) {
            log.info("用户未回复过问题，userId={}", currUserId);
            return PageResult.empty();
        }
        // 包含管理后台删除，但是用户未主动删除回复的问题
        List<QuestionDO> questionDOS = questionMapper.selectForPageIgnoreDeleted(new HashSet<>(questionIds), start, pageReqVO.getPageSize());
        if (CollectionUtil.isEmpty(questionDOS)) {
            return PageResult.empty();
        }

        Set<Long> pageQuestionId = questionDOS.stream().map(QuestionDO::getId).collect(Collectors.toSet());
        List<QuestionReplyDO> myReplys = questionReplyMapper.queryByQuestionIdAndUserId(pageQuestionId, currUserId);

        if (CollectionUtil.isEmpty(myReplys)) {
            return PageResult.empty();
        }

        Map<Long, QuestionReplyDO> replyMap = myReplys.stream().collect(Collectors.toMap(QuestionReplyDO::getQuestionId, Function.identity(), (e1, e2) -> e1));

        PageResult<QuestionDetailVO> result = new PageResult<>((long) questionIds.size());

        questionDOS.forEach(item -> {
            if (!replyMap.containsKey(item.getId())) {
                return;
            }
            QuestionReplyDO replyDO = replyMap.get(item.getId());
            QuestionDetailVO vo = new QuestionDetailVO();
            vo.setTitle(item.getTitle());
            vo.setId(item.getId());
            vo.setContent(item.getContent());
            vo.setSysDeleted(item.getDeleted());
            QuestionReplySimpleVO replydata = BeanUtils.toBean(replyDO, QuestionReplySimpleVO.class);
            replydata.setEdit(Boolean.TRUE);

            vo.setReplyData(List.of(replydata));

            result.getList().add(vo);
        });
        return result;
    }

    @Override
    public PageResult<QuestionDetailVO> myPraise(MyPraisePageReqVO pageReqVO) {
        Long currUserId = SecurityFrameworkUtils.getLoginUserId();
        Integer start = (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize();
        // 包含管理后台删除，但是用户未主动删除的回复
        List<QuestionLikesDO> likesDOS = likesMapper.queryIdsByUserId(currUserId, start, pageReqVO.getPageSize());
        if (CollectionUtil.isEmpty(likesDOS)) {
            return PageResult.empty();
        }
        Set<Long> questionIds = new HashSet<>();
        Set<Long> replyIds = new HashSet<>();
        likesDOS.forEach(item -> {
            questionIds.add(item.getQuestionId());
            replyIds.add(item.getReplyId());
        });
        List<QuestionReplyDO> myReplys = questionReplyMapper.queryByIds(replyIds);

        if (CollectionUtil.isEmpty(myReplys)) {
            return PageResult.empty();
        }
        // 包含管理后台删除，但是用户未主动删除回复的问题
        List<QuestionDO> questionDOS = questionMapper.selectIgnoreDeleted(questionIds);
        if (CollectionUtil.isEmpty(questionDOS)) {
            return PageResult.empty();
        }
        Map<Long, QuestionDO> questionDOMap = questionDOS.stream().collect(Collectors.toMap(QuestionDO::getId, Function.identity(), (e1, e2) -> e1));
        Map<Long, QuestionReplyDO> replyMap = myReplys.stream().collect(Collectors.toMap(QuestionReplyDO::getId, Function.identity(), (e1, e2) -> e1));

        Long total = likesMapper.queryCountByUserId(currUserId);

        PageResult<QuestionDetailVO> result = new PageResult<>(total);

        likesDOS.forEach(item -> {
            if (!questionDOMap.containsKey(item.getQuestionId())) {
                return;
            }
            QuestionDO questionDO = questionDOMap.get(item.getQuestionId());
            QuestionDetailVO vo = new QuestionDetailVO();
            vo.setTitle(questionDO.getTitle());
            vo.setId(questionDO.getId());
            vo.setContent(questionDO.getContent());
            vo.setSysDeleted(questionDO.getDeleted());
            QuestionReplySimpleVO replydata = BeanUtils.toBean(replyMap.get(item.getReplyId()), QuestionReplySimpleVO.class);
            replydata.setEdit(Boolean.TRUE);

            vo.setReplyData(List.of(replydata));

            result.getList().add(vo);
        });
        return result;
    }

}