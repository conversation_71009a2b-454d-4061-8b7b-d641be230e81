package cn.powerchina.bjy.cloud.institute.policy.service.science;

import cn.hutool.core.date.DateUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.article.vo.ArticleMainPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.article.vo.TopicTreeRespVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.science.vo.SciencePageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.science.vo.ScienceRespVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.science.vo.ScienceSaveReqVO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.article.ArticleMainDO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.topic.TopicDO;
import cn.powerchina.bjy.cloud.institute.policy.dal.mysql.article.ArticleMainMapper;
import cn.powerchina.bjy.cloud.institute.policy.dal.mysql.topic.TopicMapper;
import cn.powerchina.bjy.cloud.institute.policy.enums.ArticleContentType;
import cn.powerchina.bjy.cloud.institute.policy.enums.PublishStatusEnum;
import cn.powerchina.bjy.cloud.institute.policy.enums.TopicModelEnum;
import cn.powerchina.bjy.cloud.institute.policy.service.article.ArticleMainService;
import cn.powerchina.bjy.cloud.institute.policy.service.system.PlanPolicySystemServiceImpl;
import cn.powerchina.bjy.cloud.institute.policy.service.topic.TopicService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.policy.enums.ErrorCodeConstants.TECHNICAL_SCIENCE_NOT_EXISTS;
import static cn.powerchina.bjy.cloud.institute.policy.enums.PublishStatusEnum.ONLINE;
import static cn.powerchina.bjy.cloud.institute.policy.enums.TopicEnum.TECHNICAL_KNOWLEDGE;
import static cn.powerchina.bjy.cloud.institute.policy.enums.TopicModelEnum.*;

/**
 * 技术规范 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ScienceServiceImpl implements ScienceService {
    @Resource
    public TopicService topicService;
    @Resource
    private ArticleMainMapper articleMainMapper;
    @Resource
    private ArticleMainService articleMainService;
    @Resource
    private PlanPolicySystemServiceImpl planPolicySystemService;
    @Resource
    private TopicMapper topicMapper;


    @Override
    public Long createScienceGraphic(ScienceSaveReqVO createReqVO) {
        // 插入
        TopicDO topicDO = topicService.findByCode(TECHNICAL_KNOWLEDGE.getCode());
        ArticleMainDO articleMainDO = BeanUtils.toBean(createReqVO, ArticleMainDO.class);
        articleMainDO.setTopicId(topicDO.getId());
        articleMainDO.setTopicName(topicDO.getName());
        articleMainDO.setTitle(createReqVO.getTitle());
        articleMainDO.setCoverImage(createReqVO.getCoverImage());
        articleMainDO.setContentType(ArticleContentType.RICH_TEXT.getCode());
        articleMainDO.setContent(createReqVO.getContent());
        articleMainDO.setModelType(SCIENCE_GRAPHIC_MODEL.getCode());
        articleMainDO.setStatus(createReqVO.getStatus());
        if(createReqVO.getStatus().equals(ONLINE.getCode())){
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedNowStr = now.format(formatter);
            LocalDateTime formattedNow = LocalDateTime.parse(formattedNowStr, formatter);
            articleMainDO.setOnlineTime(formattedNow);
        }
        articleMainMapper.insert(articleMainDO);
        String updatedHref = planPolicySystemService.systemDomainName() + "techn/science/graphicDetail?scienceType=" + ArticleContentType.RICH_TEXT.getCode() + "&id=" + articleMainDO.getId();
        articleMainDO.setHref(updatedHref);
        articleMainMapper.updateById(articleMainDO);
        // 返回
        return articleMainDO.getId();
    }
    @Override
    public Long createScienceIllustration(ScienceSaveReqVO createReqVO) {
        // 插入
        TopicDO topicDO = topicService.findByCode(TECHNICAL_KNOWLEDGE.getCode());
        ArticleMainDO articleMainDO = BeanUtils.toBean(createReqVO, ArticleMainDO.class);
        articleMainDO.setTopicId(topicDO.getId());
        articleMainDO.setTopicName(topicDO.getName());
        articleMainDO.setTitle(createReqVO.getTitle());
        articleMainDO.setAbstracts(createReqVO.getAbstracts());
        articleMainDO.setCoverImage(createReqVO.getCoverImage());
        articleMainDO.setContentType(ArticleContentType.IMG.getCode());
        articleMainDO.setContent(createReqVO.getContent());
        articleMainDO.setModelType(SCIENCE_ILLUSTRATION_MODEL.getCode());
        articleMainDO.setStatus(createReqVO.getStatus());
        if(createReqVO.getStatus().equals(ONLINE.getCode())){
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedNowStr = now.format(formatter);
            LocalDateTime formattedNow = LocalDateTime.parse(formattedNowStr, formatter);
            articleMainDO.setOnlineTime(formattedNow);
        }
        articleMainMapper.insert(articleMainDO);
        String updatedHref = planPolicySystemService.systemDomainName() + "techn/science/graphicDetail?scienceType=" + ArticleContentType.IMG.getCode() + "&id=" + articleMainDO.getId();
        articleMainDO.setHref(updatedHref);
        articleMainMapper.updateById(articleMainDO);
        // 返回
        return articleMainDO.getId();
    }
    @Override
    public Long createScienceVideo(ScienceSaveReqVO createReqVO) {
        // 插入
        TopicDO topicDO = topicService.findByCode(TECHNICAL_KNOWLEDGE.getCode());
        ArticleMainDO articleMainDO = BeanUtils.toBean(createReqVO, ArticleMainDO.class);
        articleMainDO.setTopicId(topicDO.getId());
        articleMainDO.setTopicName(topicDO.getName());
        articleMainDO.setTitle(createReqVO.getTitle());
        articleMainDO.setAbstracts(createReqVO.getAbstracts());
        articleMainDO.setCoverImage(createReqVO.getCoverImage());
        articleMainDO.setContentType(ArticleContentType.VIDEO.getCode());
        articleMainDO.setContent(createReqVO.getContent());
        articleMainDO.setModelType(SCIENCE_VIDEO_MODEL.getCode());
        articleMainDO.setStatus(createReqVO.getStatus());
        if(createReqVO.getStatus().equals(ONLINE.getCode())){
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String formattedNowStr = now.format(formatter);
            LocalDateTime formattedNow = LocalDateTime.parse(formattedNowStr, formatter);
            articleMainDO.setOnlineTime(formattedNow);
        }
        articleMainMapper.insert(articleMainDO);
        String updatedHref = planPolicySystemService.systemDomainName() + "techn/science/graphicDetail?scienceType=" + ArticleContentType.VIDEO.getCode() + "&id=" + articleMainDO.getId();
        articleMainDO.setHref(updatedHref);
        articleMainMapper.updateById(articleMainDO);
        // 返回
        return articleMainDO.getId();
    }
    @Override
    public Long publishScienceGraphic(ScienceSaveReqVO createReqVO) {
        // 插入
        TopicDO topicDO = topicService.findByCode(TECHNICAL_KNOWLEDGE.getCode());
        ArticleMainDO articleMainDO = BeanUtils.toBean(createReqVO, ArticleMainDO.class);
        articleMainDO.setTopicId(topicDO.getId());
        articleMainDO.setTopicName(topicDO.getName());
        articleMainDO.setTitle(createReqVO.getTitle());
        articleMainDO.setContentType(ArticleContentType.RICH_TEXT.getCode());
        articleMainDO.setContent(createReqVO.getContent());
        articleMainDO.setModelType(SCIENCE_GRAPHIC_MODEL.getCode());
        articleMainDO.setStatus(ONLINE.getCode());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedNowStr = now.format(formatter);
        LocalDateTime formattedNow = LocalDateTime.parse(formattedNowStr, formatter);
        articleMainDO.setOnlineTime(formattedNow);
        articleMainMapper.insert(articleMainDO);
        String updatedHref = planPolicySystemService.systemDomainName() + "techn/science/graphicDetail?scienceType=" + ArticleContentType.RICH_TEXT.getCode() + "&id=" + articleMainDO.getId();
        articleMainDO.setHref(updatedHref);
        articleMainMapper.updateById(articleMainDO);
        // 返回
        return articleMainDO.getId();
    }
    @Override
    public Long publishScienceIllustration(ScienceSaveReqVO createReqVO) {
        // 插入
        TopicDO topicDO = topicService.findByCode(TECHNICAL_KNOWLEDGE.getCode());
        ArticleMainDO articleMainDO = BeanUtils.toBean(createReqVO, ArticleMainDO.class);
        articleMainDO.setTopicId(topicDO.getId());
        articleMainDO.setTopicName(topicDO.getName());
        articleMainDO.setTitle(createReqVO.getTitle());
        articleMainDO.setAbstracts(createReqVO.getAbstracts());
        articleMainDO.setContentType(ArticleContentType.IMG.getCode());
        articleMainDO.setContent(createReqVO.getContent());
        articleMainDO.setModelType(SCIENCE_ILLUSTRATION_MODEL.getCode());
        articleMainDO.setStatus(ONLINE.getCode());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedNowStr = now.format(formatter);
        LocalDateTime formattedNow = LocalDateTime.parse(formattedNowStr, formatter);
        articleMainDO.setOnlineTime(formattedNow);
        articleMainMapper.insert(articleMainDO);
        String updatedHref = planPolicySystemService.systemDomainName() + "techn/science/graphicDetail?scienceType=" + ArticleContentType.IMG.getCode() + "&id=" + articleMainDO.getId();
        articleMainDO.setHref(updatedHref);
        articleMainMapper.updateById(articleMainDO);
        // 返回
        return articleMainDO.getId();
    }
    @Override
    public Long publishScienceVideo(ScienceSaveReqVO createReqVO) {
        // 插入
        TopicDO topicDO = topicService.findByCode(TECHNICAL_KNOWLEDGE.getCode());
        ArticleMainDO articleMainDO = BeanUtils.toBean(createReqVO, ArticleMainDO.class);
        articleMainDO.setTopicId(topicDO.getId());
        articleMainDO.setTopicName(topicDO.getName());
        articleMainDO.setTitle(createReqVO.getTitle());
        articleMainDO.setAbstracts(createReqVO.getAbstracts());
        articleMainDO.setContentType(ArticleContentType.VIDEO.getCode());
        articleMainDO.setContent(createReqVO.getContent());
        articleMainDO.setModelType(SCIENCE_VIDEO_MODEL.getCode());
        articleMainDO.setStatus(ONLINE.getCode());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedNowStr = now.format(formatter);
        LocalDateTime formattedNow = LocalDateTime.parse(formattedNowStr, formatter);
        articleMainDO.setOnlineTime(formattedNow);
        articleMainMapper.insert(articleMainDO);
        String updatedHref = planPolicySystemService.systemDomainName() + "techn/science/graphicDetail?scienceType=" + ArticleContentType.VIDEO.getCode() + "&id=" + articleMainDO.getId();
        articleMainDO.setHref(updatedHref);
        articleMainMapper.updateById(articleMainDO);
        // 返回
        return articleMainDO.getId();
    }
    @Override
    public void updateScience(ScienceSaveReqVO updateReqVO) {
        // 校验存在
        validateStandardExists(updateReqVO.getId());
        // 更新
        ArticleMainDO updateObj = BeanUtils.toBean(updateReqVO, ArticleMainDO.class);
        if(updateReqVO.getStatus().equals(ONLINE.getCode())){
            updateObj.setOnlineTime(DateUtil.toLocalDateTime(new Date()));
            articleMainMapper.updateById(updateObj);
        }else {
            articleMainMapper.updateById(updateObj);
        }
    }
    @Override
    public void onlineScience(ScienceSaveReqVO updateReqVO) {
        // 校验存在
        validateStandardExists(updateReqVO.getId());
        // 更新
        ArticleMainDO updateObj = BeanUtils.toBean(updateReqVO, ArticleMainDO.class);
        updateObj.setStatus(ONLINE.getCode());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedNowStr = now.format(formatter);
        LocalDateTime formattedNow = LocalDateTime.parse(formattedNowStr, formatter);
        updateObj.setOnlineTime(formattedNow);
        articleMainMapper.updateById(updateObj);
    }
    @Override
    public void offlineScience(List<ScienceSaveReqVO> updateReqVOs) {
        for (ScienceSaveReqVO updateReqVO : updateReqVOs) {
            // 校验存在
            validateStandardExists(updateReqVO.getId());
            // 更新
            ArticleMainDO updateObj = BeanUtils.toBean(updateReqVO, ArticleMainDO.class);
            updateObj.setStatus(PublishStatusEnum.OFFLINE.getCode());
            articleMainMapper.updateById(updateObj);
        }
    }
    private void validateStandardExists(Long id) {
        if (articleMainMapper.selectById(id) == null) {
            throw exception(TECHNICAL_SCIENCE_NOT_EXISTS);
        }
    }
    @Override
    public void deleteScience(List<Long> ids) {
        for (Long id : ids) {
            // 校验存在
            validateStandardExists(id);
        }
        // 删除
        for (Long id : ids) {
            articleMainMapper.deleteById(id);
        }
    }
    @Override
    public ArticleMainDO getScience(Long id) {
        return articleMainMapper.selectById(id);
    }
    @Override
    public String getScienceHref(Long id) {
        // 校验存在
        validateStandardExists(id);
        //获取链接
        return articleMainMapper.getHrefById(id);
    }

    @Override
    public PageResult<ScienceRespVO> getSciencePage(SciencePageReqVO pageReqVO) {
        pageReqVO.setTopicId(topicService.findByCode(pageReqVO.getTopicCode()).getId());
        PageResult<ScienceRespVO> pageResult = BeanUtils.toBean(articleMainMapper.selectSciencePage(BeanUtils.toBean(pageReqVO, ArticleMainPageReqVO.class)), ScienceRespVO.class);
        pageResult.getList().forEach(scienceRespVO -> {
            Long topicId = scienceRespVO.getTopicId();
            if (topicId != null) {
                List<TopicTreeRespVO> topicList = articleMainService.buildTopicList(topicId);
                scienceRespVO.setTopicList(topicList);
            }
        });
        return pageResult;
    }
    @Override
    public List<ArticleMainDO> getScienceGraphicList() {
        return articleMainMapper.scienceGraphicList();
    }
    @Override
    public List<ArticleMainDO> getScienceIllustrationList() {
        return articleMainMapper.scienceIllustrationList();
    }
    @Override
    public List<ArticleMainDO> getScienceVideoList() {
        return articleMainMapper.scienceVideoList();
    }
}
