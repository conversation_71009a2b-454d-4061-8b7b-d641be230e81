package cn.powerchina.bjy.cloud.institute.policy.dal.mysql.timelinedetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.timelinedetail.vo.TimelineDetailPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.timelinedetail.TimelineDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 时间轴、大事件 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TimelineDetailMapper extends BaseMapperX<TimelineDetailDO> {

    default PageResult<TimelineDetailDO> selectPage(TimelineDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TimelineDetailDO>()
                .eqIfPresent(TimelineDetailDO::getTimelineId, reqVO.getTimelineId())
                .eqIfPresent(TimelineDetailDO::getTitle, reqVO.getTitle())
                .eqIfPresent(TimelineDetailDO::getSort, reqVO.getSort())
                .betweenIfPresent(TimelineDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TimelineDetailDO::getId));
    }

}