package cn.powerchina.bjy.cloud.institute.policy.controller.admin.font.question.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 我的回答
 *
 * <AUTHOR>
 **/
@Schema(description = "前台 - 技术问答/问答消息/我的回答 Request VO")
@Data
public class MyReplyPageReqVO extends PageParam {

    /**
     * 问题id
     */
    private Long questionId;
}
