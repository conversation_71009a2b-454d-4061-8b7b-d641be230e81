package cn.powerchina.bjy.cloud.system.api.menu;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.system.api.menu.dto.MenuCreateReqDTO;
import cn.powerchina.bjy.cloud.system.api.menu.dto.MenuRespDTO;
import cn.powerchina.bjy.cloud.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

@FeignClient(name = ApiConstants.NAME) // TODO：fallbackFactory =
@Tag(name = "RPC 服务 - 菜单")
public interface MenuApi {

    String PREFIX = ApiConstants.PREFIX + "/menu";

    @GetMapping(PREFIX + "/roleId/menus")
    @Operation(summary = "获得角色id对应的菜单集合")
    @Parameter(name = "roleId", description = "角色id", required = true)
    CommonResult<List<MenuRespDTO>> roleIdMenus(@RequestParam("roleId") Long roleId);

    @GetMapping(PREFIX + "/roleCode/menus")
    @Operation(summary = "获得角色code对应的菜单集合")
    @Parameter(name = "code", description = "角色code", required = true)
    CommonResult<List<MenuRespDTO>> roleCodeMenus(@RequestParam("code") String code);

    @PostMapping(PREFIX + "/create")
    @Operation(summary = "创建菜单")
    CommonResult<Long> createMenu(@RequestBody MenuCreateReqDTO respDTO);

    @PostMapping(PREFIX + "/update")
    @Operation(summary = "创建菜单")
    CommonResult<Boolean> updateMenu(@RequestBody MenuCreateReqDTO respDTO);

    @PostMapping(PREFIX + "/delete")
    @Operation(summary = "创建菜单")
    CommonResult<Boolean> deleteMenu(@RequestParam("ids") Set<Long> ids);

    /**
     * 根据租户ID获取菜单列表
     *
     * @param tenantId
     * @return 不存在时反馈空列表
     */
    @GetMapping(PREFIX + "/tenantId/menus")
    @Operation(summary = "获得租户id对应的菜单集合")
    @Parameter(name = "tenantId", description = "租户id", required = true)
    CommonResult<List<MenuRespDTO>> tenantIdMenus(@RequestParam("tenantId") Long tenantId);

    /**
     * 获得id对应的菜单
     *
     * @param id
     * @return 不存在时反馈空列表
     */
    @GetMapping(PREFIX + "/loadByParentId/menus")
    @Operation(summary = "获得id对应的菜单id")
    @Parameter(name = "id", description = "菜单ID", required = true)
    CommonResult<List<Long>> loadIdsByParentId(@RequestParam("id") Long id);

    /**
     * 根据菜单ID获取菜单名称
     *
     * @param ids
     * @return
     */
    @GetMapping(PREFIX + "/menu/names")
    CommonResult<List<String>> getMenuNameById(@RequestParam("ids") Set<Long> ids);
}