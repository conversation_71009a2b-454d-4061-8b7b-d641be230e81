package cn.powerchina.bjy.link.dam.enums;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/10
 */
public class DamConstant {

    /**
     * 角色排序
     */
    public static final Integer PROJECT_ROLE_SORT = 50;
    /**
     * 仪器分量：中间值和成果值
     */
    public static final List<Integer> THING_TYPE = Arrays.asList(InstrumentThingTypeEnum.INTERVAL.getType(), InstrumentThingTypeEnum.RESULT.getType());

    /**
     * 拓扑结构中子设备名称显示
     */
    public static final String NET_TOPOLOGY_DEVICE = "[通道%s]%s/%s";

    /**
     * 相对测值：之前
     */
    public static final Integer RELATIVE_DATA_BEFORE = 1;

    /**
     * 导入类型：覆盖导入
     */
    public static final Integer IMPORT_TYPE_COVER = 2;

    /**
     * 设备电压的标识符：iot属性唯一标识
     */
    public static final String MAINBOARD_VOLTAGE = "mainboard_voltage";

    /**
     * 测点数据，系统自动审核
     */
    public static final String SYSTEM_NAME = "系统";
    public static final String SYSTEM_USER_ID = "-1";

    public static final String POINT_TASK_STATUS_KEY = "pointImportTask:%s";


}
