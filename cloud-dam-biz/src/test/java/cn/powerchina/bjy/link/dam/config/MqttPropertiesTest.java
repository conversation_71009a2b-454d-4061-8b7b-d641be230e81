package cn.powerchina.bjy.link.dam.config;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @Description: MQTT配置属性测试类
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class MqttPropertiesTest {

    @Autowired
    private MqttProperties mqttProperties;

    @Test
    public void testMqttPropertiesBinding() {
        log.info("MQTT配置属性测试开始...");
        
        // 测试基本配置
        log.info("Broker URL: {}", mqttProperties.getBroker().getUrl());
        log.info("Client ID: {}", mqttProperties.getClient().getId());
        log.info("Username: {}", mqttProperties.getUsername());
        log.info("Password: {}", mqttProperties.getPassword());
        
        // 测试连接配置
        log.info("Connection Timeout: {}", mqttProperties.getConnection().getTimeout());
        log.info("Keep Alive Interval: {}", mqttProperties.getKeep().getAlive().getInterval());
        log.info("Clean Session: {}", mqttProperties.getClean().isSession());
        log.info("Automatic Reconnect: {}", mqttProperties.getAutomatic().isReconnect());
        
        // 测试默认配置
        log.info("Default Topic: {}", mqttProperties.getDefaultConfig().getTopic());
        log.info("Default QoS: {}", mqttProperties.getDefaultConfig().getQos());
        log.info("Default Retained: {}", mqttProperties.getDefaultConfig().isRetained());
        log.info("Default Subscribe QoS: {}", mqttProperties.getDefaultConfig().getSubscribe().getQos());
        
        // 测试自动订阅配置
        log.info("Auto Subscribe Topics: {}", mqttProperties.getAuto().getSubscribe().getTopics());
        
        log.info("MQTT配置属性测试完成");
    }
}
