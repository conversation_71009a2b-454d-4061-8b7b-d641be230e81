package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点数据json Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointDataJsonReviewVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "406")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）", example = "1")
    private Integer dataType;

    private LocalDateTime pointTime;

    @NotNull(message = "审核状态不能为空")
    @Schema(description = "审核状态（0：未审核；1：审核通过；2：审核不通过）")
    private Integer reviewStatus;

    @NotNull(message = "审核人id不能为空")
    @Schema(description = "审核人id")
    private String reviewer;

    @NotNull(message = "审核人名称不能为空")
    @Schema(description = "审核人名称")
    @Length(max = 32)
    private String reviewName;

    @Schema(description = "审核人意见")
    @Length(max = 256)
    private String reviewOpinion;

}