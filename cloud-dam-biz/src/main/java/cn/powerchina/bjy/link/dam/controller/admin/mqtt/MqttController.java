package cn.powerchina.bjy.link.dam.controller.admin.mqtt;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.controller.admin.mqtt.vo.MqttPublishReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.mqtt.vo.MqttSubscribeReqVO;
import cn.powerchina.bjy.link.dam.service.mqtt.MqttService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description: MQTT管理控制器
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Tag(name = "管理后台 - MQTT管理")
@RestController
@RequestMapping("/admin/mqtt")
@Validated
@Slf4j
public class MqttController {

    @Autowired
    private MqttService mqttService;

    @PostMapping("/publish")
    @Operation(summary = "发布MQTT消息")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:publish')")
    public CommonResult<Boolean> publishMessage(@Valid @RequestBody MqttPublishReqVO reqVO) {
        try {
            mqttService.publishMessage(reqVO.getTopic(), reqVO.getMessage(),
                                     reqVO.getQos(), reqVO.getRetained());
            return success(true);
        } catch (Exception e) {
            log.error("发布MQTT消息失败", e);
            return CommonResult.error("发布消息失败: " + e.getMessage());
        }
    }

    @PostMapping("/publish/param")
    @Operation(summary = "发布MQTT消息（参数方式）")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:publish')")
    public CommonResult<Boolean> publishMessageByParam(
            @Parameter(description = "主题", required = true) @RequestParam @NotBlank String topic,
            @Parameter(description = "消息内容", required = true) @RequestParam @NotBlank String message,
            @Parameter(description = "QoS等级", required = false) @RequestParam(defaultValue = "1") Integer qos,
            @Parameter(description = "是否保留消息", required = false) @RequestParam(defaultValue = "false") Boolean retained) {

        try {
            mqttService.publishMessage(topic, message, qos, retained);
            return success(true);
        } catch (Exception e) {
            log.error("发布MQTT消息失败", e);
            return CommonResult.error("发布消息失败: " + e.getMessage());
        }
    }

    @PostMapping("/publish/simple")
    @Operation(summary = "发布MQTT消息（简化版）")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:publish')")
    public CommonResult<Boolean> publishSimpleMessage(
            @Parameter(description = "主题", required = true) @RequestParam @NotBlank String topic,
            @Parameter(description = "消息内容", required = true) @RequestParam @NotBlank String message) {
        
        try {
            mqttService.publishMessage(topic, message);
            return success(true);
        } catch (Exception e) {
            log.error("发布MQTT消息失败", e);
            return CommonResult.error("发布消息失败: " + e.getMessage());
        }
    }

    @PostMapping("/subscribe")
    @Operation(summary = "订阅MQTT主题")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:subscribe')")
    public CommonResult<Boolean> subscribe(@Valid @RequestBody MqttSubscribeReqVO reqVO) {
        try {
            mqttService.subscribe(reqVO.getTopic(), reqVO.getQos());
            return success(true);
        } catch (Exception e) {
            log.error("订阅MQTT主题失败", e);
            return CommonResult.error("订阅主题失败: " + e.getMessage());
        }
    }

    @PostMapping("/subscribe/param")
    @Operation(summary = "订阅MQTT主题（参数方式）")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:subscribe')")
    public CommonResult<Boolean> subscribeByParam(
            @Parameter(description = "主题", required = true) @RequestParam @NotBlank String topic,
            @Parameter(description = "QoS等级", required = false) @RequestParam(defaultValue = "1") Integer qos) {

        try {
            mqttService.subscribe(topic, qos);
            return success(true);
        } catch (Exception e) {
            log.error("订阅MQTT主题失败", e);
            return CommonResult.error("订阅主题失败: " + e.getMessage());
        }
    }

    @PostMapping("/subscribe/simple")
    @Operation(summary = "订阅MQTT主题（简化版）")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:subscribe')")
    public CommonResult<Boolean> subscribeSimple(
            @Parameter(description = "主题", required = true) @RequestParam @NotBlank String topic) {
        
        try {
            mqttService.subscribe(topic);
            return success(true);
        } catch (Exception e) {
            log.error("订阅MQTT主题失败", e);
            return CommonResult.error("订阅主题失败: " + e.getMessage());
        }
    }

    @PostMapping("/unsubscribe")
    @Operation(summary = "取消订阅MQTT主题")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:subscribe')")
    public CommonResult<Boolean> unsubscribe(
            @Parameter(description = "主题", required = true) @RequestParam @NotBlank String topic) {
        
        try {
            mqttService.unsubscribe(topic);
            return success(true);
        } catch (Exception e) {
            log.error("取消订阅MQTT主题失败", e);
            return CommonResult.error("取消订阅主题失败: " + e.getMessage());
        }
    }

    @GetMapping("/topics/available")
    @Operation(summary = "获取所有可用的传输目标主题")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:query')")
    public CommonResult<List<String>> getAvailableTopics() {
        try {
            List<String> topics = mqttService.getAvailableTopics();
            return success(topics);
        } catch (Exception e) {
            log.error("获取可用主题失败", e);
            return CommonResult.error("获取可用主题失败: " + e.getMessage());
        }
    }

    @PostMapping("/subscribe/all")
    @Operation(summary = "订阅所有可用的传输目标主题")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:subscribe')")
    public CommonResult<Boolean> subscribeToAllAvailableTopics() {
        try {
            mqttService.subscribeToAllAvailableTopics();
            return success(true);
        } catch (Exception e) {
            log.error("订阅所有可用主题失败", e);
            return CommonResult.error("订阅所有可用主题失败: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    @Operation(summary = "获取MQTT连接状态")
    @PreAuthorize("@ss.hasPermission('dam:mqtt:query')")
    public CommonResult<Boolean> getConnectionStatus() {
        try {
            boolean connected = mqttService.isConnected();
            return success(connected);
        } catch (Exception e) {
            log.error("获取MQTT连接状态失败", e);
            return CommonResult.error("获取连接状态失败: " + e.getMessage());
        }
    }
}
