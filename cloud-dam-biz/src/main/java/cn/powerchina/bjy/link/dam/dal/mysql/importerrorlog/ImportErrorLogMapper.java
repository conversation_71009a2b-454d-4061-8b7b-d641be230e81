package cn.powerchina.bjy.link.dam.dal.mysql.importerrorlog;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.link.dam.dal.dataobject.importerrorlog.ImportErrorLogDO;
import org.apache.ibatis.annotations.Mapper;
import cn.powerchina.bjy.link.dam.controller.admin.importerrorlog.vo.*;

/**
 * 错误日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ImportErrorLogMapper extends BaseMapperX<ImportErrorLogDO> {

    default PageResult<ImportErrorLogDO> selectPage(ImportErrorLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ImportErrorLogDO>()
                .eqIfPresent(ImportErrorLogDO::getPointId, reqVO.getPointId())
                .eqIfPresent(ImportErrorLogDO::getTaskId, reqVO.getTaskId())
                .likeIfPresent(ImportErrorLogDO::getFileName, reqVO.getFileName())
                .betweenIfPresent(ImportErrorLogDO::getExecuteTime, reqVO.getExecuteTime())
                .likeIfPresent(ImportErrorLogDO::getSheetName, reqVO.getSheetName())
                .eqIfPresent(ImportErrorLogDO::getErrorLine, reqVO.getErrorLine())
                .eqIfPresent(ImportErrorLogDO::getErrorRemark, reqVO.getErrorRemark())
                .betweenIfPresent(ImportErrorLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ImportErrorLogDO::getId));
    }

}