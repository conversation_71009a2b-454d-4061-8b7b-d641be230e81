package cn.powerchina.bjy.link.dam.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * @Description: MQTT配置调试器
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Component
@Slf4j
public class MqttConfigDebugger implements CommandLineRunner {

    @Autowired
    private Environment environment;

    @Autowired
    private MqttProperties mqttProperties;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== MQTT配置调试信息 ===");
        
        // 直接从Environment读取配置
        log.info("从Environment读取的配置:");
        log.info("mqtt.broker.url: {}", environment.getProperty("mqtt.broker.url"));
        log.info("mqtt.client.id: {}", environment.getProperty("mqtt.client.id"));
        log.info("mqtt.username: {}", environment.getProperty("mqtt.username"));
        log.info("mqtt.password: {}", environment.getProperty("mqtt.password"));
        log.info("mqtt.default.topic: {}", environment.getProperty("mqtt.default.topic"));
        log.info("mqtt.default.qos: {}", environment.getProperty("mqtt.default.qos"));
        log.info("mqtt.default.retained: {}", environment.getProperty("mqtt.default.retained"));
        log.info("mqtt.default.subscribe.qos: {}", environment.getProperty("mqtt.default.subscribe.qos"));
        log.info("mqtt.auto.subscribe.topics: {}", environment.getProperty("mqtt.auto.subscribe.topics"));
        
        // 从MqttProperties读取配置
        log.info("从MqttProperties读取的配置:");
        log.info("broker.url: {}", mqttProperties.getBroker().getUrl());
        log.info("client.id: {}", mqttProperties.getClient().getId());
        log.info("username: {}", mqttProperties.getUsername());
        log.info("password: {}", mqttProperties.getPassword());
        log.info("defaultConfig.topic: {}", mqttProperties.getDefaultConfig().getTopic());
        log.info("defaultConfig.qos: {}", mqttProperties.getDefaultConfig().getQos());
        log.info("defaultConfig.retained: {}", mqttProperties.getDefaultConfig().isRetained());
        log.info("defaultConfig.subscribe.qos: {}", mqttProperties.getDefaultConfig().getSubscribe().getQos());
        log.info("auto.subscribe.topics: {}", mqttProperties.getAuto().getSubscribe().getTopics());
        
        log.info("=== MQTT配置调试信息结束 ===");
    }
}
