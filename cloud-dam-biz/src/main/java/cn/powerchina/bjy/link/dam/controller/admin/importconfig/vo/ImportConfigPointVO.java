package cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class ImportConfigPointVO {
    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 导入信息id
     */
    private Long sheetConfigId;
    /**
     * 测点id
     */
    private Long pointId;

    /**
     * 仪器类型id
     */
    private Long instrumentId;

    private String pointName;
}
