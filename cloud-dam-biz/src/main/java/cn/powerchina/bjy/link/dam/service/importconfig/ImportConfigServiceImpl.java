package cn.powerchina.bjy.link.dam.service.importconfig;

import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.controller.admin.importfile.vo.ImportFileSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigPointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.instrumentmodel.InstrumentModelDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.mysql.importconfig.ImportConfigModelMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.importconfig.ImportConfigPointMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.instrumentmodel.InstrumentModelMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.point.PointMapper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.powerchina.bjy.link.dam.controller.admin.importconfig.vo.*;
import cn.powerchina.bjy.link.dam.dal.dataobject.importconfig.ImportConfigDO;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;

import cn.powerchina.bjy.link.dam.dal.mysql.importconfig.ImportConfigMapper;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.convertList;

/**
 * 导入配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ImportConfigServiceImpl implements ImportConfigService {

    @Resource
    private ImportConfigMapper importConfigMapper;

    @Resource
    private ImportConfigPointMapper importConfigPointMapper;

    @Resource
    private ImportConfigModelMapper importConfigModelMapper;

    @Resource
    private PointMapper pointMapper;

    @Resource
    private InstrumentModelMapper instrumentModelMapper;

    @Override
    public Long createImportConfig(ImportFileSaveReqVO createReqVO) {
        try {
            Workbook workbook = new XSSFWorkbook(createReqVO.getFile().getInputStream()); // 对于xlsx文件使用XSSFWorkbook，对于xls文件使用HSSFWorkbook
            List<ImportConfigDO> listSheets= new ArrayList<>();
            List<ImportConfigDO> existingConfigs = importConfigMapper.selectList(new LambdaQueryWrapperX<ImportConfigDO>()
                    .eq(ImportConfigDO::getImportId, createReqVO.getId()));
            Map<String, ImportConfigDO> configMap = new HashMap<>();
            if(existingConfigs!=null){
                for (ImportConfigDO config : existingConfigs) {
                    configMap.put(config.getSheetName(), config);
                }
            }
            // 获取所有工作表的名称
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                if(!configMap.isEmpty()){
                    if(configMap.get(sheet.getSheetName())==null){
                        ImportConfigDO importConfigDO = new ImportConfigDO();
                        importConfigDO.setImportId(createReqVO.getId());
                        importConfigDO.setSheetName(sheet.getSheetName() + "_" + String.valueOf(i + 1));
                        listSheets.add(importConfigDO);
                    }else{
                        configMap.remove(sheet.getSheetName());
                    }
                }else{
                    ImportConfigDO importConfigDO = new ImportConfigDO();
                    importConfigDO.setImportId(createReqVO.getId());
                    importConfigDO.setSheetName(sheet.getSheetName() + "_" + String.valueOf(i + 1));
                    listSheets.add(importConfigDO);
                }
            }
            if(!configMap.isEmpty()){
                configMap.forEach((key, value) -> {
                    importConfigMapper.deleteById(value.getId());
                });
            }
            if(!listSheets.isEmpty()){
                importConfigMapper.insertBatch( listSheets);
                List<ImportConfigPointDO> listPoints= new ArrayList<>();
                for(ImportConfigDO config:listSheets){
                    String sheetName = config.getSheetName().split("_")[0];
                    List<PointDO> pointDOList = pointMapper.selectList(new LambdaQueryWrapperX<PointDO>()
                        .eq(PointDO::getProjectId, createReqVO.getProjectId())
                        .eq(PointDO::getPointName, sheetName)
                    );
                    if (pointDOList!=null && !pointDOList.isEmpty()) {
                        for(PointDO pointDO:pointDOList){
                            ImportConfigPointDO configPoint = new ImportConfigPointDO();
                            configPoint.setSheetConfigId(config.getId());
                            configPoint.setPointId(pointDO.getId());
                            configPoint.setInstrumentId(pointDO.getInstrumentId());
                            listPoints.add(configPoint);
                        }
                    }
                }
                if(!listPoints.isEmpty()){
                    importConfigPointMapper.insertBatch(listPoints);
                    List<ImportConfigModelDO> listModel= new ArrayList<>();
                    for(ImportConfigPointDO config:listPoints){
                        ImportConfigModelDO model = new ImportConfigModelDO();
                        model.setPointConfigId(config.getId());
                        model.setIsAutomation(0);
                        model.setStartLine(1);
//                        importConfigDO.setInstrumentModelId(instrumentModelDO.getId());
                        model.setThingIdentity("日期");
                        listModel.add(model);
                        List<InstrumentModelDO> instrumentModelDOList = instrumentModelMapper.selectList(new LambdaQueryWrapperX<InstrumentModelDO>()
                                .eq(InstrumentModelDO::getInstrumentId, config.getInstrumentId())
                                .eq(InstrumentModelDO::getProjectId, createReqVO.getProjectId())
                        );
                        if(instrumentModelDOList!=null && !instrumentModelDOList.isEmpty()){
                            for(InstrumentModelDO instrumentModelDO:instrumentModelDOList){
                                ImportConfigModelDO importConfigDO = new ImportConfigModelDO();
                                importConfigDO.setPointConfigId(config.getId());
                                importConfigDO.setIsAutomation(0);
                                importConfigDO.setStartLine(1);
                                importConfigDO.setInstrumentModelId(instrumentModelDO.getId());
                                importConfigDO.setThingIdentity(instrumentModelDO.getThingIdentity());
                                listModel.add(importConfigDO);
                            }
                        }
                    }
                    importConfigModelMapper.insertBatch(listModel);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 1L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateImportConfig(ImportConfigModeSaveVo updateReqVO) {
        Integer isAutomation = updateReqVO.getIsAutomation();
        Integer startLine = updateReqVO.getStartLine();
        List<ImportConfigModelVO> modelList = updateReqVO.getModelList();
        for (ImportConfigModelVO modelDO : modelList) {
            modelDO.setIsAutomation(isAutomation);
            modelDO.setStartLine(startLine);
            ImportConfigModelDO importConfigModelDO = BeanUtils.toBean(modelDO, ImportConfigModelDO.class);
            // 更新
            importConfigModelMapper.updateById(importConfigModelDO);
        }
    }
    @Override
    public void deleteImportConfig(Long id) {
        // 校验存在
        validateImportConfigExists(id);
        // 删除
        importConfigPointMapper.deleteById(id);
        //  删除分量配置
        importConfigModelMapper.delete(new LambdaQueryWrapperX<ImportConfigModelDO>()
               .eq(ImportConfigModelDO::getPointConfigId, id)
        );
    }

    @Override
        public void deleteImportConfigListByIds(List<Long> ids) {
        // 删除
        importConfigMapper.deleteBatchIds(ids);
        }


    private void validateImportConfigExists(Long id) {
        if (importConfigMapper.selectById(id) == null) {
            throw exception(new ErrorCode(500, "测点不存在"));
        }
    }

    @Override
    public List<ImportConfigDO> getImportConfig(Long id) {
        List<ImportConfigDO> importConfigDOS = importConfigMapper.selectList(new LambdaQueryWrapperX<ImportConfigDO>()
                .eq(ImportConfigDO::getImportId, id));
        return importConfigDOS;
    }

    @Override
    public PageResult<ImportConfigDO> getImportConfigPage(ImportConfigPageReqVO pageReqVO) {
        return importConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ImportConfigPointVO> getWorkList(Long id) {
        List<ImportConfigPointDO> importConfigPointDOS = importConfigPointMapper.selectList(new LambdaQueryWrapperX<ImportConfigPointDO>()
                .eq(ImportConfigPointDO::getSheetConfigId, id)
        );
        List<ImportConfigPointVO> importConfigPointVOS = new ArrayList<>();
        for (ImportConfigPointDO importConfigPointDO : importConfigPointDOS) {
            PointDO pointDO = pointMapper.selectById(importConfigPointDO.getPointId());
            ImportConfigPointVO importConfigPointVO = BeanUtils.toBean(importConfigPointDO, ImportConfigPointVO.class);
            importConfigPointVO.setPointName(pointDO.getPointName());
            importConfigPointVOS.add(importConfigPointVO);
        };
        return importConfigPointVOS;
    }

    @Override
    public ImportConfigModeSaveVo getModelList(Long id) {
        ImportConfigModeSaveVo importConfigModeSaveVo = new ImportConfigModeSaveVo();
        List<ImportConfigModelDO> importConfigModelDOS = importConfigModelMapper.selectList(new LambdaQueryWrapperX<ImportConfigModelDO>()
                .eq(ImportConfigModelDO::getPointConfigId, id)
        );
        ArrayList<ImportConfigModelVO> importConfigModelVOS = new ArrayList<>();
        if(importConfigModelDOS.size()>0){
            importConfigModeSaveVo.setIsAutomation(importConfigModelDOS.get(0).getIsAutomation());
            importConfigModeSaveVo.setStartLine(importConfigModelDOS.get(0).getStartLine());
            for(ImportConfigModelDO importConfigModelDO:importConfigModelDOS){
                InstrumentModelDO instrumentModelDO = instrumentModelMapper.selectById(importConfigModelDO.getInstrumentModelId());
                ImportConfigModelVO importConfigModelVO = BeanUtils.toBean(instrumentModelDO, ImportConfigModelVO.class);
                importConfigModelVO.setInstrumentModelName(instrumentModelDO.getThingName());
                importConfigModelVOS.add(importConfigModelVO);
            }
        }
        importConfigModeSaveVo.setModelList(importConfigModelVOS);
        return importConfigModeSaveVo;
    }

}