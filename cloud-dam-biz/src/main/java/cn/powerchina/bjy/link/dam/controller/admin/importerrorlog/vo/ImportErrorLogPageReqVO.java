package cn.powerchina.bjy.link.dam.controller.admin.importerrorlog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 错误日志分页 Request VO")
@Data
public class ImportErrorLogPageReqVO extends PageParam {

    @Schema(description = "测点id", example = "7232")
    private Long pointId;

    @Schema(description = "任务id", example = "28558")
    private Long taskId;

    @Schema(description = "文件名称", example = "张三")
    private String fileName;

    @Schema(description = "任务执行时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] executeTime;

    @Schema(description = "工作表名称", example = "王五")
    private String sheetName;

    @Schema(description = "错误行")
    private String errorLine;

    @Schema(description = "错误信息", example = "随便")
    private String errorRemark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}