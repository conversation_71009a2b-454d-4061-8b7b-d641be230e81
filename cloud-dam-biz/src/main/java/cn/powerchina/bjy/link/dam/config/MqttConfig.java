package cn.powerchina.bjy.link.dam.config;

import lombok.Data;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import java.util.UUID;

/**
 * @Description: MQTT配置类
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Configuration
@ConfigurationProperties(prefix = "mqtt")
@Data
public class MqttConfig {

    private Broker broker = new Broker();
    private Client client = new Client();
    private String username;
    private String password;
    private Connection connection = new Connection();
    private Keep keep = new Keep();
    private Clean clean = new Clean();
    private Automatic automatic = new Automatic();
    private Default defaultConfig = new Default();
    private Auto auto = new Auto();

    @Data
    public static class Broker {
        private String url = "tcp://localhost:1883";
    }

    @Data
    public static class Client {
        private String id = "dam-client";
    }

    @Data
    public static class Connection {
        private int timeout = 10;
    }

    @Data
    public static class Keep {
        private Alive alive = new Alive();

        @Data
        public static class Alive {
            private int interval = 60;
        }
    }

    @Data
    public static class Clean {
        private boolean session = true;
    }

    @Data
    public static class Automatic {
        private boolean reconnect = true;
    }

    @Data
    public static class Default {
        private String topic = "dam/default";
        private int qos = 1;
        private boolean retained = false;
        private Subscribe subscribe = new Subscribe();

        @Data
        public static class Subscribe {
            private int qos = 1;
        }
    }

    @Data
    public static class Auto {
        private Subscribe subscribe = new Subscribe();

        @Data
        public static class Subscribe {
            private String topics;
        }
    }

    /**
     * MQTT客户端工厂
     */
    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        
        // 设置服务器地址
        options.setServerURIs(broker.getUrl().split(","));
        
        // 设置用户名和密码
        if (username != null && !username.trim().isEmpty()) {
            options.setUserName(username);
        }
        if (password != null && !password.trim().isEmpty()) {
            options.setPassword(password.toCharArray());
        }
        
        // 设置连接选项
        options.setCleanSession(clean.isSession());
        options.setAutomaticReconnect(automatic.isReconnect());
        options.setConnectionTimeout(connection.getTimeout());
        options.setKeepAliveInterval(keep.getAlive().getInterval());
        
        factory.setConnectionOptions(options);
        return factory;
    }

    /**
     * MQTT输入通道
     */
    @Bean
    public MessageChannel mqttInputChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT输出通道
     */
    @Bean
    public MessageChannel mqttOutboundChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT消息生产者（用于接收消息）
     */
    @Bean
    public MessageProducer inbound() {
        String clientId = client.getId() + "-inbound-" + UUID.randomUUID().toString();
        MqttPahoMessageDrivenChannelAdapter adapter =
                new MqttPahoMessageDrivenChannelAdapter(clientId, mqttClientFactory());
        adapter.setCompletionTimeout(5000);
        adapter.setConverter(new DefaultPahoMessageConverter());
        adapter.setQos(defaultConfig.getSubscribe().getQos());
        adapter.setOutputChannel(mqttInputChannel());
        return adapter;
    }

    /**
     * MQTT消息处理器（用于发送消息）
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttOutboundChannel")
    public MessageHandler mqttOutbound() {
        String clientId = client.getId() + "-outbound-" + UUID.randomUUID().toString();
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(clientId, mqttClientFactory());
        messageHandler.setAsync(true);
        messageHandler.setDefaultTopic(defaultConfig.getTopic());
        messageHandler.setDefaultQos(defaultConfig.getQos());
        messageHandler.setDefaultRetained(defaultConfig.isRetained());
        return messageHandler;
    }

    /**
     * 获取完整的客户端ID
     */
    public String getFullClientId() {
        return client.getId() + "-" + UUID.randomUUID().toString();
    }
}
