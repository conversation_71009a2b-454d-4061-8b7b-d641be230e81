package cn.powerchina.bjy.link.dam.controller.admin.importtask.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 导入任务分页 Request VO")
@Data
public class ImportTaskPageReqVO extends PageParam {

    @Schema(description = "任务id", example = "28192")
    private Long taskId;

    @Schema(description = "文件名称", example = "李四")
    private String fileName;

    @Schema(description = "最新上传时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] uploadTime;

    @Schema(description = "任务执行时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] executeTime;

    @Schema(description = "已录入测值数")
    private Long uploadNumber;

    @Schema(description = "应录入测值数")
    private Long totalNumber;

    @Schema(description = "执行状态", example = "1")
    private Integer taskStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}