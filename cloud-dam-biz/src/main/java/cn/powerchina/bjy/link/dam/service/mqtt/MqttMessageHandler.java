package cn.powerchina.bjy.link.dam.service.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

/**
 * @Description: MQTT消息处理器
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Component
@Slf4j
public class MqttMessageHandler {

    /**
     * 处理接收到的MQTT消息
     *
     * @param message 接收到的消息
     */
    @ServiceActivator(inputChannel = "mqttInputChannel")
    public void handleMessage(Message<?> message) {
        try {
            String topic = (String) message.getHeaders().get("mqtt_receivedTopic");
            String payload = message.getPayload().toString();
            
            log.info("收到MQTT消息 - Topic: {}, Payload: {}", topic, payload);
            
            // 根据不同的topic处理不同的消息
            processMessageByTopic(topic, payload);
            
        } catch (Exception e) {
            log.error("处理MQTT消息时发生错误", e);
        }
    }

    /**
     * 根据主题处理消息
     *
     * @param topic   主题
     * @param payload 消息内容
     */
    private void processMessageByTopic(String topic, String payload) {
        if (topic == null) {
            log.warn("接收到空主题的消息: {}", payload);
            return;
        }

        // 根据主题前缀进行分类处理
        if (topic.startsWith("dam/data")) {
            handleDataMessage(topic, payload);
        } else if (topic.startsWith("dam/alarm")) {
            handleAlarmMessage(topic, payload);
        } else if (topic.startsWith("dam/command")) {
            handleCommandMessage(topic, payload);
        } else if (topic.startsWith("dam/status")) {
            handleStatusMessage(topic, payload);
        } else {
            handleDefaultMessage(topic, payload);
        }
    }

    /**
     * 处理数据消息
     */
    private void handleDataMessage(String topic, String payload) {
        log.info("处理数据消息 - Topic: {}, Data: {}", topic, payload);
        // TODO: 实现具体的数据处理逻辑
        // 例如：解析数据、存储到数据库、触发业务逻辑等
    }

    /**
     * 处理告警消息
     */
    private void handleAlarmMessage(String topic, String payload) {
        log.info("处理告警消息 - Topic: {}, Alarm: {}", topic, payload);
        // TODO: 实现具体的告警处理逻辑
        // 例如：解析告警信息、发送通知、记录告警日志等
    }

    /**
     * 处理命令消息
     */
    private void handleCommandMessage(String topic, String payload) {
        log.info("处理命令消息 - Topic: {}, Command: {}", topic, payload);
        // TODO: 实现具体的命令处理逻辑
        // 例如：解析命令、执行相应操作、返回执行结果等
    }

    /**
     * 处理状态消息
     */
    private void handleStatusMessage(String topic, String payload) {
        log.info("处理状态消息 - Topic: {}, Status: {}", topic, payload);
        // TODO: 实现具体的状态处理逻辑
        // 例如：更新设备状态、记录状态变化等
    }

    /**
     * 处理默认消息
     */
    private void handleDefaultMessage(String topic, String payload) {
        log.info("处理默认消息 - Topic: {}, Message: {}", topic, payload);
        // TODO: 实现默认的消息处理逻辑
    }
}
