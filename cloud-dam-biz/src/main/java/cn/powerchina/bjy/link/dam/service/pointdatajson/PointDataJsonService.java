package cn.powerchina.bjy.link.dam.service.pointdatajson;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo.PointDataReqVO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.bo.PointDataInstrumentBO;
import cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.pointparam.vo.PointParamTableRespVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdata.PointDataDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO;
import jakarta.validation.Valid;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 测点数据json Service 接口
 *
 * <AUTHOR>
 */
public interface PointDataJsonService {

    /**
     * 创建测点数据json
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointDataJson(@Valid PointDataJsonSaveReqVO createReqVO);

    /**
     * 批量导入数据
     *
     * @param createReqVOList
     * @return
     */
    void importPointDataJson(@Valid List<PointDataJsonSaveReqVO> createReqVOList);

    /**
     * 更新测点数据json
     *
     * @param updateReqVO 更新信息
     */
    void updatePointDataJson(@Valid PointDataJsonSaveReqVO updateReqVO);

    void updatePointDataJson(@Valid PointDataJsonReviewVO updateReqVO);

    /**
     * 删除测点数据json
     *
     * @param id 编号
     */
    void deletePointDataJson(PointDataJsonSaveReqVO updateReqVO);

    /**
     * 获得测点数据json
     *
     * @param id 编号
     * @return 测点数据json
     */
    PointDataJsonRespVO getPointDataJson(PointDataJsonSaveReqVO updateReqVO);

    /**
     * 获得测点数据json分页
     *
     * @param pageReqVO 分页查询
     * @return 测点数据json分页
     */
    PageResult<PointDataJsonRespVO> getPointDataJsonPage(PointDataJsonPageReqVO pageReqVO);

    /**
     * 测点数据统计
     *
     * @param projectId
     * @param dataType
     * @param startTime
     * @return
     */
    Long countPointDataJsonByProjectIdAndDataType(Long projectId, Integer dataType, Date startTime);

    /**
     * 查找仪器类型相关数据
     *
     * @param projectId
     * @param instrumentIdList
     * @return
     */
    List<PointDataInstrumentBO> findPointDataInstrumentBOList(Long projectId, List<Long> instrumentIdList);

    PointDataInstrumentBO findPointDataInstrumentBO(Long projectId, Long instrumentId);

    /**
     * 重新计算
     *
     * @param id
     */
    void recalculate(PointDataJsonSaveReqVO createReqVO);

    /**
     * 批量计算
     * @param pageReqVO
     * @return
     */
    String batchCalculate(PointDataJsonPageReqVO pageReqVO);

    /**
     * 根据测点id查询人工录入的基本信息和分量列表
     *
     * @param pointId
     * @return
     */
    PointInformationVO getInformationByPointId(Long pointId);

    /**
     * 计算人工录入数据的成果值
     *
     * @param createReqVO
     * @return
     */
    List<PointInstrumentModelJsonVO> calculatePointFormula(PointDataJsonSaveReqVO createReqVO);

    /**
     * 人工导入计算成果值
     *
     * @param createReqVO
     * @return
     */
    List<PointDataDO> importCalculatePointFormula(PointDataJsonSaveReqVO createReqVO);

    /**
     * 删除时间段内的数据
     *
     * @param pointId
     * @param startTime
     * @param endTime
     */
    void updateByPointIdAndTime(Long pointId, LocalDateTime startTime, LocalDateTime endTime, Integer dataType, boolean delete, Long importId);

    /**
     * 查询测点最新的数据
     *
     * @param pointId
     * @return
     */
    PointDataJsonDO getLastPointDataJson(Long projectId,Long instrumentId ,Long pointId, Integer dataType);

    PointDataJsonReviewVO getReviewPointDataJson( PointDataJsonSaveReqVO createReqVO);

    void setPointJsonStatus(List<PointDataDO> pointDataDOList, PointDataJsonDO pointDataJson);

    /**
     * 根据测点编号，查询唯一的测点
     *
     * @param projectId
     * @param pointCode
     * @return
     */
    PointDO getPointByProjectIdAndPointCode(Long projectId, String pointCode);

    /**
     * 只查询部分字段
     *
     * @param pointDataReqVO
     * @return
     */
    List<PointDataJsonDO> getPointDataJsonList(PointDataReqVO pointDataReqVO);

    /**
     * 查询导出数据，并按照表头顺序排列
     * @param pageReqVO
     * @return
     */
    List<List<String>> getDataList(PointDataJsonPageReqVO pageReqVO);

    void insertPointData(PointDataJsonSaveReqVO createReqVO);

    void delByPointTime( String projectId,String instrumentId,
                          String pointId,
                         Integer dataType,
                         String startPointTime,
                        String endPointTime);

}