package cn.powerchina.bjy.link.dam.service.device;

import cn.hutool.core.collection.CollUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils;
import cn.powerchina.bjy.cloud.framework.common.util.ext.SafeConverter;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.dam.config.DamConfig;
import cn.powerchina.bjy.link.dam.controller.admin.device.bo.DeviceBO;
import cn.powerchina.bjy.link.dam.controller.admin.device.vo.*;
import cn.powerchina.bjy.link.dam.controller.admin.devicecollectlog.vo.DeviceCollectLogSaveReqVO;
import cn.powerchina.bjy.link.dam.dal.dataobject.authproduct.AuthProductDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.devicestrategy.DeviceStrategyDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.point.PointDO;
import cn.powerchina.bjy.link.dam.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.link.dam.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.dam.dal.mysql.devicestrategy.DeviceStrategyMapper;
import cn.powerchina.bjy.link.dam.enums.BindTypeEnum;
import cn.powerchina.bjy.link.dam.enums.DeviceLinkState;
import cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.dam.enums.NodeTypeEnum;
import cn.powerchina.bjy.link.dam.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.dam.service.authproduct.AuthProductService;
import cn.powerchina.bjy.link.dam.service.devicecollectlog.DeviceCollectLogService;
import cn.powerchina.bjy.link.dam.service.devicestrategy.DeviceStrategyService;
import cn.powerchina.bjy.link.dam.service.point.PointService;
import cn.powerchina.bjy.link.dam.service.project.ProjectService;
import cn.powerchina.bjy.link.dam.service.projectcategory.ProjectCategoryService;
import cn.powerchina.bjy.link.dam.util.SnowFlakeUtil;
import cn.powerchina.bjy.link.iot.api.device.DeviceApi;
import cn.powerchina.bjy.link.iot.api.devicegroup.DeviceGroupApi;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceGroupRespDTO;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceRespDTO;
import cn.powerchina.bjy.link.iot.api.product.ProductApi;
import cn.powerchina.bjy.link.iot.api.product.dto.ProductRespDTO;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.model.DeviceCheckOnlineModel;
import cn.powerchina.bjy.link.iot.model.DeviceCommandModel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.DEVICE_FORBID_DELETED;
import static cn.powerchina.bjy.link.dam.enums.ErrorCodeConstants.DEVICE_NOT_EXISTS;

/**
 * 大坝设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DeviceServiceImpl implements DeviceService {

    @Resource
    private DeviceMapper deviceMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private AuthProductService authProductService;

    @Resource
    private DeviceGroupApi deviceGroupApi;
    @Resource
    private ProductApi productApi;
    @Resource
    private DeviceApi deviceApi;
    @Autowired
    @Lazy
    private DeviceStrategyService deviceStrategyService;

    @Autowired
    private ProjectCategoryService projectCategoryService;

    @Autowired
    private PointService pointService;

    @Autowired
    private DeviceCollectLogService deviceCollectLogService;


    @Resource
    private RocketMQv5Client rocketMQv5Client;

    @Autowired
    private DeviceStrategyMapper deviceStrategyMapper;

    @Resource
    private SnowFlakeUtil snowFlakeUtil;
    @Autowired
    private DamConfig damConfig;

    @Override
    public Long createDevice(DeviceSaveReqVO createReqVO) {
        // 插入
        DeviceDO device = BeanUtils.toBean(createReqVO, DeviceDO.class);
        deviceMapper.insert(device);
        // 返回
        return device.getId();
    }

    @Override
    @Transactional
    public void updateDevice(DeviceSaveReqVO updateReqVO) {
        // 校验存在
        DeviceDO deviceDO = validateDeviceExists(updateReqVO.getId());
//        //校验测站是否存在
//        ProjectCategoryDO projectCategoryDO = projectCategoryService.validateProjectCategoryExists(updateReqVO.getStationId());
//        if (!Objects.equals(projectCategoryDO.getCategoryType(), CategoryTypeEnum.STATION.getType())) {
//            throw exception(ErrorCodeConstants.POINT_SELECT_STATION_ERROR);
//        }
        //校验采集策略是否存在
        if (Objects.isNull(updateReqVO.getStrategyId())) {
            DeviceStrategyDO creat = BeanUtils.toBean(updateReqVO, DeviceStrategyDO.class);
            creat.setId(snowFlakeUtil.snowflakeId());
            deviceStrategyMapper.insert(creat);
            // 更新
            DeviceDO updateObj = BeanUtils.toBean(updateReqVO, DeviceDO.class);
            updateObj.setStrategyId(creat.getId());
            //采集策略变更，那么修改采集仪执行计划
            if (Objects.isNull(deviceDO.getStrategyId()) || !Objects.equals(deviceDO.getStrategyId(), updateReqVO.getStrategyId())) {
                deviceCollectLogService.createDeviceCollectLog(DeviceCollectLogSaveReqVO.builder().projectId(deviceDO.getProjectId()).deviceCode(deviceDO.getDeviceCode()).strategyId(updateObj.getStrategyId()).build());
            }
            deviceMapper.updateById(updateObj);
        } else {
            DeviceStrategyDO deviceStrategyDO = deviceStrategyService.validateDeviceStrategyExists(updateReqVO.getStrategyId());
            // 更新
            DeviceStrategyDO update = BeanUtils.toBean(updateReqVO, DeviceStrategyDO.class);
            update.setId(updateReqVO.getStrategyId());
            deviceStrategyMapper.updateById(update);
            // 更新
            DeviceDO updateObj = BeanUtils.toBean(updateReqVO, DeviceDO.class);
            //采集策略变更，那么修改采集仪执行计划
            if (!Objects.equals(deviceStrategyDO.getTimeInterval(), updateReqVO.getTimeInterval())) {
                deviceCollectLogService.createDeviceCollectLog(DeviceCollectLogSaveReqVO.builder().projectId(deviceDO.getProjectId()).deviceCode(deviceDO.getDeviceCode()).strategyId(updateReqVO.getStrategyId()).build());
            }
            deviceMapper.updateById(updateObj);
        }
    }

    @Override
    public void deleteDevice(Long id) {
        // 校验存在
        validateDeviceExists(id);
        // 删除
        deviceMapper.deleteById(id);
    }

    private DeviceDO validateDeviceExists(Long id) {
        DeviceDO deviceDO = deviceMapper.selectById(id);
        if (Objects.isNull(deviceDO)) {
            throw exception(DEVICE_NOT_EXISTS);
        }
        return deviceDO;
    }

    @Override
    public DeviceDO getDevice(Long id) {
        return deviceMapper.selectById(id);
    }

    @Override
    public DeviceBO getDeviceBO(Long id) {
        DeviceDO deviceDO = getDevice(id);
        if (Objects.nonNull(deviceDO)) {
            DeviceBO deviceBO = new DeviceBO();
            org.springframework.beans.BeanUtils.copyProperties(deviceDO, deviceBO);
            //设置策略信息
            if (Objects.nonNull(deviceBO.getStrategyId())) {
                DeviceStrategyDO strategyDO = deviceStrategyService.getDeviceStrategy(deviceBO.getStrategyId());
                if (Objects.nonNull(strategyDO)) {
                    deviceBO.setStrategyName(strategyDO.getStrategyName());
                    deviceBO.setStrategyType(strategyDO.getStrategyType());
                    deviceBO.setTimeInterval(strategyDO.getTimeInterval());
                    deviceBO.setTimePoint(strategyDO.getTimePoint());
                }
            }
            return deviceBO;
        }
        return null;
    }

    @Override
    public PageResult<DeviceBO> getDevicePage(DevicePageReqVO pageReqVO) {
        return deviceMapper.selectDevicePage(pageReqVO);
    }

    @Override
    public PageResult<DeviceBO> getDeviceBOPage(DevicePageReqVO pageReqVO) {
        PageResult<DeviceBO> pageResult = getDevicePage(pageReqVO);
        PageResult<DeviceBO> boPageResult = new PageResult<>(pageResult.getTotal());
        if (CollectionUtils.isEmpty(pageResult.getList())) {
            return pageResult;
        }

        boPageResult.setList(pageResult.getList().stream().map(item -> {
            DeviceBO deviceBO = new DeviceBO();
            org.springframework.beans.BeanUtils.copyProperties(item, deviceBO);

            // 设置最后更新时间
            List<LocalDateTime> receiveTimeList = new ArrayList<>();
            if (Objects.nonNull(deviceBO.getTemperatureTime())) {
                receiveTimeList.add(deviceBO.getTemperatureTime());
            }
            if (Objects.nonNull(deviceBO.getClockTime())) {
                receiveTimeList.add(deviceBO.getClockTime());
            }
            if (Objects.nonNull(deviceBO.getVoltageTime())) {
                receiveTimeList.add(deviceBO.getVoltageTime());
            }
            LocalDateTime lastReceiveTime = receiveTimeList.stream().max(LocalDateTime::compareTo).orElse(null);
            deviceBO.setLastReceiveTime(lastReceiveTime);

            //设置父节点产品名称
            if (StringUtils.isNotBlank(item.getParentProductCode())) {
                AuthProductDO productDO = authProductService.findProductByProjectIdProductCode(item.getProjectId(), item.getParentProductCode());
                deviceBO.setParentProductName(Objects.nonNull(productDO) ? productDO.getProductName() : null);
            }
            //设置项目名称
            ProjectDO projectDO = projectService.getProject(item.getProjectId());
            deviceBO.setProjectName(Objects.nonNull(projectDO) ? projectDO.getProjectName() : null);
            //设置产品名称
            AuthProductDO productDO = authProductService.findProductByProjectIdProductCode(item.getProjectId(), item.getProductCode());
            deviceBO.setProductName(Objects.nonNull(productDO) ? productDO.getProductName() : null);
            //设置所属测站全路径名称
            if (Objects.nonNull(item.getStationId())) {
                deviceBO.setStationName(projectCategoryService.getAllPathCategoryName(item.getStationId(), "--"));
            }
            //设置策略信息
            if (Objects.nonNull(item.getStrategyId())) {
                DeviceStrategyDO strategyDO = deviceStrategyService.getDeviceStrategy(item.getStrategyId());
                if (Objects.nonNull(strategyDO)) {
                    deviceBO.setStrategyName(strategyDO.getStrategyName());
                    deviceBO.setStrategyType(strategyDO.getStrategyType());
                    deviceBO.setTimeInterval(strategyDO.getTimeInterval());
                    deviceBO.setTimePoint(strategyDO.getTimePoint());
                }
            }
            //设置测点名称
            if (Objects.nonNull(item.getPointId())) {
                PointDO pointDO = pointService.getPoint(item.getPointId());
                if (Objects.nonNull(pointDO)) {
                    deviceBO.setPointCode(pointDO.getPointCode());
                    deviceBO.setPointName(pointDO.getPointName());
                }
            }
            return deviceBO;
        }).collect(Collectors.toList()));
        return boPageResult;
    }

    /**
     * 1、筛选出新增的和删减的设备
     * 2、新增的设备直接插入
     * 3、删减的设备设备列表删除+测点解绑
     *
     * @param projectId
     * @param deviceGroupIds
     * @return
     */
    @Override
    @Transactional
    public List<String> addDeviceByDeviceGroupIds(Long projectId, List<Long> deviceGroupIds) {
        //查询当前项目已经存在的设备
        List<DeviceDO> deviceDbList = Optional.ofNullable(deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getProjectId, projectId))).orElse(new ArrayList<>());
        //当前项目已经存在的设备编码
        List<String> deviceDbCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(deviceDbList)) {
            deviceDbCodeList.addAll(deviceDbList.stream().map(DeviceDO::getDeviceCode).toList());
        }
        //需要新增的设备列表
        List<DeviceDO> deviceAddList = new ArrayList<>();
        //分组里rpc过来的所有设备
        List<String> deviceCodeList = new ArrayList<>();
        //设备对应的产品编码
        Set<String> productCodeSet = new HashSet<>();
        //遍历设备分组，查找分组下的设备
        for (Long deviceGroupId : deviceGroupIds) {
            CommonResult<List<DeviceRespDTO>> deviceResult = deviceGroupApi.getDeviceListByDeviceGroupId(deviceGroupId);
            if (Objects.nonNull(deviceResult) && !CollectionUtils.isEmpty(deviceResult.getData())) {
                for (DeviceRespDTO respDTO : deviceResult.getData()) {
                    productCodeSet.add(respDTO.getProductCode());
                    DeviceDO deviceDO = new DeviceDO();
                    deviceDO.setProjectId(projectId);
                    deviceDO.setBindType(BindTypeEnum.NO.getType());
                    org.springframework.beans.BeanUtils.copyProperties(respDTO, deviceDO, "id", "createTime", "creator", "updateTime", "updater");
                    //数据库里不包含就是新增的
                    if (!deviceDbCodeList.contains(deviceDO.getDeviceCode()) && !deviceCodeList.contains(deviceDO.getDeviceCode())) {
                        deviceAddList.add(deviceDO);
                    }
                    deviceCodeList.add(deviceDO.getDeviceCode());
                }
            }
        }
        //当前项目已经存在的设备-减去-分组里rpc过来的设备，就是当前项目数据库里需要减去的设备
        List<DeviceDO> deviceRemoveList = deviceDbList.stream().filter(item -> !deviceCodeList.contains(item.getDeviceCode())).toList();
        //插入新增的设备
        if (!CollectionUtils.isEmpty(deviceAddList)) {
            deviceMapper.insertBatch(deviceAddList);
        }
        //删除删减的设备
        if (!CollectionUtils.isEmpty(deviceRemoveList)) {
            deviceMapper.deleteBatchIds(deviceRemoveList.stream().map(DeviceDO::getId).toList());
            //移出的设备需要通知测点解绑
            List<Long> pointIdList = deviceRemoveList.stream().filter(item -> Objects.equals(item.getBindType(), BindTypeEnum.YES.getType())).map(DeviceDO::getPointId).toList();
            if (!CollectionUtils.isEmpty(pointIdList)) {
                pointService.batchPointBindDevice(pointIdList, BindTypeEnum.NO.getType());
            }
        }
        return productCodeSet.stream().toList();
    }

    @Override
    public Long countDeviceByStrategyId(Long strategyId) {
        Long strategyCount = deviceMapper.selectCount(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getStrategyId, strategyId));
        return Objects.isNull(strategyCount) ? 0L : strategyCount;
    }

    @Override
    public Long countDeviceByStationId(Long stationId) {
        Long stationCount = deviceMapper.selectCount(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getStationId, stationId));
        return Objects.isNull(stationCount) ? 0L : stationCount;
    }

    @Override
    public DeviceDO getDeviceDOByDeviceCode(String deviceCode) {
        return deviceMapper.selectOne(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getDeviceCode, deviceCode).last("limit 1"));
    }

    @Override
    public DeviceDO getDeviceDOByProjectIdAndDeviceCode(Long projectId, String deviceCode) {
        return deviceMapper.selectOne(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getProjectId, projectId).eq(DeviceDO::getDeviceCode, deviceCode));
    }

    @Override
    @Transactional
    public void deviceBindPoint(DeviceBindReqVO reqVO) {
        //校验设备存在
        DeviceDO deviceDO = getDevice(reqVO.getId());
        if (Objects.isNull(deviceDO)) {
            deviceDO = getDeviceDOByProjectIdAndDeviceCode(reqVO.getProjectId(), reqVO.getDeviceCode());
        }
        if (Objects.isNull(deviceDO)) {
            throw exception(ErrorCodeConstants.DEVICE_NOT_EXISTS);
        }

        //解绑
        if (Objects.equals(reqVO.getBindType(), BindTypeEnum.NO.getType())) {
            Long pointId = deviceDO.getPointId();
            if (Objects.nonNull(pointId)) {
                deviceDO.setPointId(null);
            }
            deviceDO.setBindType(BindTypeEnum.NO.getType());
            deviceMapper.updateById(deviceDO);
            //通知测点解绑
            pointService.pointBindDevice(pointId, BindTypeEnum.NO.getType());
            return;
        }

        //进行绑定，看当前设备是否已绑定
        if (Objects.nonNull(deviceDO.getPointId()) || Objects.equals(deviceDO.getBindType(), BindTypeEnum.YES.getType())) {
            throw exception(ErrorCodeConstants.AUTH_DEVICE_POINT_EXISTS);
        }
        //看测点是否存在
        PointDO pointDO = pointService.validatePointExists(reqVO.getPointId());
        deviceDO.setPointId(pointDO.getId());
        deviceDO.setBindType(BindTypeEnum.YES.getType());
        deviceMapper.updateById(deviceDO);
        //通知测点绑定
        pointService.pointBindDevice(pointDO.getId(), BindTypeEnum.YES.getType());
    }

    @Override
    public List<DeviceDO> getDeviceDOListByProjectIdAndStationId(Long projectId, Long stationId) {
        return deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getProjectId, projectId).eq(DeviceDO::getStationId, stationId));
    }

    @Override
    public List<DeviceDO> getDeviceDOListByProjectIdAndParentCode(Long projectId, String parentCode) {
        return deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getProjectId, projectId).eq(DeviceDO::getParentCode, parentCode));
    }

    @Override
    public DeviceDO getDeviceDOByDeviceCodeAndMcuChannel(String deviceCode, String mcuChannel) {
        return deviceMapper.selectOne(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getDeviceCode, deviceCode).eqIfPresent(DeviceDO::getMcuChannel, mcuChannel).isNotNull(DeviceDO::getPointId).last("limit 1"));
    }

    @Override
    public void deviceDataCollect(DeviceCollectReqVO reqVO) {
        DeviceDO deviceDO = getDeviceDOByProjectIdAndDeviceCode(reqVO.getProjectId(), reqVO.getDeviceCode());
        if (Objects.isNull(deviceDO)) {
            throw exception(ErrorCodeConstants.DEVICE_NOT_EXISTS);
        }
        if (DeviceLinkState.OFFLINE.getType().equals(deviceDO.getLinkState())) {
            throw exception(ErrorCodeConstants.DEVICE_OFFLINE);
        }
        DeviceCommandModel deviceCommandModel = new DeviceCommandModel();
        deviceCommandModel.setDeviceCode(reqVO.getDeviceCode());
        deviceCommandModel.setMcuChannelList(Objects.equals(deviceDO.getNodeType(), NodeTypeEnum.EDGE_SUB.getType()) ? List.of(deviceDO.getMcuChannel()) : null);
        rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DAM_COLLECT_COMMAND,  deviceCommandModel,IotTopicConstant.GROUP_DAM_COLLECT_COMMAND);
    }

    @Override
    public Long countDeviceByProjectIdAndNodeType(Long projectId, Integer nodeType, Date startTime) {
        Long count = deviceMapper.selectCount(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getProjectId, projectId).eq(DeviceDO::getNodeType, nodeType).geIfPresent(DeviceDO::getCreateTime, startTime));
        return Objects.nonNull(count) ? count : 0L;
    }

    @Override
    public List<DeviceDO> getDeviceByStrategyId(Long strategyId) {
        return deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getStrategyId, strategyId));
    }

    @Override
    public void updateDeviceCheckOnline(DeviceCheckOnlineModel deviceCheckOnlineModel) {
        // 更新
        List<DeviceDO> deviceDOList = deviceMapper.selectList(new LambdaQueryWrapper<DeviceDO>().eq(DeviceDO::getDeviceCode, deviceCheckOnlineModel.getDeviceCode()));
        if (CollUtil.isNotEmpty(deviceDOList)) {
            deviceDOList.forEach(device -> device.setLinkState(deviceCheckOnlineModel.getCheckResult()));
            if (deviceCheckOnlineModel.getCheckResult().equals(DeviceLinkState.ONLINE.getType())) {
                deviceDOList.forEach(device -> device.setLastUpTime(LocalDateTime.now()));
            }
            if (CollUtil.isNotEmpty(deviceCheckOnlineModel.getChildDeviceStatus())) {
                deviceCheckOnlineModel.getChildDeviceStatus().forEach(item -> {
                    List<DeviceDO> deviceListDO = deviceMapper.selectList(new LambdaQueryWrapper<DeviceDO>().eq(DeviceDO::getDeviceCode, item.getDeviceCode()));
                    deviceListDO.forEach(device -> device.setLinkState(deviceCheckOnlineModel.getCheckResult()));
                    if (item.getCheckResult().equals(DeviceLinkState.ONLINE.getType())) {
                        deviceListDO.forEach(device -> device.setLastUpTime(LocalDateTime.now()));
                    }
                    deviceDOList.addAll(deviceListDO);
                });
            }
            deviceMapper.updateBatch(deviceDOList);
        }
    }

    @Override
    public void updateDeviceVoltage(String deviceCode, String voltage) {
        List<DeviceDO> deviceDOList = deviceMapper.selectList(new LambdaQueryWrapper<DeviceDO>().eq(DeviceDO::getDeviceCode, deviceCode));
        if (!CollectionUtils.isEmpty(deviceDOList)) {
            deviceDOList.forEach(item -> {
                item.setDeviceVoltage(voltage);
                item.setVoltageTime(LocalDateTime.now());
                item.setUpdateTime(LocalDateTime.now());
            });
            deviceMapper.updateBatch(deviceDOList);
        }
    }

    @Override
    public void updateDeviceTemperature(String deviceCode, String temperature) {
        List<DeviceDO> deviceDOList = deviceMapper.selectList(new LambdaQueryWrapper<DeviceDO>().eq(DeviceDO::getDeviceCode, deviceCode));
        if (!CollectionUtils.isEmpty(deviceDOList)) {
            deviceDOList.forEach(item -> {
                item.setDeviceTemperature(temperature);
                item.setTemperatureTime(LocalDateTime.now());
                item.setUpdateTime(LocalDateTime.now());
            });
            deviceMapper.updateBatch(deviceDOList);
        }
    }

    @Override
    public PageResult<DeviceIotRespVO> spaceNoBindDevices(DeviceIotPageReqVO iotPageReqVO) {
        List<DeviceGroupRespDTO> groupRespDTOS = deviceGroupApi.getDeviceGroupListByResourceSpaceId(damConfig.getResourceSpaceId()).getCheckedData();
        List<Long> groupIds = groupRespDTOS.stream().map(DeviceGroupRespDTO::getId).toList();
        Collection<DeviceRespDTO> deviceIot = deviceGroupApi.batchGetDeviceListByDeviceGroupIds(groupIds).getCheckedData().values().stream().flatMap(Collection::stream).sorted(Comparator.comparing(DeviceRespDTO::getId,Comparator.nullsLast(Long::compareTo)).reversed()).collect(Collectors.toMap(DeviceRespDTO::getId, Function.identity(), (o1, o2) -> o2)).values();

        List<String> bindDeviceCodes = deviceMapper.bindDeviceCodes();
        List<DeviceRespDTO> respDTOS = deviceIot.stream().filter(o -> !bindDeviceCodes.contains(o.getDeviceCode())) //未绑定的设备
                .filter(o -> NodeTypeEnum.EDGE.getType().equals(o.getNodeType()))//只添加网关
                .filter(o -> StringUtil.isEmpty(iotPageReqVO.getDeviceName()) || o.getDeviceName().contains(iotPageReqVO.getDeviceName()))
                .filter(o -> StringUtil.isEmpty(iotPageReqVO.getDeviceCode()) || o.getDeviceCode().contains(iotPageReqVO.getDeviceCode()))
                .filter(o -> StringUtil.isEmpty(iotPageReqVO.getProductCode()) || o.getProductCode().contains(iotPageReqVO.getProductCode()))
                .filter(o -> StringUtil.isEmpty(iotPageReqVO.getDeviceSerial()) || o.getDeviceSerial().contains(iotPageReqVO.getDeviceSerial()))
                .skip((long) (iotPageReqVO.getPageNo() - 1) * iotPageReqVO.getPageSize())
                .limit(iotPageReqVO.getPageSize()).toList();

        List<DeviceIotRespVO> deviceIotRespVOS = new ArrayList<>();
        List<String> productCodeList = respDTOS.stream().map(DeviceRespDTO::getProductCode).collect(Collectors.toList());
        Map<String, ProductRespDTO> productMap = productApi.batchGetProductByProductCodes(productCodeList).getCheckedData();
        respDTOS.forEach(o -> {
            DeviceIotRespVO respVO = BeanUtils.toBean(o, DeviceIotRespVO.class);
            ProductRespDTO product = productMap.get(o.getProductCode());
            respVO.setProductName(SafeConverter.fetchField(product, ProductRespDTO::getProductName));
            respVO.setDescription(SafeConverter.fetchField(product, ProductRespDTO::getDescription));

            deviceIotRespVOS.add(respVO);
        });

        PageResult<DeviceIotRespVO> pageResult = new PageResult<>(deviceIotRespVOS, (long) respDTOS.size());
        return pageResult;
    }

    @Override
    public DeviceDetailResVO getDeviceDetail(Long id) {
        DeviceDO deviceDO = deviceMapper.selectById(id);
        AuthProductDO productDO = authProductService.findProductByProjectIdProductCode(deviceDO.getProjectId(), deviceDO.getProductCode());

        DeviceBaseInfoVO deviceBaseInfoVO = new DeviceBaseInfoVO();
        deviceBaseInfoVO.setProjectId(productDO.getProjectId());
        deviceBaseInfoVO.setDeviceName(deviceDO.getDeviceName());
        deviceBaseInfoVO.setDeviceSerial(deviceDO.getDeviceSerial());
        deviceBaseInfoVO.setDeviceCode(deviceDO.getDeviceCode());
        deviceBaseInfoVO.setProductName(Optional.ofNullable(productDO).map(AuthProductDO::getProductName).orElse(null));
        deviceBaseInfoVO.setLinkState(deviceDO.getLinkState());
        deviceBaseInfoVO.setLastUpTime(deviceDO.getLastUpTime());

        List<DeviceRunInfoVO> deviceRunInfoVOList = new ArrayList<>();

        if (StringUtils.isNotBlank(deviceDO.getDeviceVoltage())) {
            DeviceRunInfoVO Voltage = new DeviceRunInfoVO();
            Voltage.setItem("主板电压");
            Voltage.setReportValue(deviceDO.getDeviceVoltage());
            Voltage.setReportTime(deviceDO.getVoltageTime());
            deviceRunInfoVOList.add(Voltage);
        }

        if (StringUtils.isNotBlank(deviceDO.getDeviceTemperature())) {
            DeviceRunInfoVO temperature = new DeviceRunInfoVO();
            temperature.setItem("主板温度");
            temperature.setReportValue(deviceDO.getDeviceTemperature());
            temperature.setReportTime(deviceDO.getTemperatureTime());
            deviceRunInfoVOList.add(temperature);
        }

        if (Objects.nonNull(deviceDO.getClockTime())) {
            DeviceRunInfoVO clock = new DeviceRunInfoVO();
            clock.setItem("设备时钟");
            clock.setReportValue(deviceDO.getDeviceClock().format(DateTimeFormatter.ofPattern(DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)));
            clock.setReportTime(deviceDO.getClockTime());
            deviceRunInfoVOList.add(clock);
        }

        DeviceDetailResVO deviceDetailResVO = new DeviceDetailResVO();
        deviceDetailResVO.setDeviceBaseInfoVO(deviceBaseInfoVO);
        deviceDetailResVO.setDeviceRunInfoVOList(deviceRunInfoVOList);

        return deviceDetailResVO;
    }

    @Override
    public List<String> bindGatewayDevices(Long projectId, List<Long> deviceIds) {
        //查询当前项目已经存在的设备
        List<DeviceDO> deviceDbList = deviceMapper.selectList(DeviceDO::getProjectId, projectId);
        List<String> oldCodes = deviceDbList.stream().map(DeviceDO::getDeviceCode).toList();

        //查找设备和子设备
        Map<Long, DeviceRespDTO> iotDeviceMap = deviceApi.loadDevices(deviceIds);
        List<DeviceRespDTO> allIotDevice = new ArrayList<>(iotDeviceMap.values());
        List<DeviceRespDTO> childIotDevices = deviceApi.loadChildDevices(iotDeviceMap.values().stream().map(DeviceRespDTO::getDeviceCode).collect(Collectors.toList()));
        allIotDevice.addAll(childIotDevices);

        List<DeviceDO> addDevices = allIotDevice.stream().filter(o -> !oldCodes.contains(o.getDeviceCode())) //未绑定的设备
                .map(o -> {
                    DeviceDO deviceDO = new DeviceDO();
                    deviceDO.setProjectId(projectId);
                    deviceDO.setBindType(BindTypeEnum.NO.getType());
                    org.springframework.beans.BeanUtils.copyProperties(o, deviceDO, "id", "createTime", "creator", "updateTime", "updater");

                    return deviceDO;
                }).toList();

        deviceMapper.insertBatch(addDevices);
        return addDevices.stream().map(DeviceDO::getProductCode).collect(Collectors.toList());
    }

    @Override
    public void deleteGatewayDevice(Long deviceId) {
        DeviceDO deviceDO = validateDeviceExists(deviceId);
        List<DeviceDO> childrenList = getDeviceDOListByProjectIdAndParentCode(deviceDO.getProjectId(), deviceDO.getDeviceCode());
        if (childrenList.stream().anyMatch(o -> Objects.equals(o.getBindType(), BindTypeEnum.YES.getType()))) {
            throw exception(DEVICE_FORBID_DELETED);
        }
        //删除的产品code
        List<String> deleteProductCodeList = childrenList.stream().map(DeviceDO::getProductCode).collect(Collectors.toList());
        deleteProductCodeList.add(deviceDO.getProductCode());

        List<Long> ids = childrenList.stream().map(DeviceDO::getId).collect(Collectors.toList());
        deviceMapper.deleteBatchIds(CollUtil.addAll(ids, deviceId));
        //授权的产品同步删除
        List<DeviceDO> deviceDbList = deviceMapper.selectList(DeviceDO::getProjectId, deviceDO.getProjectId());
        List<String> productCodeDBList = deviceDbList.stream().map(DeviceDO::getProductCode).collect(Collectors.toList());
        List<String> deleteProductList = new ArrayList<>();
        deleteProductCodeList.forEach(item -> {
            if (!productCodeDBList.contains(item)) {
                deleteProductList.add(item);
            }
        });
        if (!CollectionUtils.isEmpty(deleteProductList)) {
            authProductService.deleteAuthProductByProductCode(deviceDO.getProjectId(), deleteProductList);
        }
    }

}