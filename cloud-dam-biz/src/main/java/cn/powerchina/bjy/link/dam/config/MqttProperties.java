package cn.powerchina.bjy.link.dam.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @Description: MQTT配置属性类
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Component
@ConfigurationProperties(prefix = "mqtt")
@Data
public class MqttProperties {

    @NestedConfigurationProperty
    private Broker broker = new Broker();

    @NestedConfigurationProperty
    private Client client = new Client();

    private String username = "";
    private String password = "";

    @NestedConfigurationProperty
    private Connection connection = new Connection();

    @NestedConfigurationProperty
    private Keep keep = new Keep();

    @NestedConfigurationProperty
    private Clean clean = new Clean();

    @NestedConfigurationProperty
    private Automatic automatic = new Automatic();

    // 使用@NestedConfigurationProperty和特殊的属性名来处理default关键字
    @NestedConfigurationProperty
    private DefaultConfig defaultConfig = new DefaultConfig();

    // 提供一个setter方法来处理YAML中的default属性
    public void setDefault(DefaultConfig defaultConfig) {
        this.defaultConfig = defaultConfig;
    }

    // 提供一个getter方法来获取default配置
    public DefaultConfig getDefault() {
        return this.defaultConfig;
    }

    @NestedConfigurationProperty
    private Auto auto = new Auto();

    @Data
    public static class Broker {
        private String url = "tcp://localhost:1883";
    }

    @Data
    public static class Client {
        private String id = "dam-client";
    }

    @Data
    public static class Connection {
        private int timeout = 10;
    }

    @Data
    public static class Keep {
        private Alive alive = new Alive();

        @Data
        public static class Alive {
            private int interval = 60;
        }
    }

    @Data
    public static class Clean {
        private boolean session = true;
    }

    @Data
    public static class Automatic {
        private boolean reconnect = true;
    }

    @Data
    public static class DefaultConfig {
        private String topic = "dam/default";
        private int qos = 1;
        private boolean retained = false;
        private Subscribe subscribe = new Subscribe();

        @Data
        public static class Subscribe {
            private int qos = 1;
        }
    }

    @Data
    public static class Auto {
        private Subscribe subscribe = new Subscribe();

        @Data
        public static class Subscribe {
            private String topics = "";
        }
    }
}
