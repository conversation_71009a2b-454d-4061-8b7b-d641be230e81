<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.dam.dal.tdengine.DamPointDataMapper">

    <select id="getPointDataSTableFieldList" resultType="cn.powerchina.bjy.link.dam.framework.tdengine.TDengineTableField">
        DESCRIBE point_data_${projectId}_${instrumentId}
    </select>

    <update id="createPointDataSTable">
        CREATE STABLE point_data_${projectId}_${instrumentId} (
                ts TIMESTAMP,
                data_type INT,
                data_status INT,
                creator NCHAR(64),
                create_time TIMESTAMP,
                review_status  INT,
                reviewer NCHAR(64),
                review_name NCHAR(64),
                review_opinion NCHAR(256),
                <foreach item="field" collection="fields" separator=",">
                    ${field.field} ${field.type}
                    <if test="field.length != null and field.length > 0">
                        (${field.length})
                    </if>
                </foreach>
            ) TAGS (
                point_id NCHAR(50)
            )
    </update>

    <update id="alterPointDataSTableAddField">
        ALTER STABLE point_data_${projectId}_${instrumentId}
        ADD COLUMN ${field.field} ${field.type}
        <if test="field.length != null and field.length > 0">
            (${field.length})
        </if>
    </update>

    <update id="alterPointDataSTableModifyField">
        ALTER STABLE point_data_${projectId}_${instrumentId}
        MODIFY COLUMN ${field.field} ${field.type}
        <if test="field.length != null and field.length > 0">
            (${field.length})
        </if>
    </update>

    <update id="alterPointDataSTableDropField">
        ALTER STABLE point_data_${projectId}_${instrumentId}
        DROP COLUMN ${field.field}
    </update>

    <insert id="insertReview1">
        INSERT INTO point_data_json1_${pointData.instrumentId}_${pointData.pointId}
        USING point_data_${pointData.projectId}_${pointData.instrumentId}
        TAGS ('${pointData.pointId}')
        (ts,review_status,reviewer,review_name,review_opinion,creator,create_time
        )
        VALUES
        (#{pointTime},#{pointData.reviewStatus},#{pointData.reviewer},#{pointData.reviewName},#{pointData.reviewOpinion},#{pointData.reviewer},NOW
        )
    </insert>

    <insert id="insertReview2">
        INSERT INTO point_data_json2_${pointData.instrumentId}_${pointData.pointId}
            USING point_data_${pointData.projectId}_${pointData.instrumentId}
            TAGS ('${pointData.pointId}')
        (ts,review_status,reviewer,review_name,review_opinion,creator,create_time
        )
        VALUES
            (#{pointTime},#{pointData.reviewStatus},#{pointData.reviewer},#{pointData.reviewName},#{pointData.reviewOpinion},#{pointData.reviewer},NOW
            )
    </insert>

    <insert id="insertType1">
        INSERT INTO point_data_json1_${pointData.instrumentId}_${pointData.pointId}
        USING point_data_${pointData.projectId}_${pointData.instrumentId}
        TAGS ('${pointData.pointId}')
        (ts,data_type,data_status,creator,create_time,
        <foreach item="key" collection="pointDataMap.keys" separator=",">
            ${@cn.hutool.core.util.StrUtil@toUnderlineCase(key)}
        </foreach>
        )
        VALUES
        (#{pointData.pointTime},#{pointData.dataType},#{pointData.dataStatus},#{pointData.creator},#{pointData.createTime},
        <foreach item="value" collection="pointDataMap.values" separator=",">
            #{value}
        </foreach>
        )
    </insert>

    <insert id="insertType2">
        INSERT INTO point_data_json2_${pointData.instrumentId}_${pointData.pointId}
        USING point_data_${pointData.projectId}_${pointData.instrumentId}
        TAGS ('${pointData.pointId}')
        (ts,data_type,data_status,creator,create_time,
        <foreach item="key" collection="pointDataMap.keys" separator=",">
            ${@cn.hutool.core.util.StrUtil@toUnderlineCase(key)}
        </foreach>
        )
        VALUES
        (#{pointData.pointTime},#{pointData.dataType},#{pointData.dataStatus},#{pointData.creator},#{pointData.createTime},
        <foreach item="value" collection="pointDataMap.values" separator=",">
            #{value}
        </foreach>
        )
    </insert>

    <update id="delByPointTime1">
        DELETE FROM point_data_json1_${instrumentId}_${pointId}
        WHERE ts >= #{startPointTime} and ts &lt;= #{endPointTime}
    </update>

    <update id="delByPointTime2">
        DELETE FROM point_data_json2_${instrumentId}_${pointId}
        WHERE ts >= #{startPointTime} and ts &lt;= #{endPointTime}
    </update>

    <select id="getLastPointDataJson" resultType="Map">
        select * from  point_data_json${dataType}_${instrumentId}_${pointId}
        order by ts DESC
        limit 1
    </select>

    <select id="selectPointDateJson" resultType="Map">
        SELECT * FROM point_data_${reqVO.projectId}_${reqVO.instrumentId}
        <where>
            1=1 and point_id = #{reqVO.pointId}
            <if test="reqVO.pointTime != null and reqVO.pointTime != ''">
                AND ts = #{reqVO.pointTime}
            </if>
            <if test="reqVO.startPointTime != null and reqVO.startPointTime != ''">
                AND ts >= #{reqVO.startPointTime}
            </if>
            <if test="reqVO.endPointTime != null and reqVO.endPointTime != ''">
                AND ts &lt;= #{reqVO.endPointTime}
            </if>
            <if test="reqVO.dataType != null and reqVO.dataType != ''">
                AND data_type = #{reqVO.dataType}
            </if>
            <if test="reqVO.dataStatus != null and reqVO.dataStatus != ''">
                AND data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.reviewStatus != null and reqVO.reviewStatus != ''">
                AND review_status = #{reqVO.reviewStatus}
            </if>
        </where>
        ORDER BY ts DESC
        <if test="reqVO.pageNo != null and reqVO.pageSize != null">
            LIMIT #{reqVO.pageNo},#{reqVO.pageSize}
        </if>
    </select>

    <select id="selectPointDateJsonCount" resultType="Long">
        SELECT count(1) FROM point_data_${reqVO.projectId}_${reqVO.instrumentId}
        <where>
            1=1 and point_id = #{reqVO.pointId}
            <if test="reqVO.pointTime != null and reqVO.pointTime != ''">
                AND ts = #{reqVO.pointTime}
            </if>
            <if test="reqVO.startPointTime != null and reqVO.startPointTime != ''">
                AND ts >= #{reqVO.startPointTime}
            </if>
            <if test="reqVO.endPointTime != null and reqVO.endPointTime != ''">
                AND ts &lt;= #{reqVO.endPointTime}
            </if>
            <if test="reqVO.dataType != null and reqVO.dataType != ''">
                AND data_type = #{reqVO.dataType}
            </if>
            <if test="reqVO.dataStatus != null and reqVO.dataStatus != ''">
                AND data_status = #{reqVO.dataStatus}
            </if>
            <if test="reqVO.reviewStatus != null and reqVO.reviewStatus != ''">
                AND review_status = #{reqVO.reviewStatus}
            </if>
        </where>
    </select>

<!--    <select id="showDeviceLogSTable" resultType="String">-->
<!--        SHOW STABLES LIKE 'point_data_${projectId}'-->
<!--    </select>-->

<!--    <insert id="insert">-->
<!--        INSERT INTO device_log__${projectId}_${instrument_id}-->
<!--            (ts,point_time, instrument_model_id, thing_identity, device_name, thing_name, thing_value_origin, thing_value, absolute_value, data_type,data_status,creator,create_time)-->
<!--        USING device_log-->
<!--        TAGS ('${instrumentId}')-->
<!--        VALUES (-->
<!--            NOW,-->
<!--            #{id},-->
<!--            #{productKey},-->
<!--            #{deviceName},-->
<!--            #{type},-->
<!--            #{identifier},-->
<!--            #{content},-->
<!--            #{code},-->
<!--            #{reportTime}-->
<!--        )-->
<!--    </insert>-->

<!--    <select id="selectPage" resultType="cn.iocoder.yudao.module.iot.dal.dataobject.device.IotDeviceLogDO">-->
<!--        SELECT ts, id, device_key, product_key, type, identifier, content, report_time-->
<!--        FROM device_log_${reqVO.deviceKey}-->
<!--        <where>-->
<!--            <if test="reqVO.type != null and reqVO.type != ''">-->
<!--                AND type = #{reqVO.type}-->
<!--            </if>-->
<!--            <if test="reqVO.identifier != null and reqVO.identifier != ''">-->
<!--                AND identifier LIKE CONCAT('%',#{reqVO.identifier},'%')-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY ts DESC-->
<!--    </select>-->

<!--    <select id="selectCountByCreateTime" resultType="Long">-->
<!--        SELECT COUNT(*)-->
<!--        FROM device_log-->
<!--        <where>-->
<!--            <if test="createTime != null">-->
<!--                AND ts >= #{createTime}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->

<!--    <select id="selectDeviceLogUpCountByHour" resultType="java.util.Map">-->
<!--        SELECT -->
<!--            TIMETRUNCATE(ts, 1h) as time,-->
<!--            COUNT(*) as data -->
<!--        FROM -->
<!--        <choose>-->
<!--            <when test="deviceKey != null and deviceKey != ''">-->
<!--                device_log_${deviceKey}-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                device_log-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--        <where>-->
<!--            <if test="startTime != null">-->
<!--                AND ts >= #{startTime}-->
<!--            </if>-->
<!--            <if test="endTime != null">-->
<!--                AND ts &lt;= #{endTime}-->
<!--            </if>-->
<!--            AND (-->
<!--                 identifier IN ('online', 'offline', 'pull', 'progress', 'report', 'register', 'register_sub')-->
<!--            )-->
<!--        </where>-->
<!--        GROUP BY TIMETRUNCATE(ts, 1h)-->
<!--        ORDER BY time ASC-->
<!--    </select>-->

<!--    <select id="selectDeviceLogDownCountByHour" resultType="java.util.Map">-->
<!--        SELECT -->
<!--            TIMETRUNCATE(ts, 1h) as time,-->
<!--            COUNT(*) as data -->
<!--        FROM -->
<!--        <choose>-->
<!--            <when test="deviceKey != null and deviceKey != ''">-->
<!--                device_log_${deviceKey}-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                device_log-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--        <where>-->
<!--            <if test="startTime != null">-->
<!--                AND ts >= #{startTime}-->
<!--            </if>-->
<!--            <if test="endTime != null">-->
<!--                AND ts &lt;= #{endTime}-->
<!--            </if>-->
<!--            AND identifier IN ('set', 'get', 'upgrade', 'unregister_sub', 'topology_add')-->
<!--        </where>-->
<!--        GROUP BY TIMETRUNCATE(ts, 1h)-->
<!--        ORDER BY time ASC-->
<!--    </select>-->

</mapper>