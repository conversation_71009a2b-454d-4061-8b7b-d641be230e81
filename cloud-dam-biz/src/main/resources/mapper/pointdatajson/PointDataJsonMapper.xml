<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.dam.dal.mysql.pointdatajson.PointDataJsonMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <select id="listMaxPointTimeByPointIdList" resultType="cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO">
        select max(point_time) as point_time, point_id from dam_point_data_json where point_id in
        <foreach item="item" index="index" collection="pointIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
        group by point_id
    </select>

    <select id="listMaxPointTimeByPointIdListTdengine" resultType="cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO">
        select max(ts) as point_time, point_id from point_data_${projectId}_${instrumentId} where point_id in
        <foreach item="item" index="index" collection="pointIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by point_id
    </select>

    <select id="list4ProcessLine" resultType="cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO">
        select * from dam_point_data_json
        where project_id=#{projectId}
          <if test="pointTimeList != null and pointTimeList.size() > 0">
            <foreach collection="pointTimeList" item="item" open="and (" separator="or" close=")">
                (point_id = #{item.pointId} and point_time between #{item.minTime} and #{item.maxTime})
            </foreach>
          </if>
          <if test="dataTypeList != null and dataTypeList.size() > 0">
            <foreach collection="dataTypeList" item="item" open="and data_type in (" separator="," close=")">
                #{item}
            </foreach>
          </if>
          <if test="dataStatusList != null and dataStatusList.size() > 0">
            <foreach collection="dataStatusList" item="item" open="and data_status in (" separator="," close=")">
                #{item}
            </foreach>
          </if>
          <if test="reviewStatusList != null and reviewStatusList.size() > 0">
            <foreach collection="reviewStatusList" item="item" open="and review_status in (" separator="," close=")">
                #{item}
            </foreach>
          </if>
          and deleted=0
    </select>

    <select id="list4DistributionChart" resultType="cn.powerchina.bjy.link.dam.dal.dataobject.pointdatajson.PointDataJsonDO">
        select * from dam_point_data_json
        where project_id=#{projectId}
        <if test="pointIdList != null and pointIdList.size() > 0">
            <foreach collection="pointIdList" item="item" open="and point_id in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="timeIntervalUnit != null and timeIntervalUnit==1 and pointTimeList != null and pointTimeList.size() > 0">
            and DATE_FORMAT(point_time, '%Y-%m-%d 00:00:00') in
            <foreach collection="pointTimeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="timeIntervalUnit != null and timeIntervalUnit==2 and pointTimeList != null and pointTimeList.size() > 0">
            and DATE_FORMAT(point_time, '%Y-%m-%d %H:00:00') in
            <foreach collection="pointTimeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataTypeList != null and dataTypeList.size() > 0">
            <foreach collection="dataTypeList" item="item" open="and data_type in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dataStatusList != null and dataStatusList.size() > 0">
            <foreach collection="dataStatusList" item="item" open="and data_status in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reviewStatusList != null and reviewStatusList.size() > 0">
            <foreach collection="reviewStatusList" item="item" open="and review_status in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and deleted=0
    </select>

    <update id="batchUpdateDelete">
       update dam_point_data_json set deleted= 1 ,rollback_import_id = #{rollbackImportId} WHERE point_id = #{pointId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        AND data_type = #{dataType} and deleted=0
    </update>

    <update id="batchUpdateRecover">
        update dam_point_data_json set deleted= 0  WHERE point_id = #{pointId}
        AND point_time BETWEEN #{startTime} AND #{endTime}
        AND data_type = #{dataType} and deleted=1 and rollback_import_id = #{rollbackImportId}
    </update>
</mapper>