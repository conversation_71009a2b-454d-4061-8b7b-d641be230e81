package cn.powerchina.bjy.link.iot.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum TransportSourceTypeEnum {
    PRODUCT_CREATE("products_create", "产品创建"),
    PRODUCT_UPDATE("products_update", "产品更新"),
    PRODUCT_DELETE("products_delete", "产品删除"),
    DEVICE_CREATE("devices_create", "设备创建"),
    DEVICE_UPDATE("devices_update", "设备更新"),
    DEVICE_DELETE("devices_delete", "设备删除"),
    DEVICE_STATUS_CHANGE("devices_statue", "设备在线状态变更"),
    DEVICE_ATTRIBUTE_REPORT("properties_report", "设备属性上报"),
    DEVICE_EVENT_REPORT("events_up", "设备事件上报"),
    DEVICE_COMMAND_RESULT("commands_result", "设备指令控制结果");

    private final String code;
    private final String desc;

    TransportSourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 返回包含所有枚举信息的Map列表
     * @return List<Map<String, Object>> 枚举信息列表
     */
    public static List<Map<String, Object>> getEnumList() {
        return Arrays.stream(values())
                .map(enumItem -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("code", enumItem.getCode());
                    map.put("desc", enumItem.getDesc());
                    return map;
                })
                .collect(Collectors.toList());
    }

}
