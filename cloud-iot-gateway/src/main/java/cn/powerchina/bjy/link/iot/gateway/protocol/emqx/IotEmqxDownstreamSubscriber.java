package cn.powerchina.bjy.link.iot.gateway.protocol.emqx;


import cn.powerchina.bjy.link.iot.gateway.messagebus.core.IotMessageSubscriber;
import cn.powerchina.bjy.link.iot.gateway.mq.rocketmq.IotDeviceMessage;
import cn.powerchina.bjy.link.iot.gateway.protocol.emqx.router.IotEmqxDownstreamHandler;
import cn.powerchina.bjy.link.iot.gateway.util.IotDeviceMessageUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * IoT 网关 EMQX 订阅者：接收下行给设备的消息
 *
 * <AUTHOR>
 */
@Slf4j
public class IotEmqxDownstreamSubscriber implements IotMessageSubscriber<IotDeviceMessage> {

    private final IotEmqxDownstreamHandler downstreamHandler;


    private final IotEmqxUpstreamProtocol protocol;

    public IotEmqxDownstreamSubscriber(IotEmqxUpstreamProtocol protocol) {
        this.protocol = protocol;
        this.downstreamHandler = new IotEmqxDownstreamHandler(protocol);
    }


    @Override
    public String getTopic() {
        return IotDeviceMessageUtils.buildMessageBusGatewayDeviceMessageTopic(protocol.getServerId());
    }

    @Override
    public String getGroup() {
        // 保证点对点消费，需要保证独立的 Group，所以使用 Topic 作为 Group
        return getTopic();
    }

    @Override
    public void onMessage(IotDeviceMessage message) {
        log.debug("[onMessage][接收到下行消息, messageId: {}, method: {}, deviceId: {}]",
                message.getId(), message.getMethod(), message.getDeviceId());
        try {
            // 1. 校验
            String method = message.getMethod();
            if (method == null) {
                log.warn("[onMessage][消息方法为空, messageId: {}, deviceId: {}]",
                        message.getId(), message.getDeviceId());
                return;
            }

            // 2. 处理下行消息
            downstreamHandler.handle(message);
        } catch (Exception e) {
            log.error("[onMessage][处理下行消息失败, messageId: {}, method: {}, deviceId: {}]",
                    message.getId(), message.getMethod(), message.getDeviceId(), e);
        }
    }

}