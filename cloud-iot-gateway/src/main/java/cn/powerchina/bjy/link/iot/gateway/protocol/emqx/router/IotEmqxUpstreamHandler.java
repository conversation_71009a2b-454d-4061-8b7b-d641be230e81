package cn.powerchina.bjy.link.iot.gateway.protocol.emqx.router;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.gateway.mq.rocketmq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.gateway.protocol.emqx.IotEmqxUpstreamProtocol;
import cn.powerchina.bjy.link.iot.gateway.service.device.message.IotDeviceMessageService;
import cn.powerchina.bjy.link.iot.model.IotDeviceMessage;
import io.vertx.mqtt.messages.MqttPublishMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

import static cn.powerchina.bjy.link.iot.enums.IotTopicConstant.GROUP_DEVICE_PROPERTY;

/**
 * IoT 网关 EMQX 上行消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class IotEmqxUpstreamHandler {

    private final IotDeviceMessageService deviceMessageService;

    private final String serverId;

    public IotEmqxUpstreamHandler(IotEmqxUpstreamProtocol protocol) {
        this.deviceMessageService = SpringUtil.getBean(IotDeviceMessageService.class);
        this.serverId = protocol.getServerId();
    }

    /**
     * 处理 MQTT 发布消息
     */
    public void handle(MqttPublishMessage mqttMessage) {
        log.info("[handle][收到 MQTT 消息, topic: {}, payload: {}]", mqttMessage.topicName(), mqttMessage.payload());
        String topic = mqttMessage.topicName();
        byte[] payload = mqttMessage.payload().getBytes();
        try {
            // 1. 解析主题，一次性获取所有信息
            String[] topicParts = topic.split("/");
            if (topicParts.length < 4 || StrUtil.hasBlank(topicParts[3])) {
                log.warn("[handle][topic({}) 格式不正确，无法解析有效的 deviceCode]", topic);
                return;
            }

            String deviceCode = topicParts[3];
            String method = String.join(".", Arrays.copyOfRange(topicParts, 4, topicParts.length));
            IotDeviceMessage deviceMessage = JsonUtils.parseObject(payload, IotDeviceMessage.class);
            deviceMessage.setMethod(method);
            deviceMessage.setDeviceCode(deviceCode);
            // 发送rocketMQ
            RocketMQv5Client rocketMQv5Client = SpringUtil.getBean(RocketMQv5Client.class);
            rocketMQv5Client.syncSendFifoMessage(IotDeviceMessage.TOPIC_IOT_DEVICE_MESSAGE, deviceMessage, GROUP_DEVICE_PROPERTY);

        } catch (Exception e) {
            log.error("[handle][topic({}) payload({}) 处理异常]", topic, new String(payload), e);
        }
    }

}