package cn.powerchina.bjy.link.iot.dal.mysql.scenealarmrecord;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord.SceneAlarmRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 告警记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneAlarmRecordMapper extends BaseMapperX<SceneAlarmRecordDO> {

    default PageResult<SceneAlarmRecordDO> selectPage(SceneAlarmRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SceneAlarmRecordDO>()
                .eqIfPresent(SceneAlarmRecordDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                .inIfPresent(SceneAlarmRecordDO::getResourceSpaceId, reqVO.getResourceSpaceIdList())
                .eqIfPresent(SceneAlarmRecordDO::getRuleId, reqVO.getRuleId())
                .likeIfPresent(SceneAlarmRecordDO::getRuleName, reqVO.getRuleName())
                .eqIfPresent(SceneAlarmRecordDO::getAlarmTemplateId, reqVO.getAlarmTemplateId())
                .likeIfPresent(SceneAlarmRecordDO::getAlarmName, reqVO.getAlarmName())
                .eqIfPresent(SceneAlarmRecordDO::getAlarmContent, reqVO.getAlarmContent())
                .eqIfPresent(SceneAlarmRecordDO::getAlarmLevel, reqVO.getAlarmLevel())
                .eqIfPresent(SceneAlarmRecordDO::getAlarmStatus, reqVO.getAlarmStatus())
                .betweenIfPresent(SceneAlarmRecordDO::getTriggerTime, reqVO.getTriggerTime())
                .eqIfPresent(SceneAlarmRecordDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(SceneAlarmRecordDO::getProductCode, reqVO.getProductCode())
                .betweenIfPresent(SceneAlarmRecordDO::getProcessTime, reqVO.getProcessTime())
                .eqIfPresent(SceneAlarmRecordDO::getProcessRecord, reqVO.getProcessRecord())
                .eqIfPresent(SceneAlarmRecordDO::getProcessUserId, reqVO.getProcessUserId())
                .eqIfPresent(SceneAlarmRecordDO::getRecoveryType, reqVO.getRecoveryType())
                .betweenIfPresent(SceneAlarmRecordDO::getRecoveryTime, reqVO.getRecoveryTime())
                .eqIfPresent(SceneAlarmRecordDO::getRecoveryUserId, reqVO.getRecoveryUserId())
                .betweenIfPresent(SceneAlarmRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SceneAlarmRecordDO::getId));
    }

}
