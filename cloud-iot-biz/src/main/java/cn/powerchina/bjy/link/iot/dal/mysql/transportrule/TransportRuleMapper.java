package cn.powerchina.bjy.link.iot.dal.mysql.transportrule;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo.TransportRulePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportrule.TransportRuleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.util.CollectionUtils;

/**
 * 数据转发规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransportRuleMapper extends BaseMapperX<TransportRuleDO> {

    default PageResult<TransportRuleDO> selectPage(TransportRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TransportRuleDO>()
                .eqIfPresent(TransportRuleDO::getTransportRuleCode, reqVO.getTransportRuleCode())
                .eqIfPresent(TransportRuleDO::getTransportDesc, reqVO.getTransportDesc())
                .likeIfPresent(TransportRuleDO::getTransportName, reqVO.getTransportName())
                .eqIfPresent(TransportRuleDO::getTransportType, reqVO.getTransportType())
                .eqIfPresent(TransportRuleDO::getTransportUrl, reqVO.getTransportUrl())
                .eqIfPresent(TransportRuleDO::getTransportToken, reqVO.getTransportToken())
                .eqIfPresent(TransportRuleDO::getHost, reqVO.getHost())
                .eqIfPresent(TransportRuleDO::getPort, reqVO.getPort())
                .eqIfPresent(TransportRuleDO::getTopic, reqVO.getTopic())
                .eqIfPresent(TransportRuleDO::getRemark, reqVO.getRemark())
                .inIfPresent(TransportRuleDO::getResourceSpaceId, reqVO.getResourceSpaceIds())
                .eqIfPresent(TransportRuleDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(TransportRuleDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TransportRuleDO::getId));
    }

    /**
     * 切换数据转发启用状态
     *
     * @param id    主键id
     * @param status 切换后的状态值
     * @return
     */
    @Update("update iot_transport_rule set status = #{status} where id = #{id} and status!=#{status}")
    Integer updateTransportRuleStatus(@Param("id") Long id, @Param("status") Integer status);
}