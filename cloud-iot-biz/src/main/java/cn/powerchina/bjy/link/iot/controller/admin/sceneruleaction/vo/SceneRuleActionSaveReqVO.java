package cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo;

import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplateSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.*;
import jakarta.validation.constraints.*;

import java.util.List;

@Schema(description = "管理后台 - 场景规则执行动作新增/修改 Request VO")
@Data
public class SceneRuleActionSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12608")
    private Long id;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23926")
    private Long ruleId;

    @Schema(description = "动作类型:1-设备动作,2-告警动作,3-执行场景", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "动作类型:1-设备动作,2-告警动作,3-执行场景不能为空")
    private Integer actionType;

    @Schema(description = "延迟执行时间(单位秒)")
    private String delaySeconds;

    @Schema(description = "产品code", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productCode;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "设备code,为空表示所有设备")
    private String deviceCode;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "指令集合", hidden = true)
    private String commandConfig;

    @Schema(description = "场景ID：规则id", example = "12646")
    private Long sceneId;

    @Schema(description = "场景状态:1-执行场景,2-开启场景,3-禁用场景", example = "1")
    private Integer sceneStatus;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Valid
    @Schema(description = "告警模板表")
    private AlarmTemplateSaveReqVO alarmTemplateSave;

    @Schema(description = "服务的指令集")
    private List<CommandVO> commandList;

}