package cn.powerchina.bjy.link.iot.service.transportsource;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsTreeVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.DeviceRelayVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.ProductRelayVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.TransportSourcePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.TransportSourceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportsource.TransportSourceDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.resourcespace.ResourceSpaceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.transportsource.TransportSourceMapper;
import cn.powerchina.bjy.link.iot.dto.message.DeviceAttributeModel;
import cn.powerchina.bjy.link.iot.dto.message.DeviceStatusModel;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import cn.powerchina.bjy.link.iot.service.transporttarget.TransportTargetService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.TRANSPORT_SOURCE_NOT_EXISTS;

/**
 * 转发规则-数据源 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TransportSourceServiceImpl implements TransportSourceService {

    @Resource
    private TransportSourceMapper transportSourceMapper;
    @Resource
    private ResourceSpaceMapper resourceSpaceMapper;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    @Lazy
    private ProductService productService;
    @Resource
    private TransportTargetService transportTargetService;

    @Override
    public Long createTransportSource(TransportSourceSaveReqVO createReqVO) {
        // 插入
        TransportSourceDO transportSource = BeanUtils.toBean(createReqVO, TransportSourceDO.class);
        transportSourceMapper.insert(transportSource);
        // 返回
        return transportSource.getId();
    }

    @Override
    public void updateTransportSource(TransportSourceSaveReqVO updateReqVO) {
        // 校验存在
        validateTransportSourceExists(updateReqVO.getId());
        // 更新
        TransportSourceDO updateObj = BeanUtils.toBean(updateReqVO, TransportSourceDO.class);
        transportSourceMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransportSource(Long id) {
        // 校验存在
        validateTransportSourceExists(id);
        // 删除
        transportSourceMapper.deleteById(id);
    }

    private void validateTransportSourceExists(Long id) {
        if (transportSourceMapper.selectById(id) == null) {
            throw exception(TRANSPORT_SOURCE_NOT_EXISTS);
        }
    }

    @Override
    public TransportSourceDO getTransportSource(Long id) {
        return transportSourceMapper.selectById(id);
    }

    @Override
    public PageResult<TransportSourceDO> getTransportSourcePage(TransportSourcePageReqVO pageReqVO) {
        return transportSourceMapper.selectPage(pageReqVO);
    }

    public List<DataPermissionsTreeVO> getRange() {
        //查询所有资源空间数据
        List<ResourceSpaceDO> resourceSpaceDOList = resourceSpaceMapper.selectList();
        //查询所有产品数据
        List<ProductDO> productDOList = productMapper.selectList();
        //查询所有设备数据
        List<DeviceDO> deviceDOList = deviceMapper.selectList();
        List<DataPermissionsTreeVO> list = new ArrayList<>();
        list.add(DataPermissionsTreeVO.builder().id("-1").name("全部资源空间").parentId("-1").level(0).build());
        if (!cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.isAnyEmpty(resourceSpaceDOList)) {
            resourceSpaceDOList.forEach(resoure -> {
                list.add(DataPermissionsTreeVO.builder().id(resoure.getId() + "").name(resoure.getSpaceName()).parentId("0").level(1).build());
                list.add(DataPermissionsTreeVO.builder().id("-1").name("(+)所有产品").parentId(resoure.getId().toString()).level(2).build());
                productDOList.forEach(product -> {
                    if (resoure.getId().equals(product.getResourceSpaceId())) {
                        list.add(DataPermissionsTreeVO.builder().id(product.getId() + "").name(product.getProductName()).parentId(resoure.getId().toString()).level(3).build());
                        list.add(DataPermissionsTreeVO.builder().id("-1").name("(+)所有设备").parentId(product.getId().toString()).level(4).build());
                        deviceDOList.forEach(device -> {
                            if (product.getProductCode().equals(device.getProductCode())) {
                                list.add(DataPermissionsTreeVO.builder().id(device.getId() + "").name(device.getDeviceName()).parentId(product.getId().toString()).level(5).build());
                            }
                        });
                    }
                });
            });
        }
        return list;
    }

    @Override
    public void dataForwarding(TransportSourceTypeEnum dataType, Object obj) {
        //查询所有资源空间
        QueryWrapper<TransportSourceDO> wrapper = new QueryWrapper<>();
        wrapper.like("data_type", dataType.getCode());
        wrapper.eq("resource_space_id", -1);
        List<TransportSourceDO> allSpaceList = transportSourceMapper.selectList(wrapper);
        //产品创建、更新、删除
        if (dataType == TransportSourceTypeEnum.PRODUCT_CREATE
                || dataType == TransportSourceTypeEnum.PRODUCT_UPDATE
                || dataType == TransportSourceTypeEnum.PRODUCT_DELETE) {
            this.sendByProduct(dataType, obj, allSpaceList);
        }
        //设备创建、更新、删除、设备状态变更
        if (dataType == TransportSourceTypeEnum.DEVICE_CREATE
                || dataType == TransportSourceTypeEnum.DEVICE_UPDATE
                || dataType == TransportSourceTypeEnum.DEVICE_DELETE) {
            this.sendByDevice(dataType, obj, allSpaceList);
        }
        //设备属性上报、设备事件上报、设备指令控制结果
        if (dataType == TransportSourceTypeEnum.DEVICE_STATUS_CHANGE
                || dataType == TransportSourceTypeEnum.DEVICE_ATTRIBUTE_REPORT
                || dataType == TransportSourceTypeEnum.DEVICE_EVENT_REPORT
                || dataType == TransportSourceTypeEnum.DEVICE_COMMAND_RESULT) {
            if (CollectionUtils.isEmpty(allSpaceList)) {
                this.sendByOther(dataType, obj, allSpaceList);
            }
        }
    }

    public void sendByProduct(TransportSourceTypeEnum dataType, Object obj, List<TransportSourceDO> transportSourceList) {

        boolean isFlag = false;
        ProductRelayVO productRelayVO = (ProductRelayVO) obj;
        //无所有资源空间
        if (CollectionUtils.isEmpty(transportSourceList)) {
            //查询所有产品数据源
            QueryWrapper<TransportSourceDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("data_type", dataType.getCode())
                    .eq("resource_spaceId", productRelayVO.getResourceSpaceId())
                    .eq("product_code", -1);
            transportSourceList = transportSourceMapper.selectList(queryWrapper);
            //无所有产品数据源
            if (CollectionUtils.isEmpty(transportSourceList)) {
                //查询指定产品
                queryWrapper.clear();
                queryWrapper.like("data_type", dataType.getCode())
                        .eq("resource_spaceId", productRelayVO.getResourceSpaceId())
                        .eq("product_code", productRelayVO.getProductCode());
                transportSourceList = transportSourceMapper.selectList(queryWrapper);
                isFlag = !CollectionUtils.isEmpty(transportSourceList);
            } else {
                isFlag = true;
            }
        } else {
            isFlag = true;
        }
        if (isFlag) {
            log.info("产品消息转发开始，产品code：{}", productRelayVO.getProductCode());
            transportTargetService.productDataTransportTarget(dataType, transportSourceList.get(0).getRuleId(), productRelayVO);
        }
    }

    public void sendByDevice(TransportSourceTypeEnum dataType, Object obj, List<TransportSourceDO> transportSourceList) {
        boolean isFlag = false;
        DeviceRelayVO deviceRelayVO = (DeviceRelayVO) obj;
        if (CollectionUtils.isEmpty(transportSourceList)) {
            QueryWrapper<TransportSourceDO> queryWrapper = new QueryWrapper<>();
            // 重新构建查询条件，确保 resource_spaceId 查询条件不会被覆盖
            queryWrapper.like("data_type", dataType.getCode());
            queryWrapper.eq("resource_spaceId", deviceRelayVO.getResourceSpaceId());
            queryWrapper.eq("product_code", -1);
            transportSourceList = transportSourceMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(transportSourceList)) {
                queryWrapper.clear();
                queryWrapper.like("data_type", dataType.getCode());
                queryWrapper.eq("resource_spaceId", deviceRelayVO.getResourceSpaceId());
                queryWrapper.eq("product_code", deviceRelayVO.getProductCode());
                transportSourceList = transportSourceMapper.selectList(queryWrapper);
                isFlag = !CollectionUtils.isEmpty(transportSourceList);
                if (isFlag) {//true
                    queryWrapper.clear();
                    queryWrapper.like("data_type", dataType.getCode());
                    queryWrapper.eq("resource_spaceId", deviceRelayVO.getResourceSpaceId());
                    queryWrapper.eq("product_code", deviceRelayVO.getProductCode());
                    queryWrapper.eq("device_code", -1);
                    transportSourceList = transportSourceMapper.selectList(queryWrapper);
                    isFlag = !CollectionUtils.isEmpty(transportSourceList);
                    if (!isFlag) {
                        queryWrapper.clear();
                        queryWrapper.like("data_type", dataType.getCode());
                        queryWrapper.eq("resource_spaceId", deviceRelayVO.getResourceSpaceId());
                        queryWrapper.eq("product_code", deviceRelayVO.getProductCode());
                        queryWrapper.eq("device_code", deviceRelayVO.getDeviceCode());
                        transportSourceList = transportSourceMapper.selectList(queryWrapper);
                        isFlag = !CollectionUtils.isEmpty(transportSourceList);
                    }
                }
            } else {
                isFlag = true;
            }
        } else {
            isFlag = true;
        }
        if (isFlag) {
            log.info("设备消息转发开始，设备code：{}", deviceRelayVO.getDeviceCode());
            transportTargetService.deviceDataTransportTarget(transportSourceList.get(0).getRuleId(), deviceRelayVO);
        }
    }

    public void sendByOther(TransportSourceTypeEnum dataType, Object obj, List<TransportSourceDO> transportSourceList) {
        String productCode = null;
        String deviceCode = null;
        Long resourceSpaceId = null;
        if (dataType == TransportSourceTypeEnum.DEVICE_STATUS_CHANGE) {
            DeviceStatusModel deviceStatusChangeVO = (DeviceStatusModel) obj;
            productCode = deviceStatusChangeVO.getProductCode();
            deviceCode = deviceStatusChangeVO.getDeviceCode();
        }
        if (dataType == TransportSourceTypeEnum.DEVICE_ATTRIBUTE_REPORT) {
            DeviceAttributeModel deviceAttributeVO = (DeviceAttributeModel) obj;
            productCode = deviceAttributeVO.getProductCode();
            deviceCode = deviceAttributeVO.getDeviceCode();
        }
        // 通过productCode查询资源空间id
        ProductDO productDO = productService.getProductByCode(productCode);
        if (productDO != null) {
            resourceSpaceId = productDO.getResourceSpaceId();
        }

        boolean isFlag = false;
        if (CollectionUtils.isEmpty(transportSourceList)) {
            QueryWrapper<TransportSourceDO> queryWrapper = new QueryWrapper<>();
            // 重新构建查询条件，确保 resource_spaceId 查询条件不会被覆盖
            queryWrapper.like("data_type", dataType.getCode());
            queryWrapper.eq("resource_spaceId", resourceSpaceId);
            queryWrapper.eq("product_code", -1);
            transportSourceList = transportSourceMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(transportSourceList)) {
                queryWrapper.clear();
                queryWrapper.like("data_type", dataType.getCode());
                queryWrapper.eq("resource_spaceId", resourceSpaceId);
                queryWrapper.eq("product_code", productCode);
                transportSourceList = transportSourceMapper.selectList(queryWrapper);
                isFlag = !CollectionUtils.isEmpty(transportSourceList);
                if (isFlag) {//true
                    queryWrapper.clear();
                    queryWrapper.like("data_type", dataType.getCode());
                    queryWrapper.eq("resource_spaceId", resourceSpaceId);
                    queryWrapper.eq("product_code", productCode);
                    queryWrapper.eq("device_code", -1);
                    transportSourceList = transportSourceMapper.selectList(queryWrapper);
                    isFlag = !CollectionUtils.isEmpty(transportSourceList);
                    if (!isFlag) {
                        queryWrapper.clear();
                        queryWrapper.like("data_type", dataType.getCode());
                        queryWrapper.eq("resource_spaceId", resourceSpaceId);
                        queryWrapper.eq("product_code", productCode);
                        queryWrapper.eq("device_code", deviceCode);
                        transportSourceList = transportSourceMapper.selectList(queryWrapper);
                        isFlag = !CollectionUtils.isEmpty(transportSourceList);
                    }
                }
            } else {
                isFlag = true;
            }
        } else {
            isFlag = true;
        }
        if (isFlag) {
            log.info("消息转发");
            if (dataType == TransportSourceTypeEnum.DEVICE_STATUS_CHANGE) {
                DeviceStatusModel deviceStatusModel = (DeviceStatusModel) obj;
                log.info("设备状态变更消息转发开始，设备code：{}", deviceStatusModel.getDeviceCode());
                transportTargetService.deviceStatusChangeTransportTarget(transportSourceList.get(0).getRuleId(), deviceStatusModel);
            }
            if (dataType == TransportSourceTypeEnum.DEVICE_ATTRIBUTE_REPORT) {
                DeviceAttributeModel deviceAttributeModel = (DeviceAttributeModel) obj;
                log.info("设备属性上报消息转发开始，设备code：{}", deviceAttributeModel.getDeviceCode());
                transportTargetService.deviceAttributeTransportTarget(transportSourceList.get(0).getRuleId(), deviceAttributeModel);
            }
        }
    }

}