package cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 场景规则执行动作分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneRuleActionPageReqVO extends PageParam {

    @Schema(description = "规则ID", example = "23926")
    private Long ruleId;

    @Schema(description = "动作类型:1-设备动作,2-告警动作,3-执行场景", example = "2")
    private Integer actionType;

    @Schema(description = "指令类型:1-属性,2-服务", example = "1")
    private Integer commandType;

    @Schema(description = "延迟执行时间(单位秒)")
    private String delaySeconds;

    @Schema(description = "产品code")
    private String productCode;

    @Schema(description = "设备code,为空表示所有设备")
    private String deviceCode;

    @Schema(description = "指令唯一标识：属性/服务")
    private String commandIdentity;

    @Schema(description = "参数配置")
    private String paramsValue;

    @Schema(description = "场景ID：规则id", example = "12646")
    private Long sceneId;

    @Schema(description = "场景状态:1-执行场景,2-开启场景,3-禁用场景", example = "1")
    private Integer sceneStatus;

    @Schema(description = "告警模板id", example = "7419")
    private Long alarmTemplateId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}