package cn.powerchina.bjy.link.iot.service.tdengine;


import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;

/**
 * IoT 设备【属性】数据 Service 接口
 *
 * <AUTHOR>
 */
public interface IotDevicePropertyService {

    // ========== 设备属性相关操作 ==========

    /**
     * 创建一个空的超级表
     *
     * 产品编号
     */
    void defineDevicePropertyData( ProductDO product);


    /**
     * 保存设备数据
     *
     * @param message 设备消息
     */
//    void saveDeviceProperty(IotDeviceMessage message);


}