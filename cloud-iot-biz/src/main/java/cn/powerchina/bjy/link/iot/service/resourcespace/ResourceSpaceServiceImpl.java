package cn.powerchina.bjy.link.iot.service.resourcespace;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.aop.resourcespace.ResourceSpacePermissionCheck;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpacePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpaceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroup.DeviceGroupDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportrule.TransportRuleDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportsource.TransportSourceDO;
import cn.powerchina.bjy.link.iot.dal.mysql.edgegateway.EdgeGatewayMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.resourcespace.ResourceSpaceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.transportrule.TransportRuleMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.transportsource.TransportSourceMapper;
import cn.powerchina.bjy.link.iot.enums.EnableStateEnum;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.iot.enums.SceneTypeEnum;
import cn.powerchina.bjy.link.iot.service.devicegroup.DeviceGroupService;
import cn.powerchina.bjy.link.iot.service.devicegroupdetail.DeviceGroupDetailService;
import cn.powerchina.bjy.link.iot.util.CodeGenerator;
import cn.powerchina.bjy.link.iot.util.SnowFlakeUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.RESOURCE_SPACE_NAME_REFERENCE_EXISTS;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.RESOURCE_SPACE_NOT_EXISTS;

/**
 * 资源空间 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ResourceSpaceServiceImpl implements ResourceSpaceService {

    @Resource
    private ResourceSpaceMapper resourceSpaceMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private EdgeGatewayMapper edgeGatewayMapper;

    @Autowired
    private TransportRuleMapper transportRuleMapper;

    @Autowired
    private TransportSourceMapper transportSourceMapper;

    @Autowired
    private SnowFlakeUtil snowFlakeUtil;

    @Autowired
    private DeviceGroupDetailService deviceGroupDetailService;

    @Autowired
    private DeviceGroupService deviceGroupService;

    @Override
    public Long createResourceSpace(ResourceSpaceSaveReqVO createReqVO) {
        //校验资源空间名称是否存在
        validateResourceSpaceNameExists(createReqVO.getSpaceName(), null);
        // 插入
        ResourceSpaceDO resourceSpace = BeanUtils.toBean(createReqVO, ResourceSpaceDO.class);
        resourceSpace.setId(snowFlakeUtil.snowflakeId());
        resourceSpace.setSpaceAppid(CodeGenerator.createCode(SceneTypeEnum.RESOURCE_SPACE.getPrefix()));
        resourceSpace.setState(EnableStateEnum.YES.getType());
        resourceSpaceMapper.insert(resourceSpace);
        // 返回
        return resourceSpace.getId();
    }

    @Override
    public void updateResourceSpace(ResourceSpaceSaveReqVO updateReqVO) {
        // 校验存在
        validateResourceSpaceExists(updateReqVO.getId());
        //校验资源空间名称是否存在
        validateResourceSpaceNameExists(updateReqVO.getSpaceName(), updateReqVO.getId());
        // 更新
        ResourceSpaceDO updateObj = BeanUtils.toBean(updateReqVO, ResourceSpaceDO.class);
        resourceSpaceMapper.updateById(updateObj);
    }

    @Override
    public void deleteResourceSpace(Long id) {
        // 校验存在
        validateResourceSpaceExists(id);
        //校验产品是否有引用
        if (productMapper.selectCount(new LambdaQueryWrapperX<ProductDO>().eq(ProductDO::getResourceSpaceId, id)) > 0
                //校验边缘计算是否有引用
                || edgeGatewayMapper.selectCount(new LambdaQueryWrapperX<EdgeGatewayDO>().eq(EdgeGatewayDO::getResourceSpaceId, id)) > 0
                //校验数据转发是否有引用
                //|| transportRuleMapper.selectCount(new LambdaQueryWrapperX<TransportRuleDO>().eq(TransportRuleDO::getResourceSpaceId, id)) > 0
                || transportSourceMapper.selectCount(new LambdaQueryWrapperX<TransportSourceDO>().eq(TransportSourceDO::getResourceSpaceId, id)) > 0) {
            throw exception(RESOURCE_SPACE_NAME_REFERENCE_EXISTS);
        }
        // 删除
        resourceSpaceMapper.deleteById(id);
    }

    private void validateResourceSpaceExists(Long id) {
        if (resourceSpaceMapper.selectById(id) == null) {
            throw exception(RESOURCE_SPACE_NOT_EXISTS);
        }
    }

    /**
     * 校验资源空间名称是否存在
     *
     * @param spaceName
     * @param id
     */
    private ResourceSpaceDO validateResourceSpaceNameExists(String spaceName, Long id) {
        ResourceSpaceDO spaceDO = resourceSpaceMapper.selectOne(new LambdaQueryWrapperX<ResourceSpaceDO>()
                .eq(ResourceSpaceDO::getSpaceName, spaceName)
                .last("limit 1"));
        if (Objects.nonNull(spaceDO) && (Objects.isNull(id) || !Objects.equals(spaceDO.getId(), id))) {
            throw exception(ErrorCodeConstants.RESOURCE_SPACE_NAME_EXISTS);
        }
        return spaceDO;
    }

    @Override
    public ResourceSpaceDO getResourceSpace(Long id) {
        return Objects.isNull(id) ? null : resourceSpaceMapper.selectById(id);
    }

    @ResourceSpacePermissionCheck
    @Override
    public PageResult<ResourceSpaceDO> getResourceSpacePage(ResourceSpacePageReqVO pageReqVO) {
        return resourceSpaceMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ResourceSpaceDO> getResourceSpaceList() {
        return resourceSpaceMapper.selectList(new LambdaQueryWrapperX<ResourceSpaceDO>().eq(ResourceSpaceDO::getState, "1"));
    }

    @Override
    public boolean switchResourceSpaceState(Long id, Integer state) {
        return resourceSpaceMapper.updateResourceSpaceState(id, state) > 0;
    }

    @Override
    public Map<String, List<Long>> batchFindDeviceResourceId(List<String> deviceCodeList) {
        Map<String, List<Long>> deviceResourceIdMap = new HashMap<>();
        Map<String, List<Long>> deviceGroupIdMap = deviceGroupDetailService.batchFindDeviceGroupId(deviceCodeList);
        if (!CollectionUtils.isEmpty(deviceCodeList)) {
            deviceCodeList.forEach(item -> deviceResourceIdMap.put(item, deviceGroupService.getDeviceGroupListByIds(deviceGroupIdMap.get(item)).stream().map(DeviceGroupDO::getId).distinct().toList()));
        }
        return deviceResourceIdMap;
    }

}