package cn.powerchina.bjy.link.iot.service.edgegateway;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RolePageRespDTO;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import cn.powerchina.bjy.cloud.system.enums.permission.DataScopeEnum;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.bo.EdgeGatewayBO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.EdgeGatewayPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.EdgeGatewayRegisterReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.EdgeGatewaySaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel.EdgeChannelDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions.RoleDataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.mysql.datapermissions.DataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.edgechannel.EdgeChannelMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.edgegateway.EdgeGatewayMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.roledatapermissions.RoleDataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dto.down.*;
import cn.powerchina.bjy.link.iot.enums.*;
import cn.powerchina.bjy.link.iot.model.DeviceCheckOnlineModel;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 边缘网关 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class EdgeGatewayServiceImpl implements EdgeGatewayService {

    @Resource
    private RedisTemplate<String, Integer> redisTemplate;

    @Resource
    private EdgeGatewayMapper edgeGatewayMapper;

    @Resource
    private EdgeChannelMapper edgeChannelMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private RocketMQv5Client rocketMQv5Client;

    @Resource
    private DataPermissionsMapper dataPermissionsMapper;

    @Resource
    private RoleDataPermissionsMapper roleDataPermissionsMapper;

    @Autowired
    private ResourceSpaceService resourceSpaceService;

    @Resource
    private PermissionApi permissionApi;
    @Resource
    private RoleApi roleApi;


    @Override
    public Long createEdgeGateway(EdgeGatewaySaveReqVO createReqVO) {
        // 插入
        EdgeGatewayDO edgeGateway = BeanUtils.toBean(createReqVO, EdgeGatewayDO.class);
        edgeGatewayMapper.insert(edgeGateway);
        // 返回
        return edgeGateway.getId();
    }

    @Override
    public void updateEdgeGateway(EdgeGatewaySaveReqVO updateReqVO) {
        // 校验存在
        validateEdgeGatewayExists(updateReqVO.getId());
        // 更新
        EdgeGatewayDO updateObj = BeanUtils.toBean(updateReqVO, EdgeGatewayDO.class);
        edgeGatewayMapper.updateById(updateObj);
    }

    @Override
    public void deleteEdgeGateway(Long id) {
        // 校验存在
        validateEdgeGatewayExists(id);
        // 校验网关下是否存在设备
        validateDeviceExists(id);
        // 删除
        edgeGatewayMapper.deleteById(id);
    }

    private void validateEdgeGatewayExists(Long id) {
        if (edgeGatewayMapper.selectById(id) == null) {
            throw exception(EDGE_GATEWAY_NOT_EXISTS);
        }
    }

    private void validateDeviceExists(Long id) {
        EdgeGatewayDO gatewayDO = edgeGatewayMapper.selectById(id);
        if (deviceMapper.selectCountByEdgeCode(gatewayDO.getEdgeCode()) > 0) {
            throw exception(EDGE_GATEWAY_DEVICE_EXISTS);
        }
    }

    @Override
    public EdgeGatewayDO getEdgeGateway(Long id) {
        return Objects.isNull(id) ? null : edgeGatewayMapper.selectById(id);
    }

    @Override
    public PageResult<EdgeGatewayBO> getEdgeGatewayPage(EdgeGatewayPageReqVO pageReqVO) {
        boolean superAdmin = false;
        CommonResult<Map<Long, List<RoleRespDTO>>> commonResult = roleApi.userRoles(Collections.singleton(WebFrameworkUtils.getLoginUserId()));
        if (Objects.nonNull(commonResult) && !org.springframework.util.CollectionUtils.isEmpty(commonResult.getData())) {
            List<RoleRespDTO> roleList = commonResult.getData().get(WebFrameworkUtils.getLoginUserId());
            for (RoleRespDTO role : roleList) {
                if (List.of("tenant_admin", "admin").contains(role.getCode())) {
                    superAdmin = true;
                    break;
                }
            }
        }
        if (!superAdmin) {
            CommonResult<List<RolePageRespDTO>> result = permissionApi.getPermissionRoleByUserId(getLoginUserId());
            List<String> ids = new ArrayList<>();
            //判断响应是否成功
            if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(result.getCode())) {
                List<RolePageRespDTO> rolePageRespDTOList = result.getData();
                rolePageRespDTOList.stream().forEach(role -> {
                    //查询用户角色信息
                    RoleDataPermissionsDO roleDataPermissionsDO = roleDataPermissionsMapper.selectAllByRoleId(role.getId());
                    if (roleDataPermissionsDO != null) {
                        //判断是否是指定数据权限
                        if (DataScopeEnum.DEPT_CUSTOM.getScope().equals(roleDataPermissionsDO.getDataScope())) {
                            //查询所有资源空间
                            List<DataPermissionsDO> doList = dataPermissionsMapper.selectListByRoleId(roleDataPermissionsDO.getRoleId(), 1);
                            if (!CollectionUtils.isAnyEmpty(doList)) {
                                doList.stream().forEach(resource -> {
                                    List<DataPermissionsDO> doList1 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), resource.getDataId(), 6);
                                    if (!CollectionUtils.isAnyEmpty(doList1)) {
                                        doList1.stream().forEach(edge -> {
                                            List<DataPermissionsDO> doList2 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), edge.getDataId(), 7);
                                            if (!CollectionUtils.isAnyEmpty(doList2)) {
                                                //查询当前资源空间下的所有实例
                                                QueryWrapper<EdgeGatewayDO> queryWrappers = new QueryWrapper<>();
                                                queryWrappers.eq("resource_space_id", resource.getDataId());
                                                List<EdgeGatewayDO> edgeGatewayList = edgeGatewayMapper.selectList(queryWrappers);
                                                if (!CollectionUtils.isAnyEmpty(edgeGatewayList)) {
                                                    edgeGatewayList.forEach(item -> ids.add(item.getId().toString()));
                                                }
                                            } else {
                                                List<DataPermissionsDO> doList3 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), edge.getDataId(), 8);
                                                doList3.stream().forEach(item -> ids.add(item.getDataId().toString()));
                                            }
                                        });
                                    }
                                });
                            }
                        }
                    } else {
                        ids.add("-1");
                    }
                });
            }
            //如果资源空间下未关联产品，返回空
            if(org.springframework.util.CollectionUtils.isEmpty(ids)){
                return new PageResult<>(Collections.emptyList(), 0L);
            }
            pageReqVO.setCodes(ids);
        }
        PageResult<EdgeGatewayDO> doPageResult = edgeGatewayMapper.selectPage(pageReqVO);
        PageResult<EdgeGatewayBO> boPageResult = BeanUtils.toBean(doPageResult, EdgeGatewayBO.class);
        if (Objects.nonNull(boPageResult) && !org.springframework.util.CollectionUtils.isEmpty(boPageResult.getList())) {
            for (EdgeGatewayBO gatewayBO : boPageResult.getList()) {
                setSpaceName(gatewayBO);
            }
        }
        return boPageResult;
    }

    /**
     * 设置资源空间名称
     *
     * @param bO
     */
    private void setSpaceName(EdgeGatewayBO bO) {
        if (Objects.nonNull(bO) && Objects.nonNull(bO.getResourceSpaceId())) {
            if (Objects.equals(bO.getResourceSpaceId(), 0L)) {
                bO.setSpaceName("全部");
            } else {
                ResourceSpaceDO spaceDO = resourceSpaceService.getResourceSpace(bO.getResourceSpaceId());
                bO.setSpaceName(Objects.isNull(spaceDO) ? null : spaceDO.getSpaceName());
            }
        }
    }

    @Override
    public PageResult<EdgeGatewayDeviceDO> getEdgeGatewayDevicePage(EdgeGatewayPageReqVO pageReqVO) {
        return edgeGatewayMapper.selectJoinPage(pageReqVO);
    }

    @Override
    public void register(EdgeGatewayRegisterReqVO registerReqVO) {

        EdgeGatewayDO edgeGatewayDO = edgeGatewayMapper.selectByEdgeCode(registerReqVO.getEdgeCode());
        if (null == edgeGatewayDO || OnlineStatusEnum.OFFLINE.getType().equals(edgeGatewayDO.getEdgeStatus())) {
            log.warn("边缘网关处于离线状态，无需下发。网关编码【{}】，服务名【{}】", registerReqVO.getEdgeCode(), registerReqVO.getEdgeServiceName());
            return;
        }

        String lockKey = getLock(registerReqVO);

        QueryWrapper<DeviceDO> queryWrapper = new QueryWrapper<>();
        // 查询未注册的所有设备，包括网关与子设备
        queryWrapper.lambda().eq(DeviceDO::getDeleted, Boolean.FALSE)
                .eq(DeviceDO::getEdgeCode, registerReqVO.getEdgeCode())
                .eq(DeviceDO::getRegisterState, RegisterStateEnum.NO.getType());
        List<DeviceDO> deviceDOS = deviceMapper.selectList(queryWrapper);
        if (CollectionUtils.isAnyEmpty(deviceDOS)) {
            return;
        }
        List<DeviceDO> effectiveDOS = deviceDOS;
        if (Integer.valueOf(0).equals(registerReqVO.getScope())) {
            if (StringUtils.isBlank(registerReqVO.getDeviceCode())) {
                log.error("设备编码为空");
                return;
            }
            // 单条设备取网关设备自身和其子设备
            effectiveDOS = deviceDOS.stream()
                    .filter(item -> registerReqVO.getDeviceCode().equals(item.getDeviceCode())
                            || registerReqVO.getDeviceCode().equals(item.getParentCode())).collect(Collectors.toList());
        }

        // 网关设备映射，value只有一个元素
        Map<String, List<DeviceDO>> gwDeviceMap = fillDeviceMap(effectiveDOS, item -> StringUtils.isBlank(item.getParentCode()));
        // 网关子设备映射，<网关设备Code，List<子设备>>
        Map<String, List<DeviceDO>> subDeviceMap = fillDeviceMap(effectiveDOS, item -> StringUtils.isNotBlank(item.getParentCode()));
        // 转换为边缘网关需要的格式
        List<EdgeSyncDownDTO> edgeSyncDownDTOList = convertToEdgeSyncDownDTO(gwDeviceMap, subDeviceMap);
        if (CollectionUtils.isAnyEmpty(edgeSyncDownDTOList)) {
            return;
        }
        redisTemplate.opsForValue().increment(lockKey, gwDeviceMap.size());
        // 循环发送
        edgeSyncDownDTOList.forEach(item -> {
            if (log.isDebugEnabled()) {
                log.debug("消息内容为【{}】", JsonUtils.toJsonString(item));
            }
            item.setCurrentTime(System.currentTimeMillis());
            rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_CONFIG, item,IotTopicConstant.GROUP_DEVICE_CONFIG);
        });

    }

    @Override
    public String getEdgeServiceNameByCode(String edgeCode) {
        LambdaQueryWrapperX<EdgeGatewayDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.select(EdgeGatewayDO::getEdgeServiceName)
                .eq(EdgeGatewayDO::getEdgeCode, edgeCode);
        EdgeGatewayDO edgeGatewayDO = edgeGatewayMapper.selectOne(wrapperX);
        if (ObjectUtil.isNotNull(edgeGatewayDO)) {
            return edgeGatewayDO.getEdgeServiceName();
        } else {
            throw exception(EDGE_GATEWAY_NOT_EXISTS);
        }
    }

    @Override
    public void receiveRegister(EdgeSyncUpDTO edgeSyncUpDTO) {
        EdgeGatewayDO gatewayDO = edgeGatewayMapper.selectByEdgeCode(edgeSyncUpDTO.getNode());
        //如果是心跳，设置心跳时间
        if (Objects.nonNull(gatewayDO) && Objects.equals(edgeSyncUpDTO.getSyncType(), EdgeSyncTypeEnum.HEART.getType())) {
            gatewayDO.setHeartTime(LocalDateTime.now());
        }
        //如果是断开，更新状态
        else if (Objects.nonNull(gatewayDO) && Objects.equals(edgeSyncUpDTO.getSyncType(), EdgeSyncTypeEnum.OFF.getType())) {
            gatewayDO.setEdgeStatus(OnlineStatusEnum.OFFLINE.getType());
        }
        //如果是上线，更新信息
        else {
            gatewayDO = buildEdgeGatewayDO(edgeSyncUpDTO, Objects.nonNull(gatewayDO) ? gatewayDO.getId() : null);
        }
        int result;
        if (Objects.nonNull(gatewayDO.getId())) {
            result = edgeGatewayMapper.updateById(gatewayDO);
        } else {
            result = edgeGatewayMapper.insert(gatewayDO);
        }
        //如果是断开，需要设置当前边缘实例下的设备下线
        if (result > 0 && Objects.equals(edgeSyncUpDTO.getSyncType(), EdgeSyncTypeEnum.OFF.getType())) {
            deviceMapper.updateLinkStateByEdgeCode(gatewayDO.getEdgeCode(), OnlineStatusEnum.OFFLINE.getType());
        }
    }

    @Override
    public void edgeOnlineCheck(String edgeCode) {
        //查询边缘网关的设备在线状态，必须传网关code
        if (StringUtils.isBlank(edgeCode)) {
            return;
        }
        //查询在线边缘网关
        QueryWrapper<EdgeGatewayDO> queryWrapperEdge = new QueryWrapper<>();
        queryWrapperEdge.lambda().eq(EdgeGatewayDO::getDeleted, Boolean.FALSE)
                .eq(EdgeGatewayDO::getEdgeStatus, OnlineStatusEnum.ONLINE.getType());
        if (StringUtils.isNotBlank(edgeCode)) {
            queryWrapperEdge.lambda().eq(EdgeGatewayDO::getEdgeCode, edgeCode);
        }
        List<EdgeGatewayDO> edgeGatewayDOS = edgeGatewayMapper.selectList(queryWrapperEdge);
        if (!org.springframework.util.CollectionUtils.isEmpty(edgeGatewayDOS)) {
            for (EdgeGatewayDO edgeGatewayDO : edgeGatewayDOS) {
                //查询网关设备
                QueryWrapper<DeviceDO> queryWrapperDevice = new QueryWrapper<>();
                queryWrapperDevice.lambda().eq(DeviceDO::getDeleted, Boolean.FALSE)
                        .eq(DeviceDO::getEdgeCode, edgeGatewayDO.getEdgeCode())
                        .eq(DeviceDO::getRegisterState, RegisterStateEnum.YES.getType())
                        .eq(DeviceDO::getNodeType, NodeTypeEnum.EDGE.getType());
                List<DeviceDO> deviceDOS = deviceMapper.selectList(queryWrapperDevice);
                if (!org.springframework.util.CollectionUtils.isEmpty(deviceDOS)) {
                    for (DeviceDO deviceDO : deviceDOS) {
                        //查询网关子设备
                        QueryWrapper<DeviceDO> queryWrapperDeviceSub = new QueryWrapper<>();
                        queryWrapperDeviceSub.lambda().eq(DeviceDO::getDeleted, Boolean.FALSE)
                                .eq(DeviceDO::getEdgeCode, edgeGatewayDO.getEdgeCode())
                                .eq(DeviceDO::getRegisterState, RegisterStateEnum.YES.getType())
                                .eq(DeviceDO::getParentCode, deviceDO.getDeviceCode());
                        List<DeviceDO> deviceDOSSub = deviceMapper.selectList(queryWrapperDeviceSub);
                        // 转换为边缘网关需要的格式
                        EdgeOnlineCheckDTO edgeOnlineCheckDTO = convertToEdgeOnlineCheckDTO(deviceDO, deviceDOSSub);
                        if (log.isDebugEnabled()) {
                            log.debug("消息内容为【{}】", JsonUtils.toJsonString(edgeOnlineCheckDTO));
                        }
                        edgeOnlineCheckDTO.setCurrentTime(System.currentTimeMillis());
                        rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_ONLINE, edgeOnlineCheckDTO,  IotTopicConstant.GROUP_DEVICE_ONLINE);
                    }
                }
            }
        }
    }

    private Map<String, List<EdgeGatewayDeviceDO>> fillEdgeDeviceMap(List<EdgeGatewayDeviceDO> deviceDOList, Predicate<EdgeGatewayDeviceDO> predicate) {
        Map<String, List<EdgeGatewayDeviceDO>> gwDeviceMap = new HashMap<>(deviceDOList.size());
        deviceDOList.stream().filter(predicate).forEach(item -> {
            // 网关设备
            if (StringUtils.isBlank(item.getParentCode())) {
                if (gwDeviceMap.containsKey(item.getDeviceCode())) {
                    log.warn("设备号重复，deviceCode【{}】", item.getDeviceCode());
                } else {
                    List<EdgeGatewayDeviceDO> deviceDOS = new ArrayList<>();
                    deviceDOS.add(item);
                    gwDeviceMap.put(item.getDeviceCode(), deviceDOS);
                }
            } else {
                // 子设备
                if (gwDeviceMap.containsKey(item.getParentCode())) {
                    gwDeviceMap.get(item.getParentCode()).add(item);
                } else {
                    gwDeviceMap.put(item.getParentCode(), Lists.newArrayList(item));
                }
            }
        });
        return gwDeviceMap;
    }

    private List<EdgeOnlineCheckDTO> convertToEdgeOnlineCheckDTO(Map<String, List<EdgeGatewayDeviceDO>> gwDeviceMap, Map<String, List<EdgeGatewayDeviceDO>> subDeviceMap) {
        List<EdgeOnlineCheckDTO> result = new ArrayList<>();
        if (null == gwDeviceMap) {
            return result;
        }
        gwDeviceMap.forEach((k, v) -> {
            EdgeOnlineCheckDTO edgeOnlineCheckDTO = new EdgeOnlineCheckDTO();
            EdgeMcuDTO edgeMcuDTO = BeanUtils.toBean(v.get(0), EdgeMcuDTO.class);

            if (StringUtils.isNotBlank(v.get(0).getChannelCode())) {
                EdgeChannelDO channelDO = edgeChannelMapper.selectByCode(v.get(0).getChannelCode());
                if (channelDO != null) {
                    edgeMcuDTO.setExtra(channelDO.getExtra());
                }
            }
            edgeOnlineCheckDTO.setMcuDTO(edgeMcuDTO);
            edgeOnlineCheckDTO.setDeviceDTOList(buildEdgeDeviceDTO(subDeviceMap.get(k)));
            result.add(edgeOnlineCheckDTO);
        });

        return result;

    }

    /**
     * 转换为网关需要的格式
     *
     * @param wgDeviceDo
     * @param subDeviceDos
     * @return
     */
    private EdgeOnlineCheckDTO convertToEdgeOnlineCheckDTO(DeviceDO wgDeviceDo, List<DeviceDO> subDeviceDos) {
        EdgeOnlineCheckDTO edgeOnlineCheckDTO = new EdgeOnlineCheckDTO();
        EdgeMcuDTO edgeMcuDTO = BeanUtils.toBean(wgDeviceDo, EdgeMcuDTO.class);
        if (StringUtils.isNotBlank(wgDeviceDo.getChannelCode())) {
            EdgeChannelDO channelDO = edgeChannelMapper.selectByCode(wgDeviceDo.getChannelCode());
            if (channelDO != null) {
                edgeMcuDTO.setExtra(channelDO.getExtra());
                edgeMcuDTO.setConnectType(channelDO.getConnectType());
            }
        }
        edgeOnlineCheckDTO.setMcuDTO(edgeMcuDTO);
        edgeOnlineCheckDTO.setDeviceDTOList(buildEdgeDeviceCheckStatusDTO(subDeviceDos));
        return edgeOnlineCheckDTO;
    }

    private String getLock(EdgeGatewayRegisterReqVO registerReqVO) {
        String lockKey = "";
        String edgeKey = ApiConstants.EDGE_REGISTER_LOCK + registerReqVO.getScope() + ":" + registerReqVO.getEdgeCode();
        if (Integer.valueOf(1).equals(registerReqVO.getScope())) {
            if (redisTemplate.opsForValue().get(edgeKey) != null) {
                throw exception(EDGE_GATEWAY_ALL_REGISTER);
            }
            if (!redisTemplate.opsForValue().setIfAbsent(edgeKey, 0, 10 * 60, TimeUnit.SECONDS)) {
                throw exception(EDGE_GATEWAY_LOCK_FAILED);
            }
            lockKey = edgeKey;
        } else {
            if (redisTemplate.opsForValue().get(edgeKey) != null) {
                throw exception(EDGE_GATEWAY_ALL_REGISTER);
            }
            String key = ApiConstants.EDGE_REGISTER_LOCK + registerReqVO.getScope() + ":" + registerReqVO.getDeviceCode();
            if (redisTemplate.opsForValue().get(key) != null) {
                throw exception(EDGE_GATEWAY_SUB_REGISTER);
            }
            if (!redisTemplate.opsForValue().setIfAbsent(key, 0, 60, TimeUnit.SECONDS)) {
                throw exception(EDGE_GATEWAY_LOCK_FAILED);
            }
            lockKey = key;
        }

        return lockKey;
    }

    /**
     * 组装边缘网关数据格式
     *
     * @param edgeSyncUpDTO
     * @param id
     * @return
     */
    private EdgeGatewayDO buildEdgeGatewayDO(EdgeSyncUpDTO edgeSyncUpDTO, Long id) {
        return EdgeGatewayDO.builder()
                .id(id)
                .edgeCode(edgeSyncUpDTO.getNode())
                .edgeName(edgeSyncUpDTO.getName())
                .edgeServiceName(edgeSyncUpDTO.getService())
                .edgeHost(edgeSyncUpDTO.getHost())
                .edgePort(edgeSyncUpDTO.getPort())
                .description(edgeSyncUpDTO.getRemark())
                .edgeIdentifier(edgeSyncUpDTO.getMacAddr())
                .edgeStatus(OnlineStatusEnum.ONLINE.getType())
                .onlineTime(LocalDateTime.now())
                .heartTime(LocalDateTime.now())
                .resourceSpaceId(edgeSyncUpDTO.getResourceSpaceId())
                .build();
    }

    /**
     * 修改网关实例在线状态
     *
     * @param edgeCode
     * @param edgeStatus
     * @param changeTime
     * @return
     */
    @Override
    public Integer modifyEdgeGateWayEdgeStatus(String edgeCode, Integer edgeStatus, Date changeTime) {
        int result;
        if ((result = edgeGatewayMapper.updateEdgeGateWayEdgeStatus(edgeCode, edgeStatus, changeTime)) > 0) {
            //修改当前边缘实例下的网关设备和网关子设备下线
            deviceMapper.updateLinkStateByEdgeCode(edgeCode, edgeStatus);
            // 通知大坝设备在线离线
            List<DeviceDO> deviceDOS = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getEdgeCode, edgeCode));
            try {
                if (!org.springframework.util.CollectionUtils.isEmpty(deviceDOS)) {
                    //网关设备
                    List<DeviceDO> edgeDeviceList = deviceDOS.stream().filter(item -> NodeTypeEnum.EDGE.getType().equals(item.getNodeType())).toList();
                    if (!org.springframework.util.CollectionUtils.isEmpty(edgeDeviceList)) {
                        edgeDeviceList.forEach(item -> {
                            //子设备状态
                            List<DeviceCheckOnlineModel.ChildDevice> childDeviceList = new ArrayList<>();
                            List<DeviceDO> subDeviceList = deviceDOS.stream().filter(deviceDO -> item.getDeviceCode().equals(deviceDO.getParentCode())).toList();
                            subDeviceList.forEach(subDevice -> {
                                DeviceCheckOnlineModel.ChildDevice childDevice = new DeviceCheckOnlineModel.ChildDevice();
                                childDevice.setCheckResult(edgeStatus);
                                childDevice.setDeviceCode(subDevice.getDeviceCode());
                                childDeviceList.add(childDevice);
                            });

                            DeviceCheckOnlineModel deviceCheckOnlineModel = new DeviceCheckOnlineModel();
                            deviceCheckOnlineModel.setCheckResult(edgeStatus);
                            deviceCheckOnlineModel.setDeviceCode(item.getDeviceCode());
                            deviceCheckOnlineModel.setChildDeviceStatus(childDeviceList);
                            rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_STATUS_CHANGE,  deviceCheckOnlineModel,  IotTopicConstant.GROUP_DEVICE_STATUS_CHANGE);
                        });
                    }
                }
            } catch (Exception e) {
                log.error("send dam change device status error {}", e.getMessage());
            }
        }
        return result;
    }

    @Override
    public EdgeGatewayBO getEdgeGatewayBO(Long id) {
        EdgeGatewayDO gatewayDO = getEdgeGateway(id);
        EdgeGatewayBO gatewayBO = BeanUtils.toBean(gatewayDO, EdgeGatewayBO.class);
        setSpaceName(gatewayBO);
        return gatewayBO;
    }

    @Override
    public void detectEdgeGatewayOnlineStatus() {
        //5分钟没心跳则离线
        Date heartTimeNormal = DateUtils.addMinutes(new Date(), -5);
        List<EdgeGatewayDO> gatewayDOList = edgeGatewayMapper.selectList(new LambdaQueryWrapperX<EdgeGatewayDO>()
                .eq(EdgeGatewayDO::getEdgeStatus, OnlineStatusEnum.ONLINE.getType())
                .lt(EdgeGatewayDO::getHeartTime, heartTimeNormal));
        if (!org.springframework.util.CollectionUtils.isEmpty(gatewayDOList)) {
            gatewayDOList.forEach(item -> {
                modifyEdgeGateWayEdgeStatus(item.getEdgeCode(), OnlineStatusEnum.OFFLINE.getType(), new Date());
            });
        }
    }

    /**
     * 根据条件将设备进行映射
     *
     * @param deviceDOList 设备集合
     * @param predicate    断言
     * @return <deviceCode, List<DeviceDO>> -- deviceCode为网关设备编码，List为设备集合（网关设备时list.size为1）
     */
    private Map<String, List<DeviceDO>> fillDeviceMap(List<DeviceDO> deviceDOList, Predicate<DeviceDO> predicate) {
        Map<String, List<DeviceDO>> gwDeviceMap = new HashMap<>(deviceDOList.size());
        deviceDOList.stream().filter(predicate).forEach(item -> {
            // 网关设备
            if (StringUtils.isBlank(item.getParentCode())) {
                if (gwDeviceMap.containsKey(item.getDeviceCode())) {
                    log.warn("设备号重复，deviceCode【{}】", item.getDeviceCode());
                } else {
                    List<DeviceDO> deviceDOS = new ArrayList<>();
                    deviceDOS.add(item);
                    gwDeviceMap.put(item.getDeviceCode(), deviceDOS);
                }
            } else {
                // 子设备
                if (gwDeviceMap.containsKey(item.getParentCode())) {
                    gwDeviceMap.get(item.getParentCode()).add(item);
                } else {
                    gwDeviceMap.put(item.getParentCode(), Lists.newArrayList(item));
                }
            }
        });
        return gwDeviceMap;
    }

    private List<EdgeSyncDownDTO> convertToEdgeSyncDownDTO(Map<String, List<DeviceDO>> gwDeviceMap,
                                                           Map<String, List<DeviceDO>> subDeviceMap) {
        List<EdgeSyncDownDTO> result = new ArrayList<>();
        // 网关已注册，但子设备存在未注册情况
        if ((null == gwDeviceMap || gwDeviceMap.isEmpty()) && subDeviceMap != null && !subDeviceMap.isEmpty()) {
            subDeviceMap.forEach((k, v) -> {
                DeviceDO gwDevice = deviceMapper.selectByCode(k);
                if (null == gwDevice) {
                    return;
                }
                // gwDeviceMap不会为null
                gwDeviceMap.put(k, Lists.newArrayList(gwDevice));
            });
        }
        if (null == gwDeviceMap) {
            return result;
        }
        gwDeviceMap.forEach((k, v) -> {
            EdgeSyncDownDTO syncDownDTO = new EdgeSyncDownDTO();
            syncDownDTO.setMcuDTO(edgeMcuDTO(v.get(0)));
            syncDownDTO.setDeviceDTOList(edgeDeviceDTO(subDeviceMap.get(k)));

            result.add(syncDownDTO);
        });

        return result;

    }

    private List<EdgeDeviceDTO> buildEdgeDeviceDTO(List<EdgeGatewayDeviceDO> deviceDOList) {
        List<EdgeDeviceDTO> result = new ArrayList<>();

        if (CollectionUtils.isAnyEmpty(deviceDOList)) {
            return result;
        }
        deviceDOList.forEach(item -> {
            EdgeDeviceDTO deviceDTO = new EdgeDeviceDTO();
            deviceDTO.setDeviceCode(item.getDeviceCode());
            deviceDTO.setMcuChannel(item.getMcuChannel());
            deviceDTO.setExtra(item.getExtra());
            result.add(deviceDTO);
        });

        return result;
    }

    /**
     * 构建边缘网关设备列表
     *
     * @param deviceDOList
     * @return
     */
    private List<EdgeDeviceDTO> buildEdgeDeviceCheckStatusDTO(List<DeviceDO> deviceDOList) {
        List<EdgeDeviceDTO> result = new ArrayList<>();
        if (CollectionUtils.isAnyEmpty(deviceDOList)) {
            return result;
        }
        deviceDOList.forEach(item -> {
            EdgeDeviceDTO deviceDTO = new EdgeDeviceDTO();
            deviceDTO.setDeviceCode(item.getDeviceCode());
            deviceDTO.setMcuChannel(item.getMcuChannel());
            deviceDTO.setExtra(item.getExtra());
            result.add(deviceDTO);
        });
        return result;
    }

    private EdgeMcuDTO edgeMcuDTO(DeviceDO deviceDO) {
        if (StringUtils.isBlank(deviceDO.getChannelCode())) {
            return BeanUtils.toBean(deviceDO, EdgeMcuDTO.class);
        }
        EdgeMcuDTO result = BeanUtils.toBean(deviceDO, EdgeMcuDTO.class);
        EdgeChannelDO channelDO = edgeChannelMapper.selectByCode(deviceDO.getChannelCode());

        if (channelDO != null) {
            result.setExtra(channelDO.getExtra());
            result.setConnectType(channelDO.getConnectType());
        }

        return result;
    }

    private List<EdgeDeviceDTO> edgeDeviceDTO(List<DeviceDO> deviceDOList) {
        List<EdgeDeviceDTO> result = new ArrayList<>();

        if (CollectionUtils.isAnyEmpty(deviceDOList)) {
            return result;
        }
        deviceDOList.forEach(item -> {
            EdgeDeviceDTO deviceDTO = new EdgeDeviceDTO();
            deviceDTO.setDeviceCode(item.getDeviceCode());
            deviceDTO.setMcuChannel(item.getMcuChannel());
            deviceDTO.setExtra(item.getExtra());
            result.add(deviceDTO);
        });

        return result;
    }

}