package cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ProductRelayVO {

    private Long id;

    private String productName;

    private String productCode;

    private String productModel;

    private String firmName;

    private String description;

    private Integer nodeType;

    private String protocolCode;

    private String networkMethod;

    private String dataFormat;

    private Integer productState;

    private String productSecret;

    private LocalDateTime createTime;

    private Long resourceSpaceId;

    private List<ProductModelRelayVO> productModelList;

}

