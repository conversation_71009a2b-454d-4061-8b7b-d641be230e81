package cn.powerchina.bjy.link.iot.service.transporttarget;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.DeviceAttributeVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.DeviceRelayVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.DeviceStatusChangeVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.ProductRelayVO;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transporttarget.TransportTargetDO;
import cn.powerchina.bjy.link.iot.dal.mysql.transporttarget.TransportTargetMapper;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TransportTypeEnum;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.TRANSPORT_TARGET_NOT_EXISTS;

/**
 * 转发规则-转发目标 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TransportTargetServiceImpl implements TransportTargetService {

    @Resource
    private TransportTargetMapper transportTargetMapper;

    private final CloseableHttpClient httpClient;

    @Autowired
    public TransportTargetServiceImpl(CloseableHttpClient httpClient) {
        this.httpClient = httpClient;
    }

    @Override
    public Long createTransportTarget(TransportTargetSaveReqVO createReqVO) {
        // 插入
        TransportTargetDO transportTarget = BeanUtils.toBean(createReqVO, TransportTargetDO.class);
        transportTargetMapper.insert(transportTarget);
        // 返回
        return transportTarget.getId();
    }

    @Override
    public void updateTransportTarget(TransportTargetSaveReqVO updateReqVO) {
        // 校验存在
        validateTransportTargetExists(updateReqVO.getId());
        // 更新
        TransportTargetDO updateObj = BeanUtils.toBean(updateReqVO, TransportTargetDO.class);
        transportTargetMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransportTarget(Long id) {
        // 校验存在
        validateTransportTargetExists(id);
        // 删除
        transportTargetMapper.deleteById(id);
    }

    private void validateTransportTargetExists(Long id) {
        if (transportTargetMapper.selectById(id) == null) {
            throw exception(TRANSPORT_TARGET_NOT_EXISTS);
        }
    }

    @Override
    public TransportTargetDO getTransportTarget(Long id) {
        return transportTargetMapper.selectById(id);
    }

    @Override
    public PageResult<TransportTargetDO> getTransportTargetPage(TransportTargetPageReqVO pageReqVO) {
        return transportTargetMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TransportTargetDO> getTransportTargetListByRuleId(String ruleId) {
        LambdaQueryWrapperX<TransportTargetDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(TransportTargetDO::getRuleId, ruleId);
        return transportTargetMapper.selectList(wrapperX);
    }

    //产品数据转发
    @Override
    public void productDataTransportTarget(TransportSourceTypeEnum dataType, Long ruleId, ProductRelayVO productRelayVO) {
        Map<String, Object> productRelayMap = new HashMap<>();
        if (!TransportSourceTypeEnum.PRODUCT_DELETE.equals(dataType)) {
            productRelayMap.put("product", productRelayVO);
        } else {
            productRelayMap.put("productCode", productRelayVO.getProductCode());
        }
        LambdaQueryWrapperX<TransportTargetDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(TransportTargetDO::getRuleId, ruleId);
        List<TransportTargetDO> list = transportTargetMapper.selectList(wrapperX);
        for (TransportTargetDO transportTargetDO : list) {
            if (TransportTypeEnum.HTTP.getType().equals(transportTargetDO.getTransportType())) {
                //发送http请求
                try {
                    HttpPost httpPost = new HttpPost(transportTargetDO.getUrlAddress());
                    httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
                    httpPost.addHeader("Authorization", "Bearer " + transportTargetDO.getToken());
                    httpPost.setEntity(new StringEntity(JSON.toJSONString(productRelayMap), StandardCharsets.UTF_8));
                    CloseableHttpResponse response = httpClient.execute(httpPost);
                    String result = EntityUtils.toString(response.getEntity());
                    log.info("产品数据转发http请求发送成功 {}", result);
                } catch (Exception e) {
                    log.error("产品数据转发http请求发送失败 {}", e.getMessage());
                }
            }
            if (TransportTypeEnum.MQTT.getType().equals(transportTargetDO.getTransportType())) {
                //发送MQTT消息

            }
        }

    }

    //设备数据转发
    @Override
    public void deviceDataTransportTarget(Long ruleId, DeviceRelayVO deviceRelayVO) {
        LambdaQueryWrapperX<TransportTargetDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(TransportTargetDO::getRuleId, ruleId);
        List<TransportTargetDO> list = transportTargetMapper.selectList(wrapperX);
        for (TransportTargetDO transportTargetDO : list) {
            if (TransportTypeEnum.HTTP.getType().equals(transportTargetDO.getTransportType())) {
                //发送http请求
                try {
                    HttpPost httpPost = new HttpPost(transportTargetDO.getUrlAddress());
                    httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
                    httpPost.addHeader("Authorization", "Bearer " + transportTargetDO.getToken());
                    httpPost.addHeader("tenant-id", "100");
                    httpPost.setEntity(new StringEntity(JSON.toJSONString(deviceRelayVO), StandardCharsets.UTF_8));
                    CloseableHttpResponse response = httpClient.execute(httpPost);
                    String result = EntityUtils.toString(response.getEntity());
                    log.info("设备数据转发http请求发送成功 {}", result);
                } catch (Exception e) {
                    log.error("设备数据转发http请求发送失败 {}", e.getMessage());
                }
            }
            if (TransportTypeEnum.MQTT.getType().equals(transportTargetDO.getTransportType())) {
                //发送MQTT消息
            }
        }
    }

    //设备在线状态变更数据转发
    @Override
    public void deviceStatusChangeTransportTarget(Long ruleId, DeviceStatusChangeVO deviceStatusChangeVO) {
        LambdaQueryWrapperX<TransportTargetDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(TransportTargetDO::getRuleId, ruleId);
        List<TransportTargetDO> list = transportTargetMapper.selectList(wrapperX);
        for (TransportTargetDO transportTargetDO : list) {
            if (TransportTypeEnum.HTTP.getType().equals(transportTargetDO.getTransportType())) {
                //发送http请求
                try {
                    HttpPost httpPost = new HttpPost(transportTargetDO.getUrlAddress());
                    httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
                    httpPost.addHeader("Authorization", "Bearer " + transportTargetDO.getToken());
                    httpPost.setEntity(new StringEntity(JSON.toJSONString(deviceStatusChangeVO), StandardCharsets.UTF_8));
                    CloseableHttpResponse response = httpClient.execute(httpPost);
                    String result = EntityUtils.toString(response.getEntity());
                    log.info("设备在线状态变更数据转发http请求发送成功 {}", result);
                } catch (Exception e) {
                    log.error("设备在线状态变更数据转发http请求发送失败 {}", e.getMessage());
                }
            }
            if (TransportTypeEnum.MQTT.getType().equals(transportTargetDO.getTransportType())) {
                //发送MQTT消息
            }
        }
    }

    //设备属性上报转发
    @Override
    public void deviceAttributeTransportTarget(Long ruleId, DeviceAttributeVO deviceAttributeVO) {
        LambdaQueryWrapperX<TransportTargetDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(TransportTargetDO::getRuleId, ruleId);
        List<TransportTargetDO> list = transportTargetMapper.selectList(wrapperX);
        for (TransportTargetDO transportTargetDO : list) {
            if (TransportTypeEnum.HTTP.getType().equals(transportTargetDO.getTransportType())) {
                //发送http请求
                try {
                    HttpPost httpPost = new HttpPost(transportTargetDO.getUrlAddress());
                    httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
                    httpPost.addHeader("Authorization", "Bearer " + transportTargetDO.getToken());
                    httpPost.setEntity(new StringEntity(JSON.toJSONString(deviceAttributeVO), StandardCharsets.UTF_8));
                    CloseableHttpResponse response = httpClient.execute(httpPost);
                    String result = EntityUtils.toString(response.getEntity());
                    log.info("设备属性上报数据转发http请求发送成功 {}", result);
                } catch (Exception e) {
                    log.error("设备属性上报数据转发http请求发送失败 {}", e.getMessage());
                }
            }
            if (TransportTypeEnum.MQTT.getType().equals(transportTargetDO.getTransportType())) {
                //发送MQTT消息
            }
        }
    }

}