package cn.powerchina.bjy.link.iot.listener.device;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import cn.powerchina.bjy.link.iot.dto.message.DeviceStatusModel;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.framework.rule.RuleActionService;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicecurrentattribute.DeviceShadowService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 监听设备上报的消息
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/16
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_DEVICE_CHANGE, consumerGroup = IotTopicConstant.GROUP_DEVICE_CHANGE)
public class DeviceStatusChangeReceiver implements RocketMQListener {

    @Resource
    private RuleActionService ruleActionService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private DeviceShadowService deviceShadowService;


    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        // 解析消息体
        DeviceStatusModel deviceStatusModel = parseMessageBody(messageView);
        if (deviceStatusModel == null) {
            log.error("MessageView is null, skip consumption");
            return ConsumeResult.SUCCESS;
        }
        log.info("receive message: {}", JSONObject.toJSON(deviceStatusModel));

        //变更设备状态
        DeviceDO device = deviceService.getDevice(deviceStatusModel.getDeviceCode());
        if (device != null) {
            device.setLinkState(deviceStatusModel.getStatus());
            if (deviceStatusModel.getStatus() == 1) {
                device.setLastUpTime(LocalDateTime.now());
            }
            deviceService.updateDevice(BeanUtils.toBean(device, DeviceSaveReqVO.class));
            //更新设备影子
            LocalDateTime reportTime = deviceStatusModel.getCurrentTime() == null ? LocalDateTime.now() : Instant.ofEpochMilli(deviceStatusModel.getCurrentTime())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            List<DeviceShadowDO> shadowDOList = new ArrayList<>();
            DeviceShadowDO deviceShadowDO = deviceShadowService.getShadowByDeviceCodeAndThingIdentity(deviceStatusModel.getDeviceCode(), "status");
            if (deviceShadowDO == null) {
                deviceShadowDO = new DeviceShadowDO();
                deviceShadowDO.setDeviceCode(deviceStatusModel.getDeviceCode());
                deviceShadowDO.setProductCode(deviceStatusModel.getProductCode());
                deviceShadowDO.setReportTime(reportTime);
                deviceShadowDO.setThingIdentity("status");
                deviceShadowDO.setThingValue(String.valueOf(deviceStatusModel.getStatus()));
                shadowDOList.add(deviceShadowDO);
                deviceShadowService.createShadow(shadowDOList);
            } else {
                deviceShadowDO.setReportTime(reportTime);
                deviceShadowDO.setThingValue(String.valueOf(deviceStatusModel.getStatus()));
                shadowDOList.add(deviceShadowDO);
                deviceShadowService.updateShadow(shadowDOList);
            }
        }
        try {
            Facts facts = new Facts();
            facts.put("productCode", deviceStatusModel.getProductCode());
            facts.put("deviceCode", deviceStatusModel.getDeviceCode());
            facts.put("deviceStatus", deviceStatusModel.getStatus());
            ruleActionService.matchRuleAndAction(facts);
        } catch (Exception e) {
            log.error("根据上报的属性，匹配规则异常：{}", e.getMessage());
        }

        //设备在线状态变更


        return ConsumeResult.SUCCESS;
    }


    /**
     * 解析消息体为实体类
     */
    private DeviceStatusModel parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, DeviceStatusModel.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
