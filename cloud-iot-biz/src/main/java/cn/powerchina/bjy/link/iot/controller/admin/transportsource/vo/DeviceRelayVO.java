package cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class DeviceRelayVO {

    private Long id;

    private String productCode;

    private String projectCode;

    private String deviceName;

    private String deviceCode;

    private String parentCode;

    private String parentName;

    private String deviceSerial;

    private Boolean shadow;

    private String channelCode;

    private String mcuChannel;

    private String slaveId;

    private Integer distributeState;

    private Integer linkState;

    private Integer registerState;

    private Integer nodeType;

    private String extra;

    private LocalDateTime lastUpTime;

    private LocalDateTime createTime;

    private String edgeCode;

    private String driverCode;

    private String productName;

    private String remark;

    private String firmName;

    private String productModel;

    private Long resourceSpaceId;

    private String spaceName;

    private String longitude;

    private String latitude;
}