package cn.powerchina.bjy.link.iot.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.dto.up.EdgeWritePropertyValue;
import cn.powerchina.bjy.link.iot.enums.CommandServeEnum;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.enums.StatisticImageTypeEnum;
import cn.powerchina.bjy.link.iot.model.DeviceCommandModel;
import cn.powerchina.bjy.link.iot.model.EdgeResultModel;
import cn.powerchina.bjy.link.iot.service.devicepropertylog.DevicePropertyLogService;
import cn.powerchina.bjy.link.iot.service.instructiondownlog.InstructionDownLogService;
import cn.powerchina.bjy.link.iot.service.messagestatisticday.MessageStatisticDayService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_DEVICE_COMMAND_RESULT, consumerGroup = IotTopicConstant.GROUP_DEVICE_COMMAND_RESULT)
public class CommandReceiver implements RocketMQListener {

    @Resource
    private InstructionDownLogService instructionDownLogService;

    @Resource
    private DevicePropertyLogService propertyLogService;

    @Resource
    private MessageStatisticDayService messageStatisticDayService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            EdgeWritePropertyValue writePropertyValue = parseMessageBody(messageView);
            if (writePropertyValue == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            log.info("receive dam command: {}", JSONObject.toJSON(writePropertyValue));
            //计算上行时间
            int upConsumeTime = Math.toIntExact(System.currentTimeMillis() - writePropertyValue.getCurrentTime());
            Integer downConsumeTime = writePropertyValue.getDownConsumeTime();
            //服务器时间可能有差距   为负时取反
            downConsumeTime = downConsumeTime < 0 ? -downConsumeTime : downConsumeTime;
            upConsumeTime = upConsumeTime < 0 ? -upConsumeTime : upConsumeTime;
            //如果是收集通道值
            if (writePropertyValue.getThingIdentity().equals(CommandServeEnum.CHANNEL_VALUE.getThingIdentity())) {
                EdgeResultModel resultModel = JsonUtils.parseObject(writePropertyValue.getOutputParams(), EdgeResultModel.class);
                List<EdgeReadPropertyValue.EdgeDevicePropertyValueDTO> propertyValueList = JsonUtils.parseArray(resultModel.getData(), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO.class);
                propertyLogService.savePropertyLog(propertyValueList, null);
            }
            //插入操作记录日志
            instructionDownLogService.updateInstructionLogByMsgId(writePropertyValue.getMessageId(),
                    JsonUtils.toJsonString(writePropertyValue.getOutputParams()),
                    downConsumeTime,
                    upConsumeTime);
            messageStatisticDayService.insertMessageStatisticDay(new Date(), StatisticImageTypeEnum.MESSAGE.getType(), 1L);
        } catch (Exception e) {
            log.error("指令下发属性解析异常--->error,entityDTO={}", JsonUtils.toJsonString(messageView), e);
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 解析消息体为实体类
     */
    private EdgeWritePropertyValue parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, EdgeWritePropertyValue.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
