package cn.powerchina.bjy.link.iot.service.sceneruleaction;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruleaction.SceneRuleActionDO;
import cn.powerchina.bjy.link.iot.dal.mysql.sceneruleaction.SceneRuleActionMapper;
import cn.powerchina.bjy.link.iot.enums.ActionTypeEnum;
import cn.powerchina.bjy.link.iot.service.alarmtemplate.AlarmTemplateService;
import cn.powerchina.bjy.link.iot.util.SnowFlakeUtil;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.SCENE_RULE_ACTION_NOT_EXISTS;

/**
 * 场景规则执行动作 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SceneRuleActionServiceImpl implements SceneRuleActionService {

    @Resource
    private SceneRuleActionMapper sceneRuleActionMapper;

    @Resource
    private AlarmTemplateService alarmTemplateService;

    @Override
    public void createAndUpdateSceneRuleAction(List<SceneRuleActionSaveReqVO> saveOrUpdateList, Long ruleId) {
        List<Long> oldIdList = new ArrayList<>();
        List<SceneRuleActionDO> oldEntityList = getSceneRuleActionByRuleId(ruleId);
        if (!CollectionUtils.isEmpty(oldEntityList)) {
            oldIdList = oldEntityList.stream().map(SceneRuleActionDO::getId).collect(Collectors.toList());
        }
        //saveOrUpdateList为空，oldIdList不为空时，说明是全部删除了。将所属执行动作全部删除
        if (CollectionUtil.isEmpty(saveOrUpdateList) && !CollectionUtils.isEmpty(oldIdList)) {
            sceneRuleActionMapper.deleteBatchIds(oldIdList);
            return;
        }

        for (SceneRuleActionSaveReqVO ruleActionSaveReqVO : saveOrUpdateList) {
            ruleActionSaveReqVO.setRuleId(ruleId);
            //设备动作
            if (ActionTypeEnum.DEVICE_TYPE.getType().equals(ruleActionSaveReqVO.getActionType())) {
                ruleActionSaveReqVO.setCommandConfig(JSONObject.toJSONString(ruleActionSaveReqVO.getCommandList()));
            }
            Long actionId = ruleActionSaveReqVO.getId();
            if (Objects.isNull(ruleActionSaveReqVO.getId())) {
                actionId = createSceneRuleAction(ruleActionSaveReqVO);
            } else {
                updateSceneRuleAction(ruleActionSaveReqVO);
                oldIdList.remove(ruleActionSaveReqVO.getId());
            }
            //判断是否是告警模板
            if (ActionTypeEnum.ALARM_TYPE.getType().equals(ruleActionSaveReqVO.getActionType())) {
                alarmTemplateService.createAndUpdateAlarmTemplate(ruleActionSaveReqVO.getAlarmTemplateSave(), ruleId, actionId);
            }
        }
        //删除
        if (!CollectionUtils.isEmpty(oldIdList)) {
            sceneRuleActionMapper.deleteBatchIds(oldIdList);
        }
    }

    @Override
    public List<SceneRuleActionDO> getSceneRuleActionByRuleId(Long ruleId) {
        return sceneRuleActionMapper.selectList(new LambdaQueryWrapperX<SceneRuleActionDO>().
                eq(SceneRuleActionDO::getRuleId, ruleId)
                .orderByAsc(SceneRuleActionDO::getSort));
    }

    @Override
    public List<SceneRuleActionDO> getRuleActionBySceneId(Long sceneId) {
        return sceneRuleActionMapper.selectList(new LambdaQueryWrapperX<SceneRuleActionDO>().
                eq(SceneRuleActionDO::getSceneId, sceneId));
    }

    @Override
    public Long createSceneRuleAction(SceneRuleActionSaveReqVO createReqVO) {
        // 插入
        SceneRuleActionDO sceneRuleAction = BeanUtils.toBean(createReqVO, SceneRuleActionDO.class);
        sceneRuleActionMapper.insert(sceneRuleAction);
        // 返回
        return sceneRuleAction.getId();
    }

    @Override
    public void updateSceneRuleAction(SceneRuleActionSaveReqVO updateReqVO) {
        // 校验存在
        validateSceneRuleActionExists(updateReqVO.getId());
        // 更新
        SceneRuleActionDO updateObj = BeanUtils.toBean(updateReqVO, SceneRuleActionDO.class);
        sceneRuleActionMapper.updateById(updateObj);
    }

    @Override
    public void deleteSceneRuleAction(Long id) {
        // 校验存在
        validateSceneRuleActionExists(id);
        // 删除
        sceneRuleActionMapper.deleteById(id);
    }

    private void validateSceneRuleActionExists(Long id) {
        if (sceneRuleActionMapper.selectById(id) == null) {
            throw exception(SCENE_RULE_ACTION_NOT_EXISTS);
        }
    }

    @Override
    public SceneRuleActionDO getSceneRuleAction(Long id) {
        return sceneRuleActionMapper.selectById(id);
    }

    @Override
    public PageResult<SceneRuleActionDO> getSceneRuleActionPage(SceneRuleActionPageReqVO pageReqVO) {
        return sceneRuleActionMapper.selectPage(pageReqVO);
    }

}