package cn.powerchina.bjy.link.iot.service.transportsource;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsTreeVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.TransportSourcePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.TransportSourceRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.TransportSourceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportsource.TransportSourceDO;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 转发规则-数据源 Service 接口
 *
 * <AUTHOR>
 */
public interface TransportSourceService {

    /**
     * 创建转发规则-数据源
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransportSource(@Valid TransportSourceSaveReqVO createReqVO);

    /**
     * 更新转发规则-数据源
     *
     * @param updateReqVO 更新信息
     */
    void updateTransportSource(@Valid TransportSourceSaveReqVO updateReqVO);

    /**
     * 删除转发规则-数据源
     *
     * @param id 编号
     */
    void deleteTransportSource(Long id);

    /**
     * 获得转发规则-数据源
     *
     * @param id 编号
     * @return 转发规则-数据源
     */
    TransportSourceDO getTransportSource(Long id);

    /**
     * 获得转发规则-数据源分页
     *
     * @param pageReqVO 分页查询
     * @return 转发规则-数据源分页
     */
    PageResult<TransportSourceRespVO> getTransportSourcePage(TransportSourcePageReqVO pageReqVO);

    List<DataPermissionsTreeVO> getRangeResourceSpace();

    List<DataPermissionsTreeVO> getRangeProduct(Long resourceSpaceId);

    List<DataPermissionsTreeVO> getRangeDevice(String productCode);

    void dataForwarding(TransportSourceTypeEnum dataType, Object obj);

}