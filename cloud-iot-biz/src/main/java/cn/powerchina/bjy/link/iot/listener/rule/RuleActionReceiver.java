package cn.powerchina.bjy.link.iot.listener.rule;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.CommandVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.ParamsModelVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruleaction.SceneRuleActionDO;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.enums.ThingModeTypeEnum;
import cn.powerchina.bjy.link.iot.enums.device.IotDeviceMessageMethodEnum;
import cn.powerchina.bjy.link.iot.model.IotDeviceMessage;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.utils.IotDeviceMessageUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 描述
 * @Author: zhaoqiang
 * @CreateDate: 2025/6/10
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_RULE_ACTION_DELAY, consumerGroup = IotTopicConstant.GROUP_RULE_ACTION_DELAY)
public class RuleActionReceiver implements RocketMQListener {
    @Resource
    private RocketMQv5Client rocketMQv5Client;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
            return ConsumeResult.SUCCESS;
        }
        SceneRuleActionDO sceneRuleActionDO = parseMessageBody(messageView);
        if (sceneRuleActionDO == null) {
            log.error("MessageView is null, skip consumption");
            return ConsumeResult.SUCCESS;
        }
        log.info("topic:{},receiver message {}", IotTopicConstant.TOPIC_RULE_ACTION_DELAY, JSONObject.toJSON(sceneRuleActionDO));
        log.info("执行任务id:{}的参数{}", sceneRuleActionDO.getId(), sceneRuleActionDO.getCommandConfig());
        List<CommandVO> commandVOList = JSONObject.parseArray(sceneRuleActionDO.getCommandConfig(), CommandVO.class);
        if (CollectionUtil.isNotEmpty(commandVOList)) {
            commandVOList.forEach(item -> {
                IotDeviceMessage iotDeviceMessage = new IotDeviceMessage();
                iotDeviceMessage.setDeviceCode(sceneRuleActionDO.getDeviceCode());
                iotDeviceMessage.setThingIdentity(item.getCommandIdentity());
                Map<String, Object> params = new HashMap<>();
                if (CollectionUtil.isNotEmpty(item.getParamsConfig())) {
                    params = item.getParamsConfig().stream().collect(Collectors.toMap(ParamsModelVO::getThingIdentity, ParamsModelVO::getThingValue));
                }
                iotDeviceMessage.setParams(params);
                iotDeviceMessage.setRequestId(IotDeviceMessageUtils.generateMessageId());
                if (ThingModeTypeEnum.PROPERTY.getType() == item.getCommandType()) {
                    iotDeviceMessage.setMethod(IotDeviceMessageMethodEnum.PROPERTIES_SET.getMethod());
                }
                if (ThingModeTypeEnum.FUNCTION.getType() == item.getCommandType()) {
                    iotDeviceMessage.setMethod(IotDeviceMessageMethodEnum.SERVICE_INVOKE.getMethod());
                }
                rocketMQv5Client.syncSendFifoMessage(IotDeviceMessage.TOPIC_GATEWAY_DEVICE_MESSAGE, iotDeviceMessage, IotDeviceMessage.GROUP_IOT_DEVICE_MESSAGE);
            });
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 解析消息体为实体类
     */
    private SceneRuleActionDO parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, SceneRuleActionDO.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
