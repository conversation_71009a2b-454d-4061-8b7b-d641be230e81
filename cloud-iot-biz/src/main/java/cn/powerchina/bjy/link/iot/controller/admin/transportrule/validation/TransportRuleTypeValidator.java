package cn.powerchina.bjy.link.iot.controller.admin.transportrule.validation;

import cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo.TransportRuleSaveReqVO;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.util.StringUtils;

public class TransportRuleTypeValidator implements ConstraintValidator<TransportRuleTypeValid, TransportRuleSaveReqVO> {


    @Override
    public void initialize(TransportRuleTypeValid constraintAnnotation) {
    }

    @Override
    public boolean isValid(TransportRuleSaveReqVO value, ConstraintValidatorContext context) {
        /*Integer transportType = value.getTransportType();
        if (transportType == null) {
            return false;
        }

        // HTTP推送
        if (transportType == 1) {
            if (!StringUtils.hasText(value.getTransportUrl())) {
                addConstraintViolation(context, "HTTP推送时，推送URL不能为空");
                return false;
            }
            if (!StringUtils.hasText(value.getTransportToken())) {
                addConstraintViolation(context, "HTTP推送时，token不能为空");
                return false;
            }
        }

        // MQTT推送
        if (transportType == 2) {
            if (!StringUtils.hasText(value.getTopic())) {
                addConstraintViolation(context, "MQTT推送时，topic不能为空");
                return false;
            }
        }*/

        return true;
    }

    private void addConstraintViolation(ConstraintValidatorContext context, String message) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message)
                .addConstraintViolation();
    }
}