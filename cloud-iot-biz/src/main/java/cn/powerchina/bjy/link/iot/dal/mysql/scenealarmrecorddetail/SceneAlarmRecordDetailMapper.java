package cn.powerchina.bjy.link.iot.dal.mysql.scenealarmrecorddetail;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecorddetail.SceneAlarmRecordDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 告警记录详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneAlarmRecordDetailMapper extends BaseMapperX<SceneAlarmRecordDetailDO> {

    default PageResult<SceneAlarmRecordDetailDO> selectPage(SceneAlarmRecordDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SceneAlarmRecordDetailDO>()
                .eqIfPresent(SceneAlarmRecordDetailDO::getAlarmId, reqVO.getAlarmId())
                .eqIfPresent(SceneAlarmRecordDetailDO::getRuleId, reqVO.getRuleId())
                .betweenIfPresent(SceneAlarmRecordDetailDO::getAlarmTime, reqVO.getAlarmTime())
                .likeIfPresent(SceneAlarmRecordDetailDO::getDeviceName, reqVO.getDeviceName())
                .likeIfPresent(SceneAlarmRecordDetailDO::getTriggerCondition, reqVO.getTriggerCondition())
                .eqIfPresent(SceneAlarmRecordDetailDO::getAttributeValue, reqVO.getAttributeValue())
                .betweenIfPresent(SceneAlarmRecordDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SceneAlarmRecordDetailDO::getId));
    }

}