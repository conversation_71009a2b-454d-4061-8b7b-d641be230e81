package cn.powerchina.bjy.link.iot.service.alarmtemplate;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmMsgRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplatePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplateSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.alarmtemplate.AlarmTemplateDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationconfig.NotificationConfigDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod.NotificationMethodDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord.SceneAlarmRecordDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletrigger.SceneRuleTriggerDO;
import cn.powerchina.bjy.link.iot.dal.mysql.alarmtemplate.AlarmTemplateMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.sceneruletrigger.SceneRuleTriggerMapper;
import cn.powerchina.bjy.link.iot.enums.AlarmLevelEnum;
import cn.powerchina.bjy.link.iot.enums.AlarmStatusEnum;
import cn.powerchina.bjy.link.iot.enums.DeviceTriggerTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TriggerTypeEnum;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.notificationconfig.NotificationConfigService;
import cn.powerchina.bjy.link.iot.service.notificationmethod.NotificationMethodService;
import cn.powerchina.bjy.link.iot.service.notificationrecord.NotificationRecordService;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import cn.powerchina.bjy.link.iot.service.scenealarmrecord.SceneAlarmRecordService;
import cn.powerchina.bjy.link.iot.service.scenealarmrecorddetail.SceneAlarmRecordDetailService;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import cn.powerchina.bjy.link.iot.util.DingTalkUtils;
import cn.powerchina.bjy.link.iot.util.EmailSendUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.ALARM_TEMPLATE_NOT_EXISTS;

/**
 * 告警模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AlarmTemplateServiceImpl implements AlarmTemplateService {

    @Resource
    private AlarmTemplateMapper alarmTemplateMapper;

    @Resource
    private SceneRuleTriggerMapper sceneRuleTriggerMapper;

    @Resource
    private NotificationMethodService notificationMethodService;

    @Autowired
    @Lazy
    private SceneRuleService sceneRuleService;

    @Resource
    private SceneAlarmRecordService sceneAlarmRecordService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private ProductService productService;
    @Resource
    private SceneAlarmRecordDetailService sceneAlarmRecordDetailService;
    @Resource
    private ProductModelService productModelService;

    @Resource
    private NotificationConfigService notificationConfigService;

    @Resource
    private NotificationRecordService notificationRecordService;

    @Override
    public void createAndUpdateAlarmTemplate(AlarmTemplateSaveReqVO alarmTemplateSaveReqVO, Long ruleId, Long actionId) {
        alarmTemplateSaveReqVO.setRuleId(ruleId);
        alarmTemplateSaveReqVO.setActionId(actionId);

        Long alarmTemplateId = alarmTemplateSaveReqVO.getId();
        if (Objects.isNull(alarmTemplateSaveReqVO.getId())) {
            alarmTemplateId = createAlarmTemplate(alarmTemplateSaveReqVO);
        } else {
            updateAlarmTemplate(alarmTemplateSaveReqVO);
        }
        notificationMethodService.createAndUpdateNotificationMethod(alarmTemplateSaveReqVO.getNotificationMethodSaveReqVOList(), alarmTemplateId);
    }

    @Override
    public List<AlarmTemplateDO> getAlarmTemplateByRuleId(Long ruleId) {
        return alarmTemplateMapper.selectList(new LambdaQueryWrapperX<AlarmTemplateDO>().
                eq(AlarmTemplateDO::getRuleId, ruleId)
                .orderByAsc(AlarmTemplateDO::getSort));
    }

    @Override
    public AlarmTemplateDO getAlarmTemplateByActionId(Long actionId) {
        return alarmTemplateMapper.selectOne(new LambdaQueryWrapperX<AlarmTemplateDO>().
                eq(AlarmTemplateDO::getActionId, actionId));
    }

    @Override
    public AlarmTemplateSaveReqVO getTemplateAndNotificationByActionId(Long actionId) {
        AlarmTemplateDO alarmTemplateDO = getAlarmTemplateByActionId(actionId);
        AlarmTemplateSaveReqVO alarmTemplateSaveReqVO = BeanUtils.toBean(alarmTemplateDO, AlarmTemplateSaveReqVO.class);
        List<NotificationMethodDO> notificationMethodDOList = notificationMethodService.getNotificationMethodByAlarmTemplateId(alarmTemplateDO.getId());
        alarmTemplateSaveReqVO.setNotificationMethodSaveReqVOList(BeanUtils.toBean(notificationMethodDOList, NotificationMethodSaveReqVO.class));
        return alarmTemplateSaveReqVO;
    }

    @Override
    public void sendAlarm(Long actionId, String deviceCode) {
        AlarmTemplateDO alarmTemplateDO = getAlarmTemplateByActionId(actionId);
        if (null != alarmTemplateDO) {
            AlarmMsgRespVO alarmMsgRespVO = buildAlarmMsg(alarmTemplateDO, deviceCode);
            List<NotificationMethodDO> notificationMethodDOList = notificationMethodService.getNotificationMethodByAlarmTemplateId(alarmTemplateDO.getId());
            List<NotificationRecordSaveReqVO> allMsgRecordList = new ArrayList<>();
            //获取消息配置
            NotificationConfigDO notificationConfig = notificationConfigService.getNotificationConfigList().get(0);
            if (CollectionUtil.isNotEmpty(notificationMethodDOList)) {
                notificationMethodDOList.forEach(item -> {
                    switch (item.getNotificationMethod()) {
                        case 1:
                            DingTalkUtils.send(item, alarmMsgRespVO);
                            break;
                        case 2:
                            allMsgRecordList.addAll(EmailSendUtils.send(item, notificationConfig, alarmMsgRespVO));
                            break;
                        case 3:
                            //短信
                            break;
                        default:
                    }
                });
            }
            //生成告警记录
            createAlarmRecord(alarmTemplateDO, deviceCode, allMsgRecordList);
        }
    }

    @Override
    public List<AlarmTemplateSaveReqVO> getAlarmTemplateAndNotificationByRuleId(Long ruleId) {
        List<AlarmTemplateSaveReqVO> alarmTemplateSaveReqVOList = new ArrayList<>();
        List<AlarmTemplateDO> templateList = getAlarmTemplateByRuleId(ruleId);
        if (!CollectionUtils.isEmpty(templateList)) {
            templateList.forEach(item -> {
                AlarmTemplateSaveReqVO alarmTemplateSaveReqVO = BeanUtils.toBean(item, AlarmTemplateSaveReqVO.class);
                List<NotificationMethodDO> notificationMethodDOList = notificationMethodService.getNotificationMethodByAlarmTemplateId(item.getId());
                if (!CollectionUtils.isEmpty(notificationMethodDOList)) {
                    alarmTemplateSaveReqVO.setNotificationMethodSaveReqVOList(BeanUtils.toBean(notificationMethodDOList, NotificationMethodSaveReqVO.class));
                }
                alarmTemplateSaveReqVOList.add(alarmTemplateSaveReqVO);
            });
        }
        return alarmTemplateSaveReqVOList;
    }

    @Override
    public Long createAlarmTemplate(AlarmTemplateSaveReqVO createReqVO) {
        // 插入
        AlarmTemplateDO alarmTemplate = BeanUtils.toBean(createReqVO, AlarmTemplateDO.class);
        alarmTemplateMapper.insert(alarmTemplate);
        // 返回
        return alarmTemplate.getId();
    }

    @Override
    public void updateAlarmTemplate(AlarmTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateAlarmTemplateExists(updateReqVO.getId());
        // 更新
        AlarmTemplateDO updateObj = BeanUtils.toBean(updateReqVO, AlarmTemplateDO.class);
        alarmTemplateMapper.updateById(updateObj);
    }

    @Override
    public void deleteAlarmTemplate(Long id) {
        // 校验存在
        validateAlarmTemplateExists(id);
        // 删除
        alarmTemplateMapper.deleteById(id);
    }

    private AlarmTemplateDO validateAlarmTemplateExists(Long id) {
        AlarmTemplateDO alarmTemplateDO = alarmTemplateMapper.selectById(id);
        if (alarmTemplateDO == null) {
            throw exception(ALARM_TEMPLATE_NOT_EXISTS);
        }
        return alarmTemplateDO;
    }

    @Override
    public AlarmTemplateDO getAlarmTemplate(Long id) {
        return alarmTemplateMapper.selectById(id);
    }

    @Override
    public PageResult<AlarmTemplateDO> getAlarmTemplatePage(AlarmTemplatePageReqVO pageReqVO) {
        return alarmTemplateMapper.selectPage(pageReqVO);
    }

    private AlarmMsgRespVO buildAlarmMsg(AlarmTemplateDO alarmTemplateDO, String deviceCode) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SceneRuleDO sceneRuleDO = sceneRuleService.getSceneRule(alarmTemplateDO.getRuleId());
        AlarmMsgRespVO alarmMsgRespVO = new AlarmMsgRespVO();
        alarmMsgRespVO.setTriggerTime(sdf.format(new Date()));//告警时间
        String alarmLeve = AlarmLevelEnum.getByCode(alarmTemplateDO.getAlarmLevel()).getDesc();
        alarmMsgRespVO.setAlarmLevel(alarmLeve);//告警级别
        alarmMsgRespVO.setRuleName(sceneRuleDO.getRuleName());//触发规则
        String context = alarmTemplateDO.getAlarmName() + "：" + alarmTemplateDO.getAlarmContent();
        alarmMsgRespVO.setAlarmName(alarmTemplateDO.getAlarmName());
        alarmMsgRespVO.setAlarmContent(context);//告警内容
        Optional.ofNullable(deviceCode)
                .filter(StringUtils::isNotBlank)
                .map(deviceService::getDeviceByCode)
                .ifPresent(device -> {
                    alarmMsgRespVO.setDeviceName(device.getDeviceName());
                    alarmMsgRespVO.setDeviceCode(device.getDeviceCode());

                    Optional.ofNullable(device.getProductCode())
                            .map(productService::getProductBOByCode)
                            .ifPresent(product ->
                                    alarmMsgRespVO.setProductName(product.getProductName())
                            );
                });
        return alarmMsgRespVO;
    }

    private void createAlarmRecord(AlarmTemplateDO alarmTemplateDO, String deviceCode, List<NotificationRecordSaveReqVO> allMsgRecordList) {
        SceneRuleDO sceneRuleDO = sceneRuleService.getSceneRule(alarmTemplateDO.getRuleId());

        //相同规则触发状态的告警，不再产生相同告警，待验证告警改为触发，告警次数+1
        SceneAlarmRecordReqVO sceneAlarmRecordReqVO = new SceneAlarmRecordReqVO();
        sceneAlarmRecordReqVO.setRuleId(alarmTemplateDO.getRuleId());
        sceneAlarmRecordReqVO.setResourceSpaceId(sceneRuleDO.getResourceSpaceId());
        sceneAlarmRecordReqVO.setAlarmStatusList(Arrays.asList(
                AlarmStatusEnum.TRIGGER.getType(),
                AlarmStatusEnum.CHECKING.getType()
        ));
        //sceneAlarmRecordReqVO.setTriggerTime(LocalDateTime.now().minusMinutes(10));
        List<SceneAlarmRecordDO> doList = sceneAlarmRecordService.getSceneAlarmRecordList(sceneAlarmRecordReqVO);
        Long alarmId;
        if (CollectionUtil.isNotEmpty(doList)) {
            SceneAlarmRecordDO sceneAlarmRecordDO = doList.get(0);
            SceneAlarmRecordSaveReqVO updateReqVO = new SceneAlarmRecordSaveReqVO();
            if (AlarmStatusEnum.CHECKING.getType().equals(sceneAlarmRecordDO.getAlarmStatus())) {
                updateReqVO.setAlarmStatus(AlarmStatusEnum.TRIGGER.getType());
            }
            updateReqVO.setId(sceneAlarmRecordDO.getId());
            updateReqVO.setRuleId(alarmTemplateDO.getRuleId());
            updateReqVO.setAlarmNum(sceneAlarmRecordDO.getAlarmNum() + 1);
            updateReqVO.setLastAlarmTime(LocalDateTime.now());
            sceneAlarmRecordService.updateSceneAlarmRecord(updateReqVO);
            createAlarmRecordDetail(alarmTemplateDO.getRuleId(), sceneAlarmRecordDO, null);
            alarmId = sceneAlarmRecordDO.getId();
        } else {
            SceneAlarmRecordSaveReqVO createReqVO = new SceneAlarmRecordSaveReqVO();
            createReqVO.setResourceSpaceId(sceneRuleDO.getResourceSpaceId());
            createReqVO.setRuleId(alarmTemplateDO.getRuleId());
            createReqVO.setRuleName(sceneRuleDO.getRuleName());
            createReqVO.setAlarmTemplateId(alarmTemplateDO.getId());
            createReqVO.setAlarmName(alarmTemplateDO.getAlarmName());
            createReqVO.setAlarmContent(alarmTemplateDO.getAlarmContent());
            createReqVO.setAlarmLevel(alarmTemplateDO.getAlarmLevel());
            createReqVO.setAlarmStatus(AlarmStatusEnum.TRIGGER.getType());
            createReqVO.setTriggerTime(LocalDateTime.now());
            createReqVO.setLastAlarmTime(LocalDateTime.now());

            Optional.ofNullable(deviceCode)
                    .filter(StringUtils::isNotBlank)
                    .map(deviceService::getDeviceByCode)
                    .ifPresent(device -> {
                        createReqVO.setDeviceCode(deviceCode);
                        Optional.ofNullable(device.getProductCode())
                                .map(productService::getProductBOByCode)
                                .ifPresent(product ->
                                        createReqVO.setProductCode(product.getProductCode())
                                );
                    });
            Long id = sceneAlarmRecordService.createSceneAlarmRecord(createReqVO);
            createReqVO.setId(id);
            //生成告警详情
            createAlarmRecordDetail(alarmTemplateDO.getRuleId(), null, createReqVO);
            alarmId = id;
        }
        //生成告警记录
        createNotificationRecord(alarmId, allMsgRecordList);
    }

    /**
     * 生成告警详情
     */
    public void createAlarmRecordDetail(Long ruleId, SceneAlarmRecordDO sceneAlarmRecordDO, SceneAlarmRecordSaveReqVO createReqVO) {

        String productCode = null;
        String deviceCode = null;
        SceneAlarmRecordDetailSaveReqVO detailCreateReqVO = new SceneAlarmRecordDetailSaveReqVO();
        if (sceneAlarmRecordDO != null) {
            detailCreateReqVO.setAlarmId(sceneAlarmRecordDO.getId());
            detailCreateReqVO.setAlarmTime(sceneAlarmRecordDO.getLastAlarmTime());
            productCode = sceneAlarmRecordDO.getProductCode();
            deviceCode = sceneAlarmRecordDO.getDeviceCode();
        }
        if (createReqVO != null) {
            detailCreateReqVO.setAlarmId(createReqVO.getId());
            detailCreateReqVO.setAlarmTime(createReqVO.getLastAlarmTime());
            productCode = createReqVO.getProductCode();
            deviceCode = createReqVO.getDeviceCode();
        }
        detailCreateReqVO.setRuleId(ruleId);
        //查询触发规则
        SceneRuleTriggerDO sceneRuleTriggerDO = sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                .eqIfPresent(SceneRuleTriggerDO::getRuleId, ruleId)
                .eqIfPresent(SceneRuleTriggerDO::getProductCode, productCode)
                .eqIfPresent(SceneRuleTriggerDO::getDeviceCode, deviceCode)).get(0);
        if (sceneRuleTriggerDO != null && TriggerTypeEnum.DEVICE_TRIGGER.getType().equals(sceneRuleTriggerDO.getTriggerType())) {
            String triggerCondition = null;
            ProductModelDO productModelDO = productModelService.getProductModel(productCode, sceneRuleTriggerDO.getAttributeIdentity());
            if (DeviceTriggerTypeEnum.ATTRIBUTE.getType() == sceneRuleTriggerDO.getDeviceTriggerType()) {
                triggerCondition = productModelDO.getThingName() + "：" + sceneRuleTriggerDO.getAttributeValue();
            }
            if (DeviceTriggerTypeEnum.EVENT.getType() == sceneRuleTriggerDO.getDeviceTriggerType()) {
                triggerCondition = productModelDO.getThingName();
            }
            if (DeviceTriggerTypeEnum.ON_OR_OFF.getType() == sceneRuleTriggerDO.getDeviceTriggerType()) {
                triggerCondition = sceneRuleTriggerDO.getOnlineStatus() == 1 ? "设备由上线变为下线" : "设备由下线变为上线";
            }
            detailCreateReqVO.setDeviceName(deviceService.getDeviceByCode(deviceCode).getDeviceName());
            detailCreateReqVO.setTriggerCondition(triggerCondition);
            sceneAlarmRecordDetailService.createSceneAlarmRecordDetail(detailCreateReqVO);
        }
    }

    public void createNotificationRecord(Long alarmId, List<NotificationRecordSaveReqVO> allMsgRecordList) {
        for (NotificationRecordSaveReqVO vo : allMsgRecordList) {
            vo.setAlarmId(alarmId);
            notificationRecordService.createNotificationRecord(vo);
        }
    }

}
