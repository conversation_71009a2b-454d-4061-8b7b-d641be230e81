package cn.powerchina.bjy.link.iot.service.device;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.aop.device.DeviceDataPermissionCheck;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceAndProductVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceCountReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.DeviceRelayVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroup.DeviceGroupDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroupdetail.DeviceGroupDetailDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dto.up.EdgeCheckOnlineStatus;
import cn.powerchina.bjy.link.iot.enums.*;
import cn.powerchina.bjy.link.iot.model.DeviceCheckOnlineModel;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.service.devicegroup.DeviceGroupService;
import cn.powerchina.bjy.link.iot.service.devicegroupdetail.DeviceGroupDetailService;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import cn.powerchina.bjy.link.iot.service.sceneruleaction.SceneRuleActionService;
import cn.powerchina.bjy.link.iot.service.sceneruletrigger.SceneRuleTriggerService;
import cn.powerchina.bjy.link.iot.service.transportsource.TransportSourceService;
import cn.powerchina.bjy.link.iot.util.CodeGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DeviceServiceImpl implements DeviceService {

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ProductMapper productMapper;

    @Autowired
    @Lazy
    private ResourceSpaceService resourceSpaceService;

    @Autowired
    @Lazy
    private DeviceGroupDetailService deviceGroupDetailService;

    @Resource
    private DeviceGroupService deviceGroupService;

    @Resource
    private RocketMQv5Client rocketMQv5Client;

    @Resource
    private TransportSourceService transportSourceService;

    @Resource
    private SceneRuleTriggerService sceneRuleTriggerService;
    @Resource
    @Lazy
    private SceneRuleActionService sceneRuleActionService;
    @Resource
    @Lazy
    private SceneRuleService sceneRuleService;

    @Override
    public Long createDevice(DeviceSaveReqVO createReqVO) {
        //设备编码
        createReqVO.setDeviceCode(CodeGenerator.createCode(SceneTypeEnum.DEVICE.getPrefix()));
        // 插入
        DeviceDO device = BeanUtils.toBean(createReqVO, DeviceDO.class);
        // 校验存在
        validateSerialExists(createReqVO.getDeviceSerial());

        //查询是否有已删除设备
        String deviceCode = deviceMapper.selectCodeIsDelBySerial(createReqVO.getDeviceSerial());
        //唯一标识一样且已删除时,设备code重新启用赋值给新增的设备
        if (!StringUtils.isBlank(deviceCode)) {
            device.setDeviceCode(deviceCode);
        }
        // 对网关设备从站号去重
        if (NodeTypeEnum.EDGE.getType().equals(createReqVO.getNodeType())) {
            validateSlaveIdExists(createReqVO.getEdgeCode(), createReqVO.getSlaveId(), createReqVO.getId());
        }
        //对网关子设备MCU通道号去重
        if (NodeTypeEnum.EDGE_SUB.getType().equals(createReqVO.getNodeType())) {
            validateMcuChannelExists(createReqVO.getEdgeCode(), createReqVO.getParentCode(), createReqVO.getMcuChannel(), createReqVO.getId());
        }
        deviceMapper.insert(device);

        ProductDO productDO = productMapper.selectByCode(device.getProductCode());
        try {
            deviceDataForwarding(device, productDO.getResourceSpaceId());
        } catch (Exception e) {
            log.error("产品物模型数据转发失败：{}", device, e);
        }
        // 返回
        return device.getId();
    }

    /**
     * 校验设备唯一标识
     *
     * @param deviceSerial
     */
    private void validateSerialExists(String deviceSerial) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(DeviceDO::getDeviceSerial, deviceSerial);
        if (deviceMapper.selectCount(wrapperX) > 0) {
            throw exception(DEVICE_SERIAL_EXISTS);
        }
    }

    @Override
    public void updateDevice(DeviceSaveReqVO updateReqVO) {
        // 校验存在
        validateDeviceExists(updateReqVO.getId());
        // 对网关设备从站号去重
        if (NodeTypeEnum.EDGE.getType().equals(updateReqVO.getNodeType())) {
            validateSlaveIdExists(updateReqVO.getEdgeCode(), updateReqVO.getSlaveId(), updateReqVO.getId());
        }
        //对网关子设备MCU通道号去重
        if (NodeTypeEnum.EDGE_SUB.getType().equals(updateReqVO.getNodeType())) {
            validateMcuChannelExists(updateReqVO.getEdgeCode(), updateReqVO.getParentCode(), updateReqVO.getMcuChannel(), updateReqVO.getId());
        }
        // 更新
        DeviceDO updateObj = BeanUtils.toBean(updateReqVO, DeviceDO.class);
        deviceMapper.updateById(updateObj);

        //数据转发
        ProductDO productDO = productMapper.selectByCode(updateObj.getProductCode());
        try {
            deviceDataForwarding(updateObj, productDO.getResourceSpaceId());
        } catch (Exception e) {
            log.error("产品物模型数据转发失败：{}", updateObj, e);
        }

        //todo 更细设备信息，通知大坝平台
    }

    @Override
    public void updateDeviceRegisterState(String deviceCode, Integer state) {
        deviceMapper.updateRegisterState(deviceCode, state, new DateTime());
    }

    @Transactional
    @Override
    public void deleteDevice(Long id) {
        List<String> deviceCodeDeleteList = new ArrayList<>();
        // 校验存在
        DeviceDO deviceDO = validateDeviceExists(id);
        // 此处deviceDO不会为null，如果为null会异常，走不到这里
        ProductDO productDO = productMapper.selectByCode(deviceDO.getProductCode());
        if (productDO == null) {
            // 基本不可能会为null
            return;
        }
        deviceCodeDeleteList.add(deviceDO.getDeviceCode());
        // 网关设备
        if (NodeTypeEnum.EDGE.getType().equals(productDO.getNodeType())) {
            List<DeviceDO> deviceDOList = findDeviceByParentCode(Collections.singletonList(deviceDO.getParentCode()));
            if (CollectionUtils.isEmpty(deviceDOList)) {
                deviceCodeDeleteList.addAll(deviceDOList.stream().map(DeviceDO::getDeviceCode).filter(StringUtils::isNotBlank).toList());
            }
            // 判断有子设备，如果有子设备则删除子设备
            deleteSubDevice(deviceDO.getDeviceCode());
            // validateSubDeviceExists(deviceDO.getDeviceCode());
        }
        // 删除
        deviceMapper.deleteById(id);
        // 不删除iot_device_property_log、iot_device_event_log、iot_device_log
        //删除设备管理-分组下的设备
        if (!CollectionUtils.isEmpty(deviceCodeDeleteList)) {
            deviceGroupDetailService.deleteDeviceGroupDetailByDeviceCode(deviceCodeDeleteList);
        }
        //场景失效
        this.invalidSceneRule(deviceDO.getDeviceCode());
        //数据转发
        try {
            deviceDataForwarding(deviceDO, productDO.getResourceSpaceId());
        } catch (Exception e) {
            log.error("产品物模型数据转发失败：{}", deviceDO, e);
        }
    }

    private void invalidSceneRule(String deviceCode) {
        //触发条件、限制条件场景失效
        List<Long> sceneRuleTriggerList = sceneRuleTriggerService.deleteSceneRuleTrigger(deviceCode);
        //执行动作失效
        List<Long> sceneRuleActionList = sceneRuleActionService.deleteSceneRuleAction(deviceCode);
        //场景联动失效
        List<Long> allRuleList = new ArrayList<>();
        allRuleList.addAll(sceneRuleTriggerList);
        allRuleList.addAll(sceneRuleActionList);
        List<Long> ruleList = allRuleList.stream().distinct().collect(Collectors.toList());
        sceneRuleService.invalidSceneRule(ruleList);
    }

    private DeviceDO validateDeviceExists(Long id) {
        DeviceDO result = deviceMapper.selectById(id);
        if (result == null) {
            throw exception(DEVICE_NOT_EXISTS);
        }
        return result;
    }

    /**
     * 根据网关设备编码删除子设备
     *
     * @param parentCode
     */
    private void deleteSubDevice(String parentCode) {
        if (StringUtils.isBlank(parentCode)) {
            return;
        }

        deviceMapper.deleteByParentCode(parentCode, Boolean.TRUE);
    }

    private void validateSubDeviceExists(String parentCode) {
        QueryWrapper<DeviceDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(DeviceDO::getParentCode, parentCode)
                .eq(DeviceDO::getDeleted, Boolean.FALSE);
        if (deviceMapper.selectCount(wrapper) > 0L) {
            throw exception(DEVICE_HAVE_SUB);
        }
    }

    /**
     * 网关实例从站号判重
     * 新增时判断(网关编码 且 从站号)数量是否为零，为零则可以新增；
     * 更新时判断(网关编码 且 从站号)：
     * 1）无记录，可更新；
     * 2）数量不为1，不可更新；
     * 3）数量为1，id一致可更新；
     *
     * @param edgeCode 网关编码
     * @param slaveId  从站号
     * @param id       设备id，可为null
     */
    private void validateSlaveIdExists(String edgeCode, String slaveId, Long id) {
        if (StringUtils.isBlank(slaveId)) {
            throw exception(EDGE_GATEWAY_SLAVE_ID_REQUIRE);
        }
        QueryWrapper<DeviceDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(DeviceDO::getEdgeCode, edgeCode)
                .eq(DeviceDO::getSlaveId, slaveId)
                .eq(DeviceDO::getDeleted, Boolean.FALSE);
        if (null == id) {
            if (deviceMapper.exists(wrapper)) {
                throw exception(EDGE_GATEWAY_SLAVE_ID_EXISTS);
            }
        } else {
            List<DeviceDO> deviceDOS = deviceMapper.selectList(wrapper);
            if (CollectionUtil.isEmpty(deviceDOS)) {
                return;
            }
            if (deviceDOS.size() != 1) {
                throw exception(EDGE_GATEWAY_SLAVE_ID_EXISTS);
            }
            if (!deviceDOS.get(0).getId().equals(id)) {
                throw exception(EDGE_GATEWAY_SLAVE_ID_EXISTS);
            }
        }

    }

    /**
     * 校验网关子设备MCU通道号唯一
     *
     * @param edgeCode
     * @param parentCode
     * @param mcuChannel
     * @param id
     */
    private void validateMcuChannelExists(String edgeCode, String parentCode, String mcuChannel, Long id) {
        if (StringUtils.isBlank(parentCode)) {
            throw exception(EDGE_GATEWAY_REQUIRE);
        }
        if (StringUtils.isBlank(mcuChannel)) {
            throw exception(EDGE_GATEWAY_SUB_MCU_REQUIRE);
        }
        QueryWrapper<DeviceDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(DeviceDO::getEdgeCode, edgeCode)
                .eq(DeviceDO::getParentCode, parentCode)
                .eq(DeviceDO::getMcuChannel, mcuChannel)
                .eq(DeviceDO::getDeleted, Boolean.FALSE);
        if (null == id) {
            if (deviceMapper.exists(wrapper)) {
                throw exception(EDGE_GATEWAY_SUB_MCU_EXISTS);
            }
        } else {
            List<DeviceDO> deviceDOS = deviceMapper.selectList(wrapper);
            if (CollectionUtil.isEmpty(deviceDOS)) {
                return;
            }
            if (deviceDOS.size() != 1) {
                throw exception(EDGE_GATEWAY_SUB_MCU_EXISTS);
            }
            if (!deviceDOS.get(0).getId().equals(id)) {
                throw exception(EDGE_GATEWAY_SUB_MCU_EXISTS);
            }
        }
    }

    @Override
    public DeviceDO getDevice(Long id) {
        return deviceMapper.selectById(id);
    }

    @Override
    public Map<Long, DeviceDO> getDevices(Collection<Long> ids) {
        return deviceMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(DeviceDO::getId, Function.identity()));
    }

    @Override
    public DeviceDO getDevice(String deviceCode) {
        return deviceMapper.selectByCode(deviceCode);
    }

    @DeviceDataPermissionCheck
    @Override
    public PageResult<DeviceAndProductVO> getDevicePage(DevicePageReqVO pageReqVO) {
        //查询已分组的设备code集合
        if (Objects.nonNull(pageReqVO.getDeviceGroupId())) {
            DeviceGroupDetailPageReqVO detailPageReqVO = new DeviceGroupDetailPageReqVO();
            detailPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
            detailPageReqVO.setDeviceGroupId(pageReqVO.getDeviceGroupId());
            PageResult<DeviceGroupDetailDO> deviceGroupDetailPage = deviceGroupDetailService.getDeviceGroupDetailPage(detailPageReqVO);
            if (CollectionUtil.isNotEmpty(deviceGroupDetailPage.getList())) {
                List<String> deviceCodeList = deviceGroupDetailPage.getList().stream().map(DeviceGroupDetailDO::getDeviceCode).collect(Collectors.toList());
                pageReqVO.setDeviceCodeList(deviceCodeList);
            }
        }
        return selectDeviceAndProductPage(pageReqVO);
    }

    @Override
    public PageResult<DeviceAndProductVO> getAllDevicePage(DevicePageReqVO pageReqVO) {
        return deviceMapper.selectDeviceAndProductPage(pageReqVO);
    }

    /**
     * 设置资源空间名称
     *
     * @param bo
     */
    private void setSpaceName(DeviceAndProductVO bo) {
        if (Objects.nonNull(bo) && Objects.nonNull(bo.getResourceSpaceId())) {
            if (Objects.equals(bo.getResourceSpaceId(), 0L)) {
                bo.setSpaceName("全部");
            } else {
                ResourceSpaceDO spaceDO = resourceSpaceService.getResourceSpace(bo.getResourceSpaceId());
                bo.setSpaceName(Objects.isNull(spaceDO) ? null : spaceDO.getSpaceName());
            }
        }
    }


    @Override
    public Long getDeviceCountByProductCode(String productCode) {
        QueryWrapper<DeviceDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceDO::getProductCode, productCode).eq(DeviceDO::getDeleted, Boolean.FALSE);
        return deviceMapper.selectCount(wrapper);
    }


    @Override
    public Map<String, Object> getDeviceCountByISOnline(DevicePageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<DeviceAndProductVO> pageResult = getDevicePage(pageReqVO);
        List<DeviceAndProductVO> voList = pageResult.getList();
        Long total = (long) voList.size();
        Map<String, Object> map = new HashMap<>();
        //List<DeviceDO> deviceDOS1 = deviceMapper.selectList();
        if (CollectionUtil.isEmpty(voList)) {
            map.put("online", 0);
            map.put("offline", 0);
            map.put("all", 0);
        } else {
            //List<DeviceDO> deviceDOS = deviceDOS1.stream().filter(DeviceDO -> DeviceDO.getRegisterState() == 1).toList();
            //int size = deviceDOS.size();
            long count = voList.stream().filter(DeviceAndProductVO -> DeviceAndProductVO.getLinkState() == 1).count();
            map.put("online", count);
            map.put("offline", total - count);
            map.put("all", total);
        }
        return map;
    }

    @Override
    public DeviceDO getDeviceByCode(String deviceCode) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        return deviceMapper.selectOne(wrapperX.eq(DeviceDO::getDeviceCode, deviceCode));
    }

    @Override
    public List<DeviceDO> getDeviceByCodeList(List<String> deviceCodeList) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        return deviceMapper.selectList(wrapperX.in(DeviceDO::getDeviceCode, deviceCodeList));
    }

    @Override
    public void edgeOnlineCheck(EdgeCheckOnlineStatus edgeCheckOnlineStatus) {
        // 更新设备状态
        deviceMapper.updateLinkState(edgeCheckOnlineStatus.getDeviceCode(), edgeCheckOnlineStatus.getCheckResult());
        if (CollUtil.isNotEmpty(edgeCheckOnlineStatus.getChildDeviceStatus())) {
            edgeCheckOnlineStatus.getChildDeviceStatus().forEach(item -> {
                deviceMapper.updateLinkState(item.getDeviceCode(), item.getCheckResult());
            });
        }
        // 通知大坝设备在线离线
        DeviceCheckOnlineModel deviceCheckOnlineModel = new DeviceCheckOnlineModel();
        BeanUtil.copyProperties(edgeCheckOnlineStatus, deviceCheckOnlineModel);
        rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_STATUS_CHANGE, deviceCheckOnlineModel, IotTopicConstant.GROUP_DEVICE_STATUS_CHANGE);
    }

    @Override
    public DeviceDO getDeviceBySlaveAndEdgeCode(String slaveId, String edgeCode) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(DeviceDO::getSlaveId, slaveId).eq(DeviceDO::getEdgeCode, edgeCode);
        return deviceMapper.selectOne(wrapperX);
    }

    @Override
    public DeviceDO getDeviceByParentCodeAndMcuChannel(String parentCode, String mcuChannel) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(DeviceDO::getParentCode, parentCode)
                .eq(DeviceDO::getMcuChannel, mcuChannel);
        return deviceMapper.selectOne(wrapperX);
    }

    @Override
    public List<DeviceDO> findDeviceByParentCode(Collection<String> parentCodes) {
        return deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().in(DeviceDO::getParentCode, parentCodes));
    }

    @Override
    public Long countDevice(DeviceCountReqVO reqVO) {
        Long count = deviceMapper.selectCount(new LambdaQueryWrapperX<DeviceDO>()
                .geIfPresent(DeviceDO::getCreateTime, reqVO.getStartDate())
                .leIfPresent(DeviceDO::getCreateTime, reqVO.getEndDate())
                .eqIfPresent(DeviceDO::getNodeType, reqVO.getNodeType())
                .eqIfPresent(DeviceDO::getLinkState, reqVO.getLinkState())
                .eq(DeviceDO::getRegisterState, RegisterStateEnum.YES.getType())
        );
        return Objects.isNull(count) ? 0L : count;
    }

    @Override
    public PageResult<DeviceAndProductVO> getGroupDevicePage(DevicePageReqVO pageReqVO) {
        //查询已分组的设备code集合
        if (Objects.nonNull(pageReqVO.getDeviceGroupId())) {
            DeviceGroupDetailPageReqVO detailPageReqVO = new DeviceGroupDetailPageReqVO();
            detailPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
            detailPageReqVO.setDeviceGroupId(pageReqVO.getDeviceGroupId());
            PageResult<DeviceGroupDetailDO> deviceGroupDetailPage = deviceGroupDetailService.getDeviceGroupDetailPage(detailPageReqVO);
            if (CollectionUtil.isNotEmpty(deviceGroupDetailPage.getList())) {
                List<String> deviceCodeList = deviceGroupDetailPage.getList().stream().map(DeviceGroupDetailDO::getDeviceCode).collect(Collectors.toList());
                pageReqVO.setDeviceCodeList(deviceCodeList);
            }
            //查询资源空间id
            DeviceGroupDO deviceGroup = deviceGroupService.getDeviceGroup(pageReqVO.getDeviceGroupId());
            pageReqVO.setResourceSpaceId(deviceGroup.getResourceSpaceId());
        }
        return selectDeviceAndProductPage(pageReqVO);
    }


    @Override
    public PageResult<DeviceAndProductVO> getGatewayDevicePage(DevicePageReqVO pageReqVO) {
        return selectDeviceAndProductPage(pageReqVO);
    }

    public PageResult<DeviceAndProductVO> selectDeviceAndProductPage(DevicePageReqVO pageReqVO) {
        PageResult<DeviceAndProductVO> deviceAndProductVOPageResult = deviceMapper.selectDeviceAndProductPage(pageReqVO);
        //查网关名称(父设备名称)
        deviceAndProductVOPageResult.getList().forEach(deviceAndProductVO -> {
            setSpaceName(deviceAndProductVO);
            if (NodeTypeEnum.EDGE.getType().equals(deviceAndProductVO.getNodeType())) return;
            DeviceDO device = getDevice(deviceAndProductVO.getParentCode());
            if (ObjectUtil.isNotEmpty(device)) {
                deviceAndProductVO.setParentName(device.getDeviceName());
            }
        });
        return deviceAndProductVOPageResult;
    }

    private void deviceDataForwarding(DeviceDO deviceDO, Long resourceSpaceId) {
        //数据转发
        DeviceRelayVO deviceRelayVO = BeanUtils.toBean(deviceDO, DeviceRelayVO.class);
        deviceRelayVO.setResourceSpaceId(resourceSpaceId);
        transportSourceService.dataForwarding(TransportSourceTypeEnum.DEVICE_DELETE, deviceRelayVO);

    }

}