package cn.powerchina.bjy.link.iot.util;

import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmMsgRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationAccountVo;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod.NotificationMethodDO;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.extra.mail.MailAccount;
import org.dromara.hutool.extra.mail.MailUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * @Description: 邮件发送工具类
 * @Author: handl
 * @CreateDate: 2025/6/03
 */
@Slf4j
@Component
public class EmailSendUtils {

    private static String HOST;
    private static Integer PORT;
    private static String PASS;
    private static String FROM;
    private static Boolean AUTH;
    private static Boolean ACCOUNT_SSL_ENABLE;
    private static Boolean STARTTTLS_ENABLE;

    @Value("${email.account.host}")
    private String host;
    @Value("${email.account.port}")
    private Integer port;
    @Value("${email.account.pass}")
    private String pass;
    @Value("${email.account.from}")
    private String from;
    @Value("${email.account.auth}")
    private Boolean auth;
    @Value("${email.account.ssl_enable}")
    private Boolean accountSslEnable;
    @Value("${email.account.startttls_enable}")
    private Boolean startttlsEnable;

    @PostConstruct
    public void init() {
        HOST = this.host;
        PORT = this.port;
        PASS = this.pass;
        FROM = this.from;
        AUTH = this.auth;
        ACCOUNT_SSL_ENABLE = this.accountSslEnable;
        STARTTTLS_ENABLE = this.startttlsEnable;
    }

    public static void send(NotificationMethodDO notificationMethodDo, AlarmMsgRespVO alarmMsgRespVO) {
        MailAccount mailAccount = new MailAccount();
        mailAccount.setFrom(FROM);
        mailAccount.setUser(FROM);
        mailAccount.setPass(PASS.toCharArray());
        mailAccount.setHost(HOST);
        mailAccount.setPort(PORT);
        mailAccount.setAuth(AUTH);
        mailAccount.setSslEnable(ACCOUNT_SSL_ENABLE);
        mailAccount.setStarttlsEnable(STARTTTLS_ENABLE);
        String notificationAccount = notificationMethodDo.getNotificationAccount();
        try {
            if (Objects.isNull(notificationAccount)) {
                throw exception(ErrorCodeConstants.NOTIFICATION_ACCOUNT_NOT_EXISTS);
            }
        } catch (Exception e) {
            log.error("邮件账号错误 {}", e.getMessage());
        }

        //String content = notificationMethodDo.getNotificationContent();
        String content = buildContent(alarmMsgRespVO);
        String subject = alarmMsgRespVO.getAlarmLevel() + alarmMsgRespVO.getAlarmName();

        List<NotificationAccountVo> firstAccount = null;
        try {
            firstAccount = parseAccount(notificationAccount);
        } catch (Exception e) {
            log.error("设备告警邮件发送失败", e);
        }
        assert firstAccount != null;
        try {
            firstAccount.forEach(e -> {
                String messageId = MailUtil.send(mailAccount, e.getAccount(), subject, content, true); // 设置为 true 发送 HTML 格式
                log.info("邮件告警消息发送成功messageId：{}", messageId);
            });
        } catch (Exception e) {
            log.error("发送邮件失败:{}", e.getMessage());
        }

    }

    public static List<NotificationAccountVo> parseAccount(String notificationAccount) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(notificationAccount,
                mapper.getTypeFactory().constructCollectionType(List.class, NotificationAccountVo.class));
    }


    public static String buildContent(AlarmMsgRespVO alarmMsgRespVO) {

        String deviceName = alarmMsgRespVO.getDeviceName() != null ? alarmMsgRespVO.getDeviceName() : "无";
        String deviceCode = alarmMsgRespVO.getDeviceCode() != null ? alarmMsgRespVO.getDeviceCode() : "无";
        String productName = alarmMsgRespVO.getProductName() != null ? alarmMsgRespVO.getProductName() : "无";
        String content =
                "<html>"
                        + "<body>"
                        + "<p><strong>【告警时间】：</strong><span style='color: #333333;'>" + alarmMsgRespVO.getTriggerTime() + "</span></p>"
                        + "<p><strong>【设备信息】</strong><span style='color: #333333;'></span></p>"
                        + "<ul>"
                        + "<li>" + "触发设备名称：" + deviceName + "</li>"
                        + "<li>" + "触发设备ID：" + deviceCode + "</li>"
                        + "<li>" + "设备类型：" + productName + "</li>"
                        + "<li>" + "触发规则名称：" + alarmMsgRespVO.getRuleName() + "</li>"
                        + "</ul>"
                        + "<p><strong>【告警级别】：</strong><span style='color: #333333;'>" + alarmMsgRespVO.getAlarmLevel() + "</span></p>"
                        + "<p><strong>【告警内容】：</strong><span style='color: #ff0000;'>" + alarmMsgRespVO.getAlarmContent() + "</span></p>"
                        + "</div>"
                        + "</body>"
                        + "</html>";
        return content;
    }


}
