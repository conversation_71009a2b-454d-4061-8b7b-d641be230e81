package cn.powerchina.bjy.link.iot.service.scenealarmrecord;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord.SceneAlarmRecordDO;
import cn.powerchina.bjy.link.iot.dal.mysql.alarmtemplate.AlarmTemplateMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.scenealarmrecord.SceneAlarmRecordMapper;
import cn.powerchina.bjy.link.iot.enums.AlarmStatusEnum;
import cn.powerchina.bjy.link.iot.enums.RecoveryTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 告警记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SceneAlarmRecordServiceImpl implements SceneAlarmRecordService {

    @Resource
    private SceneAlarmRecordMapper sceneAlarmRecordMapper;

    @Resource
    private AlarmTemplateMapper alarmTemplateMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private RoleCommon roleCommon;

    @Override
    public Long createSceneAlarmRecord(SceneAlarmRecordSaveReqVO createReqVO) {
        // 插入
        SceneAlarmRecordDO sceneAlarmRecord = BeanUtils.toBean(createReqVO, SceneAlarmRecordDO.class);
        sceneAlarmRecordMapper.insert(sceneAlarmRecord);
        // 返回
        return sceneAlarmRecord.getId();
    }

    @Override
    public void updateSceneAlarmRecord(SceneAlarmRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateSceneAlarmRecordExists(updateReqVO.getId());
        // 更新
        SceneAlarmRecordDO updateObj = BeanUtils.toBean(updateReqVO, SceneAlarmRecordDO.class);
        sceneAlarmRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteSceneAlarmRecord(Long id) {
        // 校验存在
        validateSceneAlarmRecordExists(id);
        // 删除
        sceneAlarmRecordMapper.deleteById(id);
    }

    private void validateSceneAlarmRecordExists(Long id) {
        if (sceneAlarmRecordMapper.selectById(id) == null) {
            throw exception(SCENE_ALARM_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public SceneAlarmRecordDO getSceneAlarmRecord(Long id) {
        return sceneAlarmRecordMapper.selectById(id);
    }

    @Override
    public PageResult<SceneAlarmRecordRespVO> getSceneAlarmRecordPage(SceneAlarmRecordPageReqVO pageReqVO) {

        if(Objects.isNull(pageReqVO.getResourceSpaceId())){
            pageReqVO.setResourceSpaceIdList(roleCommon.getResourceSpaceIds());
        }

        PageResult<SceneAlarmRecordDO>  pageResult=sceneAlarmRecordMapper.selectPage(pageReqVO);
        PageResult<SceneAlarmRecordRespVO> result=BeanUtils.toBean(pageResult, SceneAlarmRecordRespVO.class);
        if(result!=null) {
            if(!CollectionUtils.isEmpty(result.getList())) {
                result.getList().stream().forEach(item->{
                    if(item.getProcessUserId()!=null){
                        Long loginUserId = Long.valueOf(item.getProcessUserId());
                        AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUserId).getData();
                        item.setProcessUserName(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : null);
                    }
                    if(StringUtils.isNotEmpty(item.getDeviceCode())) {
                        DeviceDO deviceDO=deviceMapper.selectByCode(item.getDeviceCode());
                        if(deviceDO!=null){
                            item.setDeviceName(StringUtils.isNotEmpty(deviceDO.getDeviceName()) ? deviceDO.getDeviceName() : null);
                            item.setDeviceSerial(StringUtils.isNotEmpty(deviceDO.getDeviceSerial()) ? deviceDO.getDeviceSerial() : null);
                        }
                    }
                    if(StringUtils.isNotEmpty(item.getProductCode())) {
                        ProductDO productDO=productMapper.selectByCode(item.getProductCode());
                        if(productDO!=null) {
                            item.setProductName(StringUtils.isNotEmpty(productDO.getProductName()) ? productDO.getProductName() : null);
                        }
                    }
                });
            }
        }
        return result;
    }

    @Override
    public List<SceneAlarmRecordDO> getSceneAlarmRecordList(SceneAlarmRecordReqVO reqVO) {
        LambdaQueryWrapper<SceneAlarmRecordDO> lambdaWrapper = new LambdaQueryWrapper<>();
        if (reqVO.getRuleId() != null) {
            lambdaWrapper.eq(SceneAlarmRecordDO::getRuleId, reqVO.getRuleId());
        }
        if (reqVO.getResourceSpaceId() != null) {
            lambdaWrapper.eq(SceneAlarmRecordDO::getResourceSpaceId, reqVO.getResourceSpaceId());
        }
        if (reqVO.getTriggerTime() != null) {
            lambdaWrapper.gt(SceneAlarmRecordDO::getTriggerTime, reqVO.getTriggerTime());
        }
        if (!CollectionUtils.isEmpty(reqVO.getAlarmStatusList())) {
            lambdaWrapper.in(SceneAlarmRecordDO::getAlarmStatus, reqVO.getAlarmStatusList());
        }
        return sceneAlarmRecordMapper.selectList(lambdaWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean processAlarmRecord(Long id, String processRecord, Long processUserId, String processUserName) {
        // 1. 校验告警记录是否存在
        SceneAlarmRecordDO record = sceneAlarmRecordMapper.selectById(id);
        if (record == null) {
            throw exception(SCENE_ALARM_RECORD_NOT_EXISTS);
        }

        // 3. 检查告警状态是否为触发状态
        if (!AlarmStatusEnum.TRIGGER.getType().equals(record.getAlarmStatus())) {
            throw exception(SCENE_RULE_ALARM_NOT_EXISTS);
        }
        // 4. 更新告警记录处理信息
        SceneAlarmRecordDO updateRecord = new SceneAlarmRecordDO();
        updateRecord.setId(id);
        updateRecord.setProcessRecord(processRecord);
        updateRecord.setProcessUserId(processUserId);
        updateRecord.setProcessTime(LocalDateTime.now());
        // 待验证状态
        updateRecord.setAlarmStatus(AlarmStatusEnum.CHECKING.getType());
        sceneAlarmRecordMapper.updateById(updateRecord);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recoverAlarmRecord(Long id, Long recoveryUserId, String recoveryUserName) {
        // 1. 校验告警记录是否存在
        SceneAlarmRecordDO record = sceneAlarmRecordMapper.selectById(id);
        if (record == null) {
            throw exception(SCENE_ALARM_RECORD_NOT_EXISTS);
        }
        // 3. 检查告警状态是否为触发状态
        if (!AlarmStatusEnum.CHECKING.getType().equals(record.getAlarmStatus())) {
            throw exception(SCENE_RULE_ALARM_NOT_VERIFY_EXISTS);
        }
        // 4. 更新告警记录恢复信息
        SceneAlarmRecordDO updateRecord = new SceneAlarmRecordDO();
        updateRecord.setId(id);
        updateRecord.setRecoveryUserId(recoveryUserId);
        updateRecord.setRecoveryTime(LocalDateTime.now());
        // 人工恢复
        updateRecord.setRecoveryType(RecoveryTypeEnum.MANUAL_RESTORE.getType());
        // 恢复状态
        updateRecord.setAlarmStatus(AlarmStatusEnum.RESTORE.getType());
        sceneAlarmRecordMapper.updateById(updateRecord);

        return true;
    }

}