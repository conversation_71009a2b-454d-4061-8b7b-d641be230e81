package cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 转发规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransportRuleRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29315")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("规则名称")
    private String name;

    @Schema(description = "转发规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("转发规则编码")
    private String ruleCode;

    @Schema(description = "启用状态（0:未启动 1：运行中）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("启用状态（0:未启动 1：运行中）")
    private Integer status;

    @Schema(description = "规则描述", example = "你猜")
    @ExcelProperty("规则描述")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}