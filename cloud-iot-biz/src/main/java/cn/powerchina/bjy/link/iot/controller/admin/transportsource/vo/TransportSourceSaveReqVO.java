package cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 转发规则-数据源新增/修改 Request VO")
@Data
public class TransportSourceSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14614")
    private Long id;

    @Schema(description = "规则id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27450")
    @NotNull(message = "规则id不能为空")
    private Long ruleId;

    @Schema(description = "数据类型，逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "数据类型，逗号分隔不能为空")
    private String dataType;

    @Schema(description = "资源空间id：全部是-1", example = "29505")
    private Long resourceSpaceId;

    @Schema(description = "产品code：全部是-1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "产品code：全部是-1不能为空")
    private String productCode;

    @Schema(description = "设备code:全部-1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "设备code:全部-1不能为空")
    private String deviceCode;

}