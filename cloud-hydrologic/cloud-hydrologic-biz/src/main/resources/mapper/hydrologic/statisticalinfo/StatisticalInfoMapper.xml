<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.statisticalinfo.StatisticalInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <sql id="Base_Column_List">
        id,
        station_id,
        stats_id,
        start_year,
        end_year,
        year_count,
        `years`,
        station_type,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticalinfo.StatisticalInfoDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="stats_id" property="statsId"/>
        <result column="start_year" property="startYear"/>
        <result column="end_year" property="endYear"/>
        <result column="year_count" property="yearCount"/>
        <result column="years" property="years"/>
        <result column="station_type" property="stationType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="listStatisticalIndexData"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo.StatisticalInexDataRespVO">
        SELECT
            dir.*,
            CASE
                WHEN dir.name = '序列格式' THEN max_subquery.max_year_count
                WHEN dir.name = '年鉴格式' THEN max_subquery.max_year_count
                ELSE info.year_count
                END AS year_count,
            info.start_year,
            info.end_year,
            info.years
        FROM hydrologic_statistical_directory dir
                 LEFT JOIN hydrologic_statistical_info info
                           ON dir.id = info.stats_id
                               AND dir.station_type = info.station_type
                               AND info.station_id = #{stationId}
                               AND dir.deleted = 0
                               AND info.deleted = 0
                 LEFT JOIN (
            -- 预计算子查询：获取每个父目录下station_id=320的最大year_count
            SELECT
                dir1.parent_id AS dir_id,
                MAX(info1.year_count) AS max_year_count
            FROM hydrologic_statistical_directory dir1
                     LEFT JOIN hydrologic_statistical_info info1
                               ON dir1.id = info1.stats_id
                                   AND dir1.station_type = info1.station_type
                                   AND info1.station_id = #{stationId}
                                   AND dir1.deleted = 0
                                   AND info1.deleted = 0
            WHERE dir1.station_type = #{stationType}
              AND dir1.deleted = 0
              AND dir1.parent_id IS NOT NULL  -- 确保只处理有父目录的记录
            GROUP BY dir1.parent_id
        ) AS max_subquery
                           ON dir.id = max_subquery.dir_id
        WHERE dir.station_type = #{stationType}
          AND dir.deleted = 0
        ORDER BY dir.sort;
    </select>
    <select id="listByParentId"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticalinfo.StatisticalInfoDO">
        select *
        from hydrologic_statistical_info info
                 inner join hydrologic_statistical_directory directory on
            info.stats_id = directory.id
        where directory.parent_id = #{parentId} and info.deleted=0 and info.station_id = #{stationId}
    </select>

    <select id="selectOneByStationIdAndDataType"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticalinfo.StatisticalInfoDO">
        select *
        from hydrologic_statistical_info info
        where
          info.station_id = #{stationId} and stats_id=#{statsId} and info.deleted=0
    </select>

    <select id="listByStationIds" resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticalinfo.StatisticalInfoDO">
        select *
        from hydrologic_statistical_info info where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and info.deleted=0
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_statistical_info(
        id,
        station_id,
        stats_id,
        start_year,
        end_year,
        year_count,
        `years`,
        station_type,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.statsId},
            #{element.startYear},
            #{element.endYear},
            #{element.yearCount},
            #{element.years},
            #{element.stationType},
            now(),
            now(),
            0,
            0,
            0
            )
        </foreach>
    </insert>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_statistical_info where id &lt; 1 and station_id=#{stationId}  and creator='1';
    </select>

    <update id="updateCreator">
        update hydrologic_statistical_info set creator='1' where id &lt; 1  and creator='0';

    </update>

<!--auto generated by MybatisCodeHelper on 2024-09-24-->
    <update id="updateById">
        update hydrologic_statistical_info
        <set>
            start_year   = #{updated.startYear},

            end_year     = #{updated.endYear},

            year_count   = #{updated.yearCount},

            years        = #{updated.years},

            station_type = #{updated.stationType},

            update_time  = #{updated.updateTime},

            updater      = #{updated.updater}
        </set>
        where id = #{updated.id}
    </update>

    <select id="listByStationId" resultMap="BaseResultMap">
        select * from hydrologic_statistical_info where station_id=#{stationId}
    </select>
    <select id="listByFilteredNodeIds" resultType="cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo.StatisticalInexDataRespVO">
        SELECT dir.*, info.start_year, info.end_year, info.year_count, info.years
        FROM hydrologic_statistical_directory dir
        LEFT JOIN hydrologic_statistical_info info
        ON dir.id = info.stats_id
        AND dir.station_type = info.station_type
        AND info.station_id = #{stationId}
        AND dir.deleted = 0 AND info.deleted = 0
        WHERE dir.station_type = #{stationType}
        AND dir.deleted = 0
        AND (dir.id IN
        <foreach item="id" index="index" collection="nodeIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        OR info.stats_id IN
        <foreach item="id" index="index" collection="nodeIds" open="(" separator="," close=")">
            #{id}
        </foreach>)
        ORDER BY dir.sort
    </select>
</mapper>