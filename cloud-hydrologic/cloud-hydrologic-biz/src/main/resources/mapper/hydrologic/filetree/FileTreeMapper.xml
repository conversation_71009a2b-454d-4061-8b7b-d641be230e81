<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.filetree.FileTreeMapper">
    <sql id="Base_Column_List">
        id,
        project_id,
        tree_name,
        parent_id,
        parent_ids,
        tree_level,
        create_time,
        update_time,
        creator,
        updater,
        deleted,
          type
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filetree.FileTreeDO">
        <result column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="tree_name" property="treeName"/>
        <result column="parent_id" property="parentId"/>
        <result column="parent_ids" property="parentIds"/>
        <result column="tree_level" property="treeLevel"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <select id="selectListByProjectIds" resultMap="BaseResultMap">
        select * from hydrologic_file_tree where project_id in
        <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_file_tree(
        id,
        project_id,
        tree_name,
        parent_id,
        parent_ids,
        tree_level,
        create_time,
        update_time,
        creator,
        updater,
        deleted,
        type
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.projectId},
            #{element.treeName},
            #{element.parentId},
            #{element.parentIds},
            #{element.treeLevel},
            now(),
            now(),
            0,
            0,
            0,
            #{element.type}
            )
        </foreach>
    </insert>

    <update id="updateProjectId">
        update hydrologic_file_tree set project_id=#{newProjectId} where project_id=#{oldProjectId};
    </update>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_file_tree where id &lt; 1 and creator='1' and project_id=#{projectId} order by tree_level,id desc;
    </select>

    <update id="updateCreator">
        update hydrologic_file_tree set creator='1' where id &lt; 1 and creator='0';
        update hydrologic_file_document set creator='1' where id &lt; 1 and creator='0';
    </update>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

</mapper>