package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.dischargeday;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeday.vo.DischargeDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.dischargeday.DischargeDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.HydrologicChecker;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 水文站-输沙率-悬移质输沙率 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DischargeDayMapper extends BaseMapperX<DischargeDayDO>, HydrologicChecker<DischargeDayDO> {

    default PageResult<DischargeDayDO> selectPage(DischargeDayPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DischargeDayDO>()
                .eqIfPresent(DischargeDayDO::getStationId, reqVO.getStationId())
                .eqIfPresent(DischargeDayDO::getYear, reqVO.getYear())
                .eqIfPresent(DischargeDayDO::getMonth, reqVO.getMonth())
                .eqIfPresent(DischargeDayDO::getDay, reqVO.getDay())
                .eqIfPresent(DischargeDayDO::getVersion, reqVO.getVersion())
                .eqIfPresent(DischargeDayDO::getLatest, reqVO.getLatest())
                .betweenIfPresent(DischargeDayDO::getCurrentDay, reqVO.getCurrentDay())
                .orderByAsc(DischargeDayDO::getYear)
                .orderByAsc(DischargeDayDO::getMonth)
                .orderByAsc(DischargeDayDO::getDay));
    }

    @Select("SELECT version FROM hydrologic_discharge_day WHERE station_id = #{stationId} AND latest = 1 AND deleted = 0 limit 1")
    Integer getLatestVersion(@Param("stationId") Long stationId);

}