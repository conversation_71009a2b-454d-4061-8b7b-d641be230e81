package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationalmanac.dataprocessor;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.QueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.station.StationDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationday.StationDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth.StationMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationyear.StationYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.station.StationMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationdatalog.StationDataLogMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationday.StationDayMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationmonth.StationMonthMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationyear.StationYearMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.interceptor.CustomTemplateSheetStrategy;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationalmanac.StationAlmanacService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationalmanac.StationAlmanacServiceImpl;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationday.StationDayService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationmonth.StationMonthService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationyear.StationYearService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.DATA_REPEAT;

/**
 * 输沙率年鉴
 */
@Slf4j
@Service
public class DischargeAlmanacServiceImpl extends StationAlmanacServiceImpl<StationDayDO, DischargeDayImportModel> implements StationAlmanacService {


    @Resource
    private StationDayService stationDayService;
    @Resource
    private StationMonthService stationMonthService;
    @Resource
    private StationYearService stationYearService;
    @Resource
    private StationMapper stationMapper;
    @Autowired
    private StationYearMapper stationYearMapper;
    @Autowired
    private StationDayMapper stationDayMapper;
    @Autowired
    private StationMonthMapper stationMonthMapper;
    @Resource
    private StationDataLogMapper stationDataLogMapper;

    private StationDataTypeEnumV2 YEARENUM = StationDataTypeEnumV2.HYDROLOGIC_ANNUAL_TRANSPORT_RATE_CHARACTERISTICS;
    private StationDataTypeEnumV2 MONTHENUM =  StationDataTypeEnumV2.HYDROLOGIC_MONTHLY_TRANSPORT_RATE_CHARACTERISTICS;
    private StationDataTypeEnumV2 DAYENUM = StationDataTypeEnumV2.HYDROLOGIC_DAILY_AVERAGE_SUSPENDED_SOLID_TRANSPORT_RATE_SEQUENCE;
    private StationDataTypeEnumV2 BOOK = StationDataTypeEnumV2.HYDROLOGIC_DAILY_AVERAGE_SUSPENDED_SOLID_TRANSPORT_RATE_ANNUAL;
    /**
     * 查询年鉴数据
     *
     * @param stationId
     */

    @Override
    public List<YearForm> info(Long stationId, Long logId, String year) {
        return getYearBookList(stationId, logId, year,null);
    }
    public List<YearForm> getYearBookList(Long stationId, Long logId, String year,Boolean isExport) {
        if (null == stationId) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        Integer stationType = StationEnum.HYDROLOGIC.getType();

        Long count = stationDataLogMapper.selectCount(new LambdaQueryWrapperX<StationDataLogDO>()
                .gtIfPresent(StationDataLogDO::getId, logId)
                .eq(StationDataLogDO::getStationId, stationId)
                .eq(StationDataLogDO::getDataType, BOOK.getCode())
//                .eq(StationDataLogDO::getYear, year)
        );
        if(count==0){
            logId=null;
        }
        StationDataLogDO dataLogDO;
        if (Objects.nonNull(logId)) {
            dataLogDO = stationDataLogService.findById(logId);
            if (Objects.nonNull(dataLogDO) && !Objects.equals(stationType, dataLogDO.getStationType())) {
                dataLogDO = null;
            }
        } else if (Objects.nonNull(year)) {
            dataLogDO = stationDataLogService.findStationDataLogDOLatestByStationTypeAndYear(stationId, stationType, BOOK.getCode(), year + "");
            dataLogDO.setCurrentVersion(-1);
            dataLogDO.setYearVersion(-1);
            dataLogDO.setMonthVersion(-1);
            dataLogDO.setDayVersion(-1);
            dataLogDO.setPeriodDayVersion(-1);
        } else {
            dataLogDO = stationDataLogService.findStationDataLogDOLatestByStationType(stationId, stationType, BOOK.getCode());
            dataLogDO.setCurrentVersion(-1);
            dataLogDO.setYearVersion(-1);
            dataLogDO.setMonthVersion(-1);
            dataLogDO.setDayVersion(-1);
            dataLogDO.setPeriodDayVersion(-1);
        }
        if (Objects.isNull(dataLogDO)) {
            return new ArrayList<>();
        }

        // 获取日序列数据
        List<StationDayDO> dayDOList = stationDayService.findDOByVersionAndYear(stationId, dataLogDO.getYear(), dataLogDO.getDayVersion(),DAYENUM.getCode());
        Map<Integer, List<StationDayDO>> dayMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dayDOList)) {
            dayMap.putAll(dayDOList.stream().collect(Collectors.groupingBy(StationDayDO::getDay, Collectors.mapping(Function.identity(), Collectors.toList()))));
        }
        // 获取年数据
        StationYearDO yearDO = stationYearService.findYearDOByVersionAndYear(stationId, dataLogDO.getYear(), dataLogDO.getYearVersion(), YEARENUM.getCode());
        // 获取月数据
        Map<Integer, StationMonthDO> monthDOMap = new HashMap<>();
        List<StationMonthDO> monthDOList = stationMonthService.findMonthDOByVersionAndYear(stationId, dataLogDO.getYear(), dataLogDO.getMonthVersion(), MONTHENUM.getCode());
        if (CollectionUtils.isNotEmpty(monthDOList)) {
            monthDOMap.putAll(monthDOList.stream().collect(Collectors.toMap(StationMonthDO::getMonth, Function.identity())));
        }
        // 组装数据
        List<YearForm> yearModelList = new ArrayList<>();
        generateFrontDataHead(dataLogDO.getYear(), yearModelList);
        // 插入日序列数据
        for (int i = 1; i < 32; i++) {
            yearModelList.add(generateFrontDataDay(i, dayMap.get(i)));
        }
        // 月数据填充
        generateFrontMonthData(yearModelList, monthDOMap);
        // 年数据填充
        generateFrontYearData(yearModelList, yearDO);
        // 填充附注
        generateFrontYearRemark(yearModelList, dataLogDO);
        return yearModelList;
    }

    private void generateFrontDataHead(String year, List<YearForm> yearModelList) {
        //生成表头
//        yearModelList.add(YearForm.builder().columnName("年份").columnSecondName("").value1(year + "年").build());
//        yearModelList.add(YearForm.builder().columnName("").value1("一月").value2("二月").value3("三月").value4("四月").value5("五月")
//                .value6("六月").value7("七月").value8("八月").value9("九月").value10("十月").value11("十一月").value12("十二月").build());
        yearModelList.add(YearForm.builder().columnName("日统计: 输沙率(kg/s)").build());
//        yearModelList.add(YearForm.builder().columnName("月份/日期").value1("一月").value2("二月").value3("三月").value4("四月").value5("五月")
//                .value6("六月").value7("七月").value8("八月").value9("九月").value10("十月").value11("十一月").value12("十二月").build());
    }

    /**
     * 生成前端日序列数据
     *
     * @param index
     * @param dischargeDayDOS
     * @return
     */
    private YearForm generateFrontDataDay(Integer index, List<StationDayDO> dischargeDayDOS) {
        Map<Integer, String> dayMonthMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dischargeDayDOS)) {
            dischargeDayDOS.forEach(item->{
                dayMonthMap.put(item.getMonth(),StringUtils.isBlank(item.getValue())?"":item.getValue());
            });
        }
        return YearForm.builder().columnName(index + "").value1(generateFrontDataDayValue(dayMonthMap, 1)).value2(generateFrontDataDayValue(dayMonthMap, 2))
                .value3(generateFrontDataDayValue(dayMonthMap, 3)).value4(generateFrontDataDayValue(dayMonthMap, 4)).value5(generateFrontDataDayValue(dayMonthMap, 5))
                .value6(generateFrontDataDayValue(dayMonthMap, 6)).value7(generateFrontDataDayValue(dayMonthMap, 7)).value8(generateFrontDataDayValue(dayMonthMap, 8))
                .value9(generateFrontDataDayValue(dayMonthMap, 9)).value10(generateFrontDataDayValue(dayMonthMap, 10)).value11(generateFrontDataDayValue(dayMonthMap, 11))
                .value12(generateFrontDataDayValue(dayMonthMap, 12)).build();
    }

    /**
     * 生成前端月数据
     *
     * @param yearModelList
     * @param monthDOMap
     */
    private void generateFrontMonthData(List<YearForm> yearModelList, Map<Integer, StationMonthDO> monthDOMap) {
        // 月平均
        yearModelList.add(YearForm.builder().columnName("月统计").build());
        yearModelList.add(YearForm.builder().columnName("平均输沙率(kg/s)")
                .value1(generateFrontMonthDataValue(monthDOMap, 1, 1))
                .value2(generateFrontMonthDataValue(monthDOMap, 2, 1))
                .value3(generateFrontMonthDataValue(monthDOMap, 3, 1))
                .value4(generateFrontMonthDataValue(monthDOMap, 4, 1))
                .value5(generateFrontMonthDataValue(monthDOMap, 5, 1))
                .value6(generateFrontMonthDataValue(monthDOMap, 6, 1))
                .value7(generateFrontMonthDataValue(monthDOMap, 7, 1))
                .value8(generateFrontMonthDataValue(monthDOMap, 8, 1))
                .value9(generateFrontMonthDataValue(monthDOMap, 9, 1))
                .value10(generateFrontMonthDataValue(monthDOMap, 10, 1))
                .value11(generateFrontMonthDataValue(monthDOMap, 11, 1))
                .value12(generateFrontMonthDataValue(monthDOMap, 12, 1)).build());
        // 月最大值
        yearModelList.add(YearForm.builder().columnName("最大输沙率(kg/s)")
                .value1(generateFrontMonthDataValue(monthDOMap, 1, 2))
                .value2(generateFrontMonthDataValue(monthDOMap, 2, 2))
                .value3(generateFrontMonthDataValue(monthDOMap, 3, 2))
                .value4(generateFrontMonthDataValue(monthDOMap, 4, 2))
                .value5(generateFrontMonthDataValue(monthDOMap, 5, 2))
                .value6(generateFrontMonthDataValue(monthDOMap, 6, 2))
                .value7(generateFrontMonthDataValue(monthDOMap, 7, 2))
                .value8(generateFrontMonthDataValue(monthDOMap, 8, 2))
                .value9(generateFrontMonthDataValue(monthDOMap, 9, 2))
                .value10(generateFrontMonthDataValue(monthDOMap, 10, 2))
                .value11(generateFrontMonthDataValue(monthDOMap, 11, 2))
                .value12(generateFrontMonthDataValue(monthDOMap, 12, 2)).build());
        // 月最大值出现日期
        yearModelList.add(YearForm.builder().columnName("最大日期(日)")
                .value1(generateFrontMonthDataValue(monthDOMap, 1, 3))
                .value2(generateFrontMonthDataValue(monthDOMap, 2, 3))
                .value3(generateFrontMonthDataValue(monthDOMap, 3, 3))
                .value4(generateFrontMonthDataValue(monthDOMap, 4, 3))
                .value5(generateFrontMonthDataValue(monthDOMap, 5, 3))
                .value6(generateFrontMonthDataValue(monthDOMap, 6, 3))
                .value7(generateFrontMonthDataValue(monthDOMap, 7, 3))
                .value8(generateFrontMonthDataValue(monthDOMap, 8, 3))
                .value9(generateFrontMonthDataValue(monthDOMap, 9, 3))
                .value10(generateFrontMonthDataValue(monthDOMap, 10, 3))
                .value11(generateFrontMonthDataValue(monthDOMap, 11, 3))
                .value12(generateFrontMonthDataValue(monthDOMap, 12, 3)).build());
    }

    /**
     * 生成前端年统计表
     *
     * @param yearModelList
     * @param dischargeYearDO
     */
    private void generateFrontYearData(List<YearForm> yearModelList, StationYearDO dischargeYearDO) {
        yearModelList.add(YearForm.builder().columnName("年统计").build());
        yearModelList.add(YearForm.builder()
                .columnName("最大日平均输沙率(kg/s)").value1(Objects.nonNull(dischargeYearDO) ? dischargeYearDO.getMaxValue() : null)
                .value3("出现日期").value6(Objects.nonNull(dischargeYearDO) ? (dischargeYearDO.getMaxValueDate()) : null)
                .value8("平均输沙率(kg/s)").value11(Objects.nonNull(dischargeYearDO) ? (dischargeYearDO.getAverageValue()) : null)
                .build());

        yearModelList.add(YearForm.builder()
                .columnName("年输沙量(10\u2074 t)").value1(Objects.nonNull(dischargeYearDO) ? dischargeYearDO.getTotalValue() : null)
                .value8("输沙模数(t/km\u00B2)").value11(Objects.nonNull(dischargeYearDO) ? dischargeYearDO.getDischargeModulus() : null)
                .build());

    }

    /**
     * 生成前端年统计附注
     *
     * @param yearModelList
     * @param dataLogDO
     */
    private void generateFrontYearRemark(List<YearForm> yearModelList, StationDataLogDO dataLogDO) {
        yearModelList.add(YearForm.builder().columnName("附注").value1(dataLogDO.getYearRemark()).build());
    }

    /**
     * 生成前端单元格内容
     *
     * @param dayMonthMap
     * @param month
     * @return
     */
    private String generateFrontDataDayValue(Map<Integer, String> dayMonthMap, int month) {
        return dayMonthMap.get(month);
    }

    /**
     * 生成前端月数据-月统计-月数据组装
     *
     * @param monthDOMap
     * @param month
     * @return
     */
    private String generateFrontMonthDataValue(Map<Integer, StationMonthDO> monthDOMap, int month, Integer type) {
        if (null == type) {
            return null;
        }
        StationMonthDO monthDO = monthDOMap.get(month);
        return switch (type) {
            case 1 -> Objects.nonNull(monthDO) ? monthDO.getAverageValue() : null;
            case 2 -> Objects.nonNull(monthDO) ? monthDO.getMaxValue() : null;
            case 3 -> Objects.nonNull(monthDO) ? monthDO.getMaxValueDate() : null;
            default -> null;
        };
    }



    //---------------------------------------------修改-------------------------------------

    /**
     * 修改年鉴数据
     *
     * @param stationId
     * @param dataMode
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyYearBookList(Long stationId, List<YearForm> dataMode,String year) {
        if (CollectionUtils.isEmpty(dataMode)) {
            return;
        }
        // 查询站点是否存在
        if (Objects.isNull(stationId) || Objects.isNull(stationMapper.selectById(stationId))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        // 增加分布式锁，只能有一个导入,针对站点增加锁，站点的所有类型，只能有一个导入,页面新增时也要增加锁控制
        String lockKey = String.format(PlanningDesignConstants.HYDROLOGIC_SAND_DAY_IMPORT_KEY, stationId);
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, 1, 60, TimeUnit.SECONDS))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EXISTS_ERROR);
        }
        try {
            //数据处理
            EXCEL.set(stationId);
            if (CollectionUtils.isEmpty(dataMode)) {
                return;
            }
//            // 获取年
//            String year = dataMode.get(0).getColumnSecondName();
//            if (StringUtils.isBlank(year)) {
//                year = dataMode.get(0).getValue1();
//            }
            if (StringUtils.isBlank(year)) {
                return;
            }
            if (year.contains("年")) {
                year = year.replace("年", "");
            }
            String yearRemark = dataMode.get(dataMode.size() - 1).getValue1();
            // 转换为日格式，日格式固定
            List<YearForm> dayDatas = new ArrayList<>(31);
            for (int i = 1; i < 1 + 31 && i < dataMode.size() ; i++) {
                dayDatas.add(dataMode.get(i));
            }
            List<StationDayDO> doList = fillDayItemDefault(dayDatas, EXCEL.get(), year);


            if (CollectionUtils.isEmpty(doList)) {
                return;
            }
            // 涉及关联站点等其他处理的逻辑
            dataProcess(doList, ShowTypeEnum.YEAR);

            // 入库
            getMapper().insertBatch(doList);
            // 转换为年、月格式，需具体处理
            updateOtherData(year, yearRemark, dataMode);
        }catch (ServiceException e){
            throw e;
        }catch (Throwable e){
            throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
        }finally {
            EXCEL.remove();
            redisTemplate.delete(lockKey);
        }
    }

    @Override
    public void exportBookExcel(HttpServletResponse response, Long stationId, Long logId, String year) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(StationDataTypeEnumV2.HYDROLOGIC_DAILY_AVERAGE_SUSPENDED_SOLID_TRANSPORT_RATE_ANNUAL.getDescription() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            List<YearForm> yearModelList = getYearBookList(stationId, logId, year,true);
            if (org.springframework.util.CollectionUtils.isEmpty(yearModelList)) {
                log.error("exportDischargeDayBookExcel--->没有数据，不导出。stationId={},logId={},year={}", stationId, logId, year);
                return;
            }
            String otherYear = year;
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + BOOK.getCode() + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream()) // 利用模板的输出流
                    .registerWriteHandler(new CustomTemplateSheetStrategy(0,otherYear))
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, otherYear.replace("年", "")).build();
            excelWriter.fill(yearModelList.subList(2, 36), writeSheet);
            Map<String, String> otherMap = new HashMap<>();
            otherMap.put("otherYear", otherYear);
            otherMap.put("otherMaxValue", yearModelList.get(36).getValue4());
            otherMap.put("otherMaxValueDate", yearModelList.get(36).getValue6());
            otherMap.put("otherAverageValue", yearModelList.get(36).getValue10());
            otherMap.put("otherTotalValue", yearModelList.get(37).getValue4());
            otherMap.put("otherDischargeModulus", yearModelList.get(37).getValue10());
            otherMap.put("otherRemark", yearModelList.get(38).getValue1());
            excelWriter.fill(otherMap, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("exportBookExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }

    @Override
    protected void dataProcess(List<StationDayDO> dischargeDayDOS, ShowTypeEnum source) {
        StationDO stationDO = stationMapper.selectById(EXCEL.get());
        Long stationId = stationDO.getId();
        Integer dataType = DAYENUM.getCode();
        Integer nextVersion = stationDataLogService.getNextVersion(stationId, dataType);

        try {
        if (CollectionUtils.isEmpty(dischargeDayDOS)) {
            return;
        }
        List<Integer> years = new ArrayList<>();
        Set<String> currentIdentify = new HashSet<>(dischargeDayDOS.size());
        // 默认版本为0
        for (int i = 0; i < dischargeDayDOS.size(); i++) {
            StationDayDO item = dischargeDayDOS.get(i);
            // 校验日期
            if (source == ShowTypeEnum.LIST && item.getId() == null) {
                validateFull(item.getYear(), item.getMonth(), item.getDay(), i + 2);
            } else if (source == ShowTypeEnum.LIST) {
                validateFull(item.getYear(), item.getMonth(), item.getDay());
            }
            if (!years.contains(item.getYear())) {
                years.add(item.getYear());
            }
            // 判断为更新接口用
            if (null == item.getStationId()) {
                item.setStationId(EXCEL.get());
            }
            item.setId(null);
            item.setDataType(dataType);
            item.setStationType(stationDO.getStationType());
            item.setCurrentDay(DateUtils.parseDate(item.getYear() + "-" + item.getMonth()+ "-" + item.getDay()));
            item.setVersion(1);
            item.setLatest(LatestEnum.LATEST.getType());
            if (!currentIdentify.add(item.getYear() + "-" + item.getMonth()+ "-" + item.getDay())) {
                if (source == ShowTypeEnum.LIST && item.getId() == null) {
                    throw exception(DATA_REPEAT, "第" + (i + 2) + "行");
                } else if (source == ShowTypeEnum.LIST) {
                    throw exception(DATA_REPEAT, item.getYear() + "-" + item.getMonth()+ "-" + item.getDay());
                }
            }
        }
        dischargeDayDOS.removeIf(item -> !Objects.equals(item.getMonth(),DateUtils.getLocalYearAndMonth(item.getCurrentDay(), "yyyy-MM-dd")));
        // 没有历史数据则不处理
        LambdaQueryWrapperX<StationDayDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(StationDayDO::getStationId, EXCEL.get())
                .eq(StationDayDO::getDataType, dataType)
                .eq(StationDayDO::getLatest, LatestEnum.LATEST.getType())
                .eq(StationDayDO::getDeleted, Boolean.FALSE);
        // 没有历史数据则不处理
        List<StationDayDO> beforeData = stationDayMapper.selectList(queryWrapperX);
        if (CollectionUtils.isEmpty(beforeData)) {
            if (source == ShowTypeEnum.LIST) {
                saveDataLog(years, 0);
            }
            return;
        }
            // 创建beforeData的映射表用于查找备注
            Map<String, StationDayDO> beforeDataMap = beforeData.stream()
                    .collect(Collectors.toMap(
                            item -> item.getYear() + "-" + item.getMonth() + "-" + item.getDay(),
                            Function.identity()
                    ));

        // 赋默认值，避免全量导入时无法正常赋值情况
        AtomicInteger oldVersion = new AtomicInteger(beforeData.get(0).getVersion());
        // 过滤掉本次新增的数据，并加入入库集合中
        beforeData.stream().filter(item -> !currentIdentify.contains(item.getYear()+ "-" + item.getMonth() + "-" + item.getDay())).forEach(oldData -> {
            oldVersion.set(oldData.getVersion());
            oldData.setId(null);
            oldData.setDataType(dataType);
            oldData.setStationType(stationDO.getStationType());
            oldData.setLatest(LatestEnum.LATEST.getType());
            dischargeDayDOS.add(oldData);
        });
            // 将beforeData中的备注复制到sandContentDayDOS中相同年月日的记录
            dischargeDayDOS.forEach(newData -> {
                String key = newData.getYear() + "-" + newData.getMonth() + "-" + newData.getDay();
                StationDayDO beforeItem = beforeDataMap.get(key);
                if (beforeItem != null && beforeItem.getRemark() != null) {
                    newData.setRemark(beforeItem.getRemark());
                }
            });

        // 将版本号加一
        dischargeDayDOS.forEach(item -> {
            item.setId(null);
            item.setCurrentDay(DateUtils.parseDate(item.getYear() + "-" + item.getMonth() + "-" + item.getDay()));
            item.setDataType(dataType);
            item.setStationType(stationDO.getStationType());
            item.setLatest(LatestEnum.LATEST.getType());
            item.setVersion(oldVersion.get() + 1);
        });

        UpdateWrapper<StationDayDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(StationDayDO::getStationId, EXCEL.get())
                .eq(StationDayDO::getStationType, stationDO.getStationType())
                .eq(StationDayDO::getDataType, dataType)
                .eq(StationDayDO::getLatest, LatestEnum.LATEST.getType())
                .eq(StationDayDO::getVersion, oldVersion.get())
                .eq(StationDayDO::getDeleted, Boolean.FALSE);
        StationDayDO entity = new StationDayDO();
        entity.setLatest(LatestEnum.HISTORY.getType());
        stationDayMapper.update(entity,updateWrapper);

        stationDayMapper.delete(new LambdaQueryWrapperX<StationDayDO>()
                .eq(StationDayDO::getStationId, EXCEL.get())
                .eq(StationDayDO::getDataType, dataType)
                .eq(StationDayDO::getLatest, LatestEnum.HISTORY.getType())
        );
        }finally {
            //最后统一处理nextVersion
            if (CollectionUtil.isNotEmpty(dischargeDayDOS)) {
//                dischargeDayDOS.forEach(item->item.setVersion(nextVersion));
                dischargeDayDOS.forEach(item->{
                    if(nextVersion>1){
                        item.setVersion(nextVersion-1);
                    }else{
                        item.setVersion(nextVersion);
                    }
                });
            }
        }
    }

    protected void updateOtherData(String year, String yearRemark, List<YearForm> excelData) {
        StationDO stationDO = stationMapper.selectById(EXCEL.get());
        Long stationId = stationDO.getId();
        Integer dataTypeOfDay = DAYENUM.getCode();
        Integer dataTypeOfMonth = MONTHENUM.getCode();
        Integer dataTypeOfYear = YEARENUM.getCode();

        List<DischargeMonthImportModel> dischargeMonthImportModel= generateMonthList(year, excelData, 0);
        if (CollectionUtils.isNotEmpty(dischargeMonthImportModel)) {
            List<StationMonthDO> data = BeanUtils.toBean(dischargeMonthImportModel, StationMonthDO.class);
            if (CollectionUtils.isNotEmpty(data)) {

                Integer nextVersion = stationDataLogService.getNextVersion(stationId, dataTypeOfMonth);
                try {
                    List<Integer> years = new ArrayList<>();
                    Set<String> currentIdentify = new HashSet<>(data.size());
                    // 默认版本为0
                    for (int i = 0; i < data.size(); i++) {
                        StationMonthDO item = data.get(i);
                        if (!years.contains(item.getYear())) {
                            years.add(item.getYear());
                        }
                        // 判断为更新接口用
                        if (null == item.getStationId()) {
                            item.setStationId(EXCEL.get());
                        }
                        item.setId(null);
                        item.setDataType(dataTypeOfMonth);
                        item.setStationType(stationDO.getStationType());
                        item.setCurrentDay(DateUtils.getFirstDayOfMonth(item.getYear().toString(), item.getMonth().toString()));
                        item.setVersion(1);
                        item.setLatest(LatestEnum.LATEST.getType());
                        currentIdentify.add(item.getYear() + "-" + item.getMonth());
                    }
                    // 没有历史数据则不处理
                    LambdaQueryWrapperX<StationMonthDO> queryWrapperX = new LambdaQueryWrapperX<>();
                    queryWrapperX.eq(StationMonthDO::getStationId, EXCEL.get())
                            .eq(StationMonthDO::getDataType, dataTypeOfMonth)
                            .eq(StationMonthDO::getLatest, LatestEnum.LATEST.getType())
                            .eq(StationMonthDO::getDeleted, Boolean.FALSE);
                    List<StationMonthDO> beforeData = stationMonthMapper.selectList(queryWrapperX);
                    if (CollectionUtils.isNotEmpty(beforeData)) {
                        // 创建beforeData的映射表用于查找备注
                        Map<String, StationMonthDO> beforeDataMap = beforeData.stream()
                                .collect(Collectors.toMap(
                                        item -> item.getYear() + "-" + item.getMonth(),
                                        Function.identity()
                                ));

                        // 赋默认值，避免全量导入时无法正常赋值情况
                        AtomicInteger oldVersion = new AtomicInteger(beforeData.get(0).getVersion());
                        // 过滤掉本次新增的数据，并加入入库集合中
                        beforeData.stream().filter(item -> !currentIdentify.contains(item.getYear() + "-" + item.getMonth())).forEach(oldData -> {
                            oldVersion.set(oldData.getVersion());
                            oldData.setId(null);
                            oldData.setLatest(LatestEnum.LATEST.getType());
                            data.add(oldData);
                        });
                        // 将beforeData中的备注复制到sandContentMonthDOS中相同年月的记录
                        data.forEach(newData -> {
                            String key = newData.getYear() + "-" + newData.getMonth();
                            StationMonthDO beforeItem = beforeDataMap.get(key);
                            if (beforeItem != null ) {
                                newData.setAverageValueRemark(beforeItem.getAverageValueRemark());
                                newData.setMaxValueRemark(beforeItem.getMaxValueRemark());
                                newData.setMinValueRemark(beforeItem.getMinValueRemark());
                            }
                        });
                        // 将版本号加一
                        data.forEach(item -> {
                            item.setId(null);
                            item.setCurrentDay(DateUtils.getFirstDayOfMonth(item.getYear().toString(), item.getMonth().toString()));
                            item.setStationType(stationDO.getStationType());
                            item.setDataType(dataTypeOfMonth);
                            item.setLatest(LatestEnum.LATEST.getType());
                            item.setVersion(oldVersion.get() + 1);
                        });
                        // 更新历史数据
                        UpdateWrapper<StationMonthDO> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.lambda().eq(StationMonthDO::getStationId, EXCEL.get())
                                .eq(StationMonthDO::getLatest, LatestEnum.LATEST.getType())
                                .eq(StationMonthDO::getVersion, oldVersion.get())
                                .eq(StationMonthDO::getDataType, dataTypeOfMonth)
                                .eq(StationMonthDO::getDeleted, Boolean.FALSE);
                        StationMonthDO entity = new StationMonthDO();
                        entity.setLatest(LatestEnum.HISTORY.getType());
                        stationMonthMapper.update(entity, updateWrapper);

                        stationMonthMapper.delete(new LambdaQueryWrapperX<StationMonthDO>()
                                .eq(StationMonthDO::getStationId, EXCEL.get())
                                .eq(StationMonthDO::getLatest, LatestEnum.HISTORY.getType())
                                .eq(StationMonthDO::getDataType, dataTypeOfMonth)
                        );
                    }
                } finally {
                    //最后统一处理nextVersion
                    if (CollectionUtil.isNotEmpty(data)) {
//                        data.forEach(item->item.setVersion(nextVersion));
                        data.forEach(item->{
                            if(nextVersion>1){
                                item.setVersion(nextVersion-1);
                            }else{
                                item.setVersion(nextVersion);
                            }
                        });
                        stationMonthMapper.insertBatch(data);
                    }
                }
            }
        }


        //年
        List<DischargeYearImportModel> yearImportModel = new ArrayList<>();
        generateYear(year, excelData,yearImportModel);
        Integer yearVersion=0;
//        ArrayList<DischargeYearImportModel> yearImportModel = Lists.newArrayList(generateYearModel(year, excelData, dischargeYearImportModels));
        if (CollectionUtils.isNotEmpty(yearImportModel)) {
            List<StationYearDO> data = BeanUtils.toBean(yearImportModel, StationYearDO.class);
            if (CollectionUtils.isNotEmpty(data)) {

                Integer nextVersion = stationDataLogService.getNextVersion(stationId, dataTypeOfYear);
                try {
                    List<Integer> years = new ArrayList<>();
                    Set<String> currentIdentify = new HashSet<>(data.size());
                    // 默认版本为0
                    data.forEach(item -> {
                        if (!years.contains(item.getYear())) {
                            years.add(item.getYear());
                        }
                        // 判断为更新接口用
                        if (null == item.getStationId()) {
                            item.setStationId(EXCEL.get());
                        }
                        item.setId(null);
                        item.setDataType(dataTypeOfYear);
                        item.setStationType(stationDO.getStationType());
                        item.setVersion(1);
                        item.setLatest(LatestEnum.LATEST.getType());
                        if (!currentIdentify.add(item.getYear() + "")) {
                            throw exception(DATA_REPEAT, item.getYear() + "年");
                        }
                    });
                    // 没有历史数据则不处理
                    QueryWrapperX<StationYearDO> queryWrapperX = new QueryWrapperX<>();
                    queryWrapperX.lambda().eq(StationYearDO::getStationId, EXCEL.get())
                            .eq(StationYearDO::getDataType, dataTypeOfYear)
                            .eq(StationYearDO::getLatest, LatestEnum.LATEST.getType())
                            .eq(StationYearDO::getDeleted, Boolean.FALSE);
                    List<StationYearDO> beforeData = stationYearMapper.selectList(queryWrapperX);
                    if (CollectionUtils.isNotEmpty(beforeData)) {

                        // 创建beforeData的映射表用于查找备注
                        Map<Integer, StationYearDO> beforeDataMap = beforeData.stream()
                                .collect(Collectors.toMap(
                                        StationYearDO::getYear,
                                        Function.identity()
                                ));
                        // 赋默认值，避免全量导入时无法正常赋值情况
                        AtomicInteger oldVersion = new AtomicInteger(beforeData.get(0).getVersion());
                        yearVersion = oldVersion.get() + 1;
                        // 过滤掉本次新增的数据，并加入入库集合中
                        beforeData.stream().filter(item -> !currentIdentify.contains(item.getYear() + "")).forEach(oldData -> {
                            oldVersion.set(oldData.getVersion());
                            oldData.setId(null);
                            oldData.setLatest(LatestEnum.LATEST.getType());
                            data.add(oldData);
                        });
                        // 将beforeData中的备注复制到sandContentYearDOS中相同年的记录
                        data.forEach(newData -> {
                            StationYearDO beforeItem = beforeDataMap.get(newData.getYear());
                            if (beforeItem != null && beforeItem.getRemark() != null) {
                                newData.setRemark(beforeItem.getRemark());
                            }
                        });
                        // 将版本号加一
                        data.forEach(item -> {
                            item.setId(null);
                            item.setStationType(stationDO.getStationType());
                            item.setDataType(dataTypeOfYear);
                            item.setLatest(LatestEnum.LATEST.getType());
                            item.setVersion(oldVersion.get() + 1);
                        });
                        // 更新历史数据
                        UpdateWrapper<StationYearDO> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.lambda().eq(StationYearDO::getStationId, EXCEL.get())
                                .eq(StationYearDO::getDataType, dataTypeOfYear)
                                .eq(StationYearDO::getLatest, LatestEnum.LATEST.getType())
                                .eq(StationYearDO::getVersion, oldVersion.get())
                                .eq(StationYearDO::getDeleted, Boolean.FALSE);
                        StationYearDO entity = new StationYearDO();
                        entity.setLatest(LatestEnum.HISTORY.getType());
                        stationYearMapper.update(entity, updateWrapper);
                        stationYearMapper.delete(new LambdaQueryWrapperX<StationYearDO>()
                                .eq(StationYearDO::getStationId, EXCEL.get())
                                .eq(StationYearDO::getLatest, LatestEnum.HISTORY.getType())
                                .eq(StationYearDO::getDataType, YEARENUM.getCode())
                        );
                    }
                } finally {
                    //最后统一处理nextVersion
                    if (CollectionUtil.isNotEmpty(data)) {
//                        data.forEach(item->item.setVersion(nextVersion));
                        data.forEach(item->{
                            if(nextVersion>1){
                                item.setVersion(nextVersion-1);
                            }else{
                                item.setVersion(nextVersion);
                            }
                        });
                        stationYearMapper.insertBatch(data);
                    }
                }
            }

        }
        // 插入日变更记录
        Integer nextVersionOfDay = stationDataLogService.getNextVersion(stationId, dataTypeOfDay);
        if(nextVersionOfDay>1){
            nextVersionOfDay=nextVersionOfDay-1;
        }
        StationDataLogAddModel dayLog = StationDataLogAddModel.builder().stationType(stationDO.getStationType()).stationId(EXCEL.get())
                .currentVersion(nextVersionOfDay).dataType(dataTypeOfDay).build();
        stationDataLogService.addStationDataLog(dayLog);
        // 插入月变更记录
        Integer nextVersionOfMonth = stationDataLogService.getNextVersion(stationId, dataTypeOfMonth);
        if(nextVersionOfMonth>1){
            nextVersionOfMonth=nextVersionOfMonth-1;
        }
        Integer nextVersionOfYear = stationDataLogService.getNextVersion(stationId, dataTypeOfYear);
        if(nextVersionOfYear>1){
            nextVersionOfYear=nextVersionOfYear-1;
        }
        if (CollectionUtils.isNotEmpty(dischargeMonthImportModel)) {
            StationDataLogAddModel monthLog = StationDataLogAddModel.builder().stationType(stationDO.getStationType()).stationId(EXCEL.get())
                    .currentVersion(nextVersionOfMonth).dataType(dataTypeOfMonth).build();
            stationDataLogService.addStationDataLog(monthLog);
        }
        //
        if (CollectionUtils.isNotEmpty(yearImportModel)) {
            StationDataLogAddModel yearLog = StationDataLogAddModel.builder().stationType(stationDO.getStationType()).stationId(EXCEL.get())
                    .currentVersion(nextVersionOfYear).dataType(dataTypeOfYear).build();
            stationDataLogService.addStationDataLog(yearLog);
        }

        // 生成年鉴记录
        Integer currentVersion = stationDataLogService.findStationDataLogDOLatestCurrentVersionByStationType(EXCEL.get(), stationDO.getStationType(), BOOK.getCode());
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(stationDO.getStationType()).stationId(EXCEL.get())
                .year(year).yearRemark(yearRemark).currentVersion(currentVersion + 1)
                .dataType(BOOK.getCode())
                .dayVersion(nextVersionOfDay)
                .monthVersion(nextVersionOfMonth)
                .yearVersion(nextVersionOfYear).build());
    }

    protected void processOtherData(String year, String yearRemark, List<YearForm> excelData) {
        StationDO stationDO = stationMapper.selectById(EXCEL.get());
        Long stationId = stationDO.getId();
        Integer dataTypeOfDay = DAYENUM.getCode();
        Integer dataTypeOfMonth = MONTHENUM.getCode();
        Integer dataTypeOfYear = YEARENUM.getCode();
        List<DischargeMonthImportModel> dischargeMonthImportModel= generateMonthList(year, excelData, 0);
        if (CollectionUtils.isNotEmpty(dischargeMonthImportModel)) {
            List<StationMonthDO> data = BeanUtils.toBean(dischargeMonthImportModel, StationMonthDO.class);
            if (CollectionUtils.isNotEmpty(data)) {
                List<Integer> years = new ArrayList<>();
                Set<String> currentIdentify = new HashSet<>(data.size());
                // 默认版本为0
                for (int i = 0; i < data.size(); i++) {
                    StationMonthDO item = data.get(i);
                    if (!years.contains(item.getYear())) {
                        years.add(item.getYear());
                    }
                    // 判断为更新接口用
                    if (null == item.getStationId()) {
                        item.setStationId(EXCEL.get());
                    }
                    item.setId(null);
                    item.setDataType(dataTypeOfMonth);
                    item.setStationType(stationDO.getStationType());
                    item.setCurrentDay(DateUtils.getFirstDayOfMonth(item.getYear().toString(),item.getMonth().toString()));
                    item.setVersion(1);
                    item.setLatest(LatestEnum.LATEST.getType());
                    currentIdentify.add(item.getYear() + "-" + item.getMonth());
                }
                // 没有历史数据则不处理
                LambdaQueryWrapperX<StationMonthDO> queryWrapperX = new LambdaQueryWrapperX<>();
                queryWrapperX.eq(StationMonthDO::getStationId, EXCEL.get())
                        .eq(StationMonthDO::getDataType, dataTypeOfMonth)
                        .eq(StationMonthDO::getLatest, LatestEnum.LATEST.getType())
                        .eq(StationMonthDO::getDeleted, Boolean.FALSE);
                List<StationMonthDO> beforeData = stationMonthMapper.selectList(queryWrapperX);
                if (CollectionUtils.isNotEmpty(beforeData)) {
                    // 赋默认值，避免全量导入时无法正常赋值情况
                    AtomicInteger oldVersion = new AtomicInteger(beforeData.get(0).getVersion());
                    // 过滤掉本次新增的数据，并加入入库集合中
                    beforeData.stream().filter(item -> !currentIdentify.contains(item.getYear() + "-" + item.getMonth())).forEach(oldData -> {
                        oldVersion.set(oldData.getVersion());
                        oldData.setId(null);
                        oldData.setLatest(LatestEnum.LATEST.getType());
                        data.add(oldData);
                    });
                    // 将版本号加一
                    data.forEach(item -> {
                        item.setId(null);
                        item.setCurrentDay(DateUtils.getFirstDayOfMonth(item.getYear().toString(),item.getMonth().toString()));
                        item.setStationType(stationDO.getStationType());
                        item.setDataType(dataTypeOfMonth);
                        item.setLatest(LatestEnum.LATEST.getType());
                        item.setVersion(oldVersion.get() + 1);
                    });
                    // 更新历史数据
                    UpdateWrapper<StationMonthDO> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().eq(StationMonthDO::getStationId, EXCEL.get())
                            .eq(StationMonthDO::getLatest, LatestEnum.LATEST.getType())
                            .eq(StationMonthDO::getVersion, oldVersion.get())
                            .eq(StationMonthDO::getDataType, dataTypeOfMonth)
                            .eq(StationMonthDO::getDeleted, Boolean.FALSE);
                    StationMonthDO entity = new StationMonthDO();
                    entity.setLatest(LatestEnum.HISTORY.getType());
                    stationMonthMapper.update(entity, updateWrapper);
                }
                stationMonthMapper.insertBatch(data);
            }
        }
        //年
        List<DischargeYearImportModel> yearImportModel = new ArrayList<>();
        generateYearModel(year, excelData,yearImportModel);
        Integer yearVersion=0;
//        ArrayList<DischargeYearImportModel> yearImportModel = Lists.newArrayList(generateYearModel(year, excelData, dischargeYearImportModels));
        if (CollectionUtils.isNotEmpty(yearImportModel)) {
            List<StationYearDO> data = BeanUtils.toBean(yearImportModel, StationYearDO.class);
            if (CollectionUtils.isNotEmpty(data)) {
                List<Integer> years = new ArrayList<>();
                Set<String> currentIdentify = new HashSet<>(data.size());
                // 默认版本为0
                data.forEach(item -> {
                    if (!years.contains(item.getYear())) {
                        years.add(item.getYear());
                    }
                    // 判断为更新接口用
                    if (null == item.getStationId()) {
                        item.setStationId(EXCEL.get());
                    }
                    item.setId(null);
                    item.setDataType(dataTypeOfYear);
                    item.setStationType(stationDO.getStationType());
                    item.setVersion(1);
                    item.setLatest(LatestEnum.LATEST.getType());
                    if (!currentIdentify.add(item.getYear() + "")) {
                        throw exception(DATA_REPEAT, item.getYear() + "年");
                    }
                });
                // 没有历史数据则不处理
                QueryWrapperX<StationYearDO> queryWrapperX = new QueryWrapperX<>();
                queryWrapperX.lambda().eq(StationYearDO::getStationId, EXCEL.get())
                        .eq(StationYearDO::getDataType, dataTypeOfYear)
                        .eq(StationYearDO::getLatest, LatestEnum.LATEST.getType())
                        .eq(StationYearDO::getDeleted, Boolean.FALSE);
                List<StationYearDO> beforeData = stationYearMapper.selectList(queryWrapperX);
                if (CollectionUtils.isNotEmpty(beforeData)) {
                    // 赋默认值，避免全量导入时无法正常赋值情况
                    AtomicInteger oldVersion = new AtomicInteger(beforeData.get(0).getVersion());
                    yearVersion= oldVersion.get() + 1;
                    // 过滤掉本次新增的数据，并加入入库集合中
                    beforeData.stream().filter(item -> !currentIdentify.contains(item.getYear() + "")).forEach(oldData -> {
                        oldVersion.set(oldData.getVersion());
                        oldData.setId(null);
                        oldData.setLatest(LatestEnum.LATEST.getType());
                        data.add(oldData);
                    });
                    // 将版本号加一
                    data.forEach(item -> {
                        item.setId(null);
                        item.setStationType(stationDO.getStationType());
                        item.setDataType(dataTypeOfYear);
                        item.setLatest(LatestEnum.LATEST.getType());
                        item.setVersion(oldVersion.get() + 1);
                    });
                    // 更新历史数据
                    UpdateWrapper<StationYearDO> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().eq(StationYearDO::getStationId, EXCEL.get())
                            .eq(StationYearDO::getDataType, dataTypeOfYear)
                            .eq(StationYearDO::getLatest, LatestEnum.LATEST.getType())
                            .eq(StationYearDO::getVersion, oldVersion.get())
                            .eq(StationYearDO::getDeleted, Boolean.FALSE);
                    StationYearDO entity = new StationYearDO();
                    entity.setLatest(LatestEnum.HISTORY.getType());
                    stationYearMapper.update(entity,updateWrapper);
                }
                stationYearMapper.insertBatch(data);
            }

        }
        // 插入日变更记录
        Integer nextVersionOfDay = stationDataLogService.getNextVersion(stationId, dataTypeOfDay);
        if(nextVersionOfDay>1){
            nextVersionOfDay=nextVersionOfDay-1;
        }
        StationDataLogAddModel dayLog = StationDataLogAddModel.builder().stationType(stationDO.getStationType()).stationId(EXCEL.get())
                .currentVersion(nextVersionOfDay).dataType(dataTypeOfDay).build();
        stationDataLogService.addStationDataLog(dayLog);
        // 插入月变更记录
        Integer nextVersionOfMonth = stationDataLogService.getNextVersion(stationId, dataTypeOfMonth);
        if(nextVersionOfMonth>1){
            nextVersionOfMonth=nextVersionOfMonth-1;
        }
        Integer nextVersionOfYear = stationDataLogService.getNextVersion(stationId, dataTypeOfYear);
        if(nextVersionOfYear>1){
            nextVersionOfYear=nextVersionOfYear-1;
        }
        if (CollectionUtils.isNotEmpty(dischargeMonthImportModel)) {
            StationDataLogAddModel monthLog = StationDataLogAddModel.builder().stationType(stationDO.getStationType()).stationId(EXCEL.get())
                    .currentVersion(nextVersionOfMonth).dataType(dataTypeOfMonth).build();
            stationDataLogService.addStationDataLog(monthLog);
        }
        //
        if (CollectionUtils.isNotEmpty(yearImportModel)) {

            StationDataLogAddModel yearLog = StationDataLogAddModel.builder().stationType(stationDO.getStationType()).stationId(EXCEL.get())
                    .currentVersion(nextVersionOfYear).dataType(dataTypeOfYear).build();
            stationDataLogService.addStationDataLog(yearLog);
        }

        // 生成年鉴记录
        Integer currentVersion = stationDataLogService.findStationDataLogDOLatestCurrentVersionByStationType(EXCEL.get(), stationDO.getStationType(), BOOK.getCode());
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(stationDO.getStationType()).stationId(EXCEL.get())
                .year(year).yearRemark(yearRemark).currentVersion(currentVersion + 1)
                .dataType(BOOK.getCode())
                .dayVersion(nextVersionOfDay)
                .monthVersion(nextVersionOfMonth)
                .yearVersion(nextVersionOfYear).build());
    }
    private void saveDataLog(List<Integer> years, Integer version) {
        if (CollectionUtils.isEmpty(years)) {
            return;
        }
        // 插入变更记录
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get())
                .currentVersion(version).dataType(YEARENUM.getCode()).build());
        // 获取年鉴最新版本
        Integer yearMarkCurrentVersion = stationDataLogService.findStationDataLogDOLatestCurrentVersionByStationType(EXCEL.get(), StationEnum.HYDROLOGIC.getType(), BOOK.getCode());
        // 获取月版本
        Integer monthVersion = stationMonthMapper.getMonthLatestVersion(EXCEL.get(),MONTHENUM.getCode());
        Integer dayVersion = stationDayMapper.getDayLatestVersion(EXCEL.get(),DAYENUM.getCode());
        // 获取月版本
        years.sort(Comparator.comparing(Integer::valueOf));
        for (int i = 0; i < years.size(); i++) {
            Integer item = years.get(i);
            // 生成年鉴记录、日操作记录
            stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().year(String.valueOf(item))
                    .stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get()).currentVersion(yearMarkCurrentVersion + 1 + i)
                    .dataType(BOOK.getCode()).dayVersion(dayVersion).monthVersion(monthVersion).yearVersion(version).build());
        }
    }
    private List<DischargeMonthImportModel> generateMonthList(String year, List<YearForm> importDataList, int headRemarkLine) {
        Map<String, DischargeMonthImportModel> monthModelMap = new HashMap<>();
        for (int i = 33 + headRemarkLine; i < 36 + headRemarkLine && i < importDataList.size(); i++) {
            YearForm importModel = importDataList.get(i);
            if (importModel == null ) {
                continue;
            }
            if (StringUtils.isEmpty(importModel.getValue1()) && StringUtils.isEmpty(importModel.getValue2()) && StringUtils.isEmpty(importModel.getValue3()) &&
                    StringUtils.isEmpty(importModel.getValue4()) && StringUtils.isEmpty(importModel.getValue5()) && StringUtils.isEmpty(importModel.getValue6()) &&
                            StringUtils.isEmpty(importModel.getValue7()) && StringUtils.isEmpty(importModel.getValue8()) && StringUtils.isEmpty(importModel.getValue9()) &&
                                    StringUtils.isEmpty(importModel.getValue10()) && StringUtils.isEmpty(importModel.getValue11()) && StringUtils.isEmpty(importModel.getValue12())) {
                continue;
            }
            fillMonthModel(monthModelMap, year, 1 + "", importModel.getValue1(), i);
            fillMonthModel(monthModelMap, year, 2 + "", importModel.getValue2(), i);
            fillMonthModel(monthModelMap, year, 3 + "", importModel.getValue3(), i);
            fillMonthModel(monthModelMap, year, 4 + "", importModel.getValue4(), i);
            fillMonthModel(monthModelMap, year, 5 + "", importModel.getValue5(), i);
            fillMonthModel(monthModelMap, year, 6 + "", importModel.getValue6(), i);
            fillMonthModel(monthModelMap, year, 7 + "", importModel.getValue7(), i);
            fillMonthModel(monthModelMap, year, 8 + "", importModel.getValue8(), i);
            fillMonthModel(monthModelMap, year, 9 + "", importModel.getValue9(), i);
            fillMonthModel(monthModelMap, year, 10 + "", importModel.getValue10(), i);
            fillMonthModel(monthModelMap, year, 11 + "", importModel.getValue11(), i);
            fillMonthModel(monthModelMap, year, 12 + "", importModel.getValue12(), i);
        }
        return new ArrayList<>(monthModelMap.values());
    }


    private void fillMonthModel(Map<String, DischargeMonthImportModel> monthImportModelMap, String year, String month, String value, int lineNumber) {
        DischargeMonthImportModel monthImportModel = monthImportModelMap.get(month);
        if (Objects.isNull(monthImportModel)) {
            monthImportModel = new DischargeMonthImportModel();
        }
        monthImportModel.setYear(year);
        monthImportModel.setMonth(month);
        if (lineNumber == 33) {
            monthImportModel.setAverageValue(value);
        }
        if (lineNumber == 34) {
            monthImportModel.setMaxValue(value);
        }
        if (lineNumber == 35) {
            monthImportModel.setMaxValueDate(value);
        }
        monthImportModelMap.put(month, monthImportModel);
    }

    private DischargeYearImportModel generateYear(String year, List<YearForm> importDataList, List<DischargeYearImportModel> dischargeYearImportModels) {
        DischargeYearImportModel yearImportModel = new DischargeYearImportModel();
        YearForm yearData1 = importDataList.get(37);
        YearForm yearData2 = importDataList.get(38);
        YearForm yearData3 = importDataList.get(39);
        yearImportModel.setMaxValue(yearData1.getValue1());
        yearImportModel.setMaxValueDate(yearData1.getValue6());
        yearImportModel.setAverageValue(yearData1.getValue11());

        yearImportModel.setTotalValue(yearData2.getValue1());
        yearImportModel.setDischargeModulus(yearData2.getValue11());

        yearImportModel.setRemark(yearData3.getValue1());
        yearImportModel.setYear(Integer.valueOf(year));
        if (Objects.isNull(yearData1.getValue4()) && Objects.isNull(yearData1.getValue6()) && Objects.isNull(yearData1.getValue4())
        && Objects.isNull(yearData2.getValue4()) && Objects.isNull(yearData2.getValue10()) && Objects.isNull(yearData3.getValue1())){
                return null;
        }
        dischargeYearImportModels.add(yearImportModel);
        return yearImportModel;
    }
    private DischargeYearImportModel generateYearModel(String year, List<YearForm> importDataList, List<DischargeYearImportModel> dischargeYearImportModels) {
        DischargeYearImportModel yearImportModel = new DischargeYearImportModel();
        YearForm yearData1 = importDataList.get(36);
        YearForm yearData2 = importDataList.get(37);
        YearForm yearData3 = importDataList.get(38);
        yearImportModel.setMaxValue(yearData1.getValue4());
        yearImportModel.setMaxValueDate(yearData1.getValue6());
        yearImportModel.setAverageValue(yearData1.getValue10());

        yearImportModel.setTotalValue(yearData2.getValue4());
        yearImportModel.setDischargeModulus(yearData2.getValue10());

        yearImportModel.setRemark(yearData3.getValue1());
        yearImportModel.setYear(Integer.valueOf(year));
        if (Objects.isNull(yearData1.getValue4()) && Objects.isNull(yearData1.getValue6()) && Objects.isNull(yearData1.getValue4())
                && Objects.isNull(yearData2.getValue4()) && Objects.isNull(yearData2.getValue10()) && Objects.isNull(yearData3.getValue1())){
            return null;
        }
        dischargeYearImportModels.add(yearImportModel);
        return yearImportModel;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile file, Long stationId, Integer type) throws IOException {
        EXCEL.set(stationId);
        String lockKey = String.format(PlanningDesignConstants.HYDROLOGIC_DISCHARGE_DAY_IMPORT_KEY, stationId);

        if (ShowTypeEnum.LIST.getType().equals(type)) {
            importData(file, lockKey, 1, null);
        } else {
            importYearData(file, lockKey, 0, 2, 0);
        }
    }

    @Override
    protected BaseMapperX<StationDayDO> getMapper() {

        return stationDayMapper;
    }
}
