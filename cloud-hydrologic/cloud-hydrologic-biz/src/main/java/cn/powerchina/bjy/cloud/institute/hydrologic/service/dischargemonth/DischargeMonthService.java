package cn.powerchina.bjy.cloud.institute.hydrologic.service.dischargemonth;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargemonth.vo.DischargeMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargemonth.vo.DischargeMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargemonth.vo.DischargeMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.dischargemonth.DischargeMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.DischargeMonthImportModel;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 水文站-输沙率-月输沙率特征值 Service 接口
 *
 * <AUTHOR>
 */
public interface DischargeMonthService {

    /**
     * 创建水文站-输沙率-月输沙率特征值
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDischargeMonth(@Valid DischargeMonthSaveReqVO createReqVO);

    /**
     * 更新水文站-输沙率-月输沙率特征值
     *
     * @param updateReqVOList 更新信息
     */
    void updateDischargeMonth(@Valid DischargeMonthSaveReqVO updateReqVO);

    /**
     * 删除水文站-输沙率-月输沙率特征值
     *
     * @param id 编号
     */
    void deleteDischargeMonth(Long id);

    /**
     * 获得水文站-输沙率-月输沙率特征值
     *
     * @param id 编号
     * @return 水文站-输沙率-月输沙率特征值
     */
    DischargeMonthDO getDischargeMonth(Long id);

    /**
     * 获得水文站-输沙率-月输沙率特征值分页
     *
     * @param pageReqVO 分页查询
     * @return 水文站-输沙率-月输沙率特征值分页
     */
    PageResult<DischargeMonthRespVO> getDischargeMonthPage(DischargeMonthPageReqVO pageReqVO);

    /**
     * 获取最新版本号
     * 无需确定“年”，因为每次均为全量版本号自增，年、月同理
     *
     * @param stationId 站点id
     * @return 最新版本号
     */
    Integer getLatestVersion(Long stationId);

    /**
     * 处理月维度数据
     * 注：涉及到分布式锁已经ThreadLocal，目前只给年鉴导入、更新功能用
     *
     * @param importModels 月维度数据
     */
    void processImportModel(List<DischargeMonthImportModel> importModels);

    /**
     * 根据年和版本查找
     *
     * @param stationId
     * @param year
     * @param version
     * @return
     */
    List<DischargeMonthDO> findByVersionAndYear(Long stationId, String year, Integer version);

    /**
     * 导入Excel
     *
     * @param file 文件
     * @param stationId 站点id
     */
    void importData(MultipartFile file, Long stationId) throws IOException;

    /**
     * 导出月序列
     *
     * @param response
     * @param pageReqVO
     */
    void exportDischargeMonthExcel(HttpServletResponse response, DischargeMonthPageReqVO pageReqVO);

}