package cn.powerchina.bjy.cloud.institute.hydrologic.model;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description: 雨量站导入降水-各时段最大降水量(分钟时段)特征值excel model
 * @Author: yhx
 * @CreateDate: 2024/7/6
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class TideDayImportModel {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12233")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21525")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站", example = "1")
    private Integer stationType;

    @Schema(description = "数据类型", example = "2")
    private Integer dataType;

    @Schema(description = "年")
    private String year;

    @Schema(description = "月")
    private String month;

    @Schema(description = "日")
    @ExcelProperty(index = 0)
    private String day;

    @Schema(description = "潮别")
    @ExcelProperty(index = 1)
    private String value1;

    @Schema(description = "潮位")
    @ExcelProperty(index = 2)
    private String value2;

    @Schema(description = "时分")
    @ExcelProperty(index = 3)
    private String value3;

    @Schema(description = "潮差")
    @ExcelProperty(index = 4)
    private String value4;

    @Schema(description = "历时")
    @ExcelProperty(index = 5)
    private String value5;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "记录时间")
    private LocalDate currentDay;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;
}
