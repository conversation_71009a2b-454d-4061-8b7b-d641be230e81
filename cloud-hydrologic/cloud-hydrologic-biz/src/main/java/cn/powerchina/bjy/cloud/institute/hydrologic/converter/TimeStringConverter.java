package cn.powerchina.bjy.cloud.institute.hydrologic.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 时间字符串转换器
 * 用于处理Excel中时间格式的读取问题
 * 
 * <AUTHOR>
 */
@Slf4j
public class TimeStringConverter implements Converter<String> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 读取时的转换逻辑
     */
    @Override
    public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        try {
            // 处理不同类型的单元格数据
            switch (cellData.getType()) {
                case STRING:
                    String stringValue = cellData.getStringValue();
                    log.debug("读取到字符串类型时间: {}", stringValue);
                    return normalizeTimeFormat(stringValue);
                    
                case NUMBER:
                    // Excel中的时间可能被读取为数字（小数形式）
                    BigDecimal numberValue = cellData.getNumberValue();
                    log.debug("读取到数字类型时间: {}", numberValue);
                    return convertExcelTimeToString(numberValue);
                    
                case BOOLEAN:
                    log.warn("时间字段读取到布尔类型: {}", cellData.getBooleanValue());
                    return "";
                    
                default:
                    log.warn("未知的单元格类型: {}", cellData.getType());
                    return "";
            }
        } catch (Exception e) {
            log.error("时间转换异常: ", e);
            return "";
        }
    }

    /**
     * 写入时的转换逻辑
     */
    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(value);
    }

    /**
     * 将Excel的时间数字转换为时间字符串
     * Excel中时间存储为小数，1.0表示24小时
     */
    private String convertExcelTimeToString(BigDecimal timeValue) {
        if (timeValue == null) {
            return "";
        }
        
        try {
            // Excel时间是以天为单位的小数
            // 例如：0.0表示00:00，0.5表示12:00，1.0表示24:00
            double timeDouble = timeValue.doubleValue();
            
            // 处理超过1的情况（可能是日期+时间）
            if (timeDouble >= 1.0) {
                timeDouble = timeDouble - Math.floor(timeDouble); // 只取小数部分
            }
            
            // 转换为总秒数
            int totalSeconds = (int) Math.round(timeDouble * 24 * 60 * 60);
            
            // 计算小时和分钟
            int hours = totalSeconds / 3600;
            int minutes = (totalSeconds % 3600) / 60;
            
            // 处理24:00的情况
            if (hours >= 24) {
                hours = hours % 24;
            }
            
            String result = String.format("%d:%02d", hours, minutes);
            log.debug("Excel时间数字 {} 转换为: {}", timeValue, result);
            return result;
            
        } catch (Exception e) {
            log.error("Excel时间数字转换失败: {}", timeValue, e);
            return timeValue.toString();
        }
    }

    /**
     * 标准化时间格式
     */
    private String normalizeTimeFormat(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return "";
        }
        
        timeStr = timeStr.trim();
        
        try {
            // 尝试解析各种可能的时间格式
            if (timeStr.matches("\\d{1,2}:\\d{2}")) {
                // 格式：H:mm 或 HH:mm
                String[] parts = timeStr.split(":");
                int hours = Integer.parseInt(parts[0]);
                int minutes = Integer.parseInt(parts[1]);
                return String.format("%d:%02d", hours, minutes);
            }
            
            if (timeStr.matches("\\d{1,2}:\\d{2}:\\d{2}")) {
                // 格式：H:mm:ss 或 HH:mm:ss，忽略秒
                String[] parts = timeStr.split(":");
                int hours = Integer.parseInt(parts[0]);
                int minutes = Integer.parseInt(parts[1]);
                return String.format("%d:%02d", hours, minutes);
            }

            /*
            // 尝试解析为LocalTime
            if (timeStr.contains(":")) {
                LocalTime time = LocalTime.parse(timeStr, DateTimeFormatter.ofPattern("H:mm"));
                return String.format("%d:%02d", time.getHour(), time.getMinute());
            }
             */
            
            // 如果是纯数字，可能是Excel的时间序列值
            if (timeStr.matches("\\d+(\\.\\d+)?")) {
                BigDecimal timeValue = new BigDecimal(timeStr);
                return convertExcelTimeToString(timeValue);
            }
            
        } catch (Exception e) {
            log.warn("时间格式解析失败: {}", timeStr, e);
        }
        
        // 如果都解析失败，返回原值
        log.debug("时间格式标准化保持原值: {}", timeStr);
        return timeStr;
    }
}
