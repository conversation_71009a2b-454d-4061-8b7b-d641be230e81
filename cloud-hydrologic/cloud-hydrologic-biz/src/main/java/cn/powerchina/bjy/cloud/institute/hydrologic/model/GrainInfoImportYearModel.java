package cn.powerchina.bjy.cloud.institute.hydrologic.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 年平均悬移质颗粒级配
 *
 * <AUTHOR>
 **/
@Data
@Accessors(chain = false)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GrainInfoImportYearModel {

    /**
     * 年
     */
    @ExcelProperty(index = 0)
    private Integer year;

    /**
     * 0.005粒径级（mm）
     */
    @ExcelProperty(index = 1)
    private String grain005;

    /**
     * 0.007粒径级（mm）
     */
    @ExcelProperty(index = 2)
    private String grain007;

    /**
     * 0.01粒径级（mm）
     */
    @ExcelProperty(index = 3)
    private String grain01;

    /**
     * 0.025粒径级（mm）
     */
    @ExcelProperty(index = 4)
    private String grain025;

    /**
     * 0.05粒径级（mm）
     */
    @ExcelProperty(index = 5)
    private String grain05;

    /**
     * 0.1粒径级（mm）
     */
    @ExcelProperty(index = 6)
    private String grain1;

    /**
     * 0.25粒径级（mm）
     */
    @ExcelProperty(index = 7)
    private String grain25;

    /**
     * 0.5粒径级（mm）
     */
    @ExcelProperty(index = 8)
    private String grain5;

    /**
     * 1.0粒径级（mm）
     */
    @ExcelProperty(index = 9)
    private String grain10;

    /**
     * 2.0粒径级（mm）
     */
    @ExcelProperty(index = 10)
    private String grain20;

    /**
     * 3.0粒径级（mm）
     */
    @ExcelProperty(index = 11)
    private String grain30;

    /**
     * 中数粒径(mm)", example = "24567
     */
    @ExcelProperty(index = 12)
    private String grainMid;

    /**
     * 平均粒径(mm)
     */
    @ExcelProperty(index = 13)
    private String grainAverage;

    /**最大粒径(mm)
     */
    @ExcelProperty(index = 14)
    private String grainMax;
}
