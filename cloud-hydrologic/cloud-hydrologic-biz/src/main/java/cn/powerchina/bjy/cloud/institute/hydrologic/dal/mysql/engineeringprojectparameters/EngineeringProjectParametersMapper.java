package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.engineeringprojectparameters;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo.EngineeringProjectParametersPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.engineeringprojectparameters.EngineeringProjectParametersDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 工程项目信息-设计参数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EngineeringProjectParametersMapper extends BaseMapperX<EngineeringProjectParametersDO> {
    default PageResult<EngineeringProjectParametersDO> selectPage(EngineeringProjectParametersPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EngineeringProjectParametersDO>()
                .eqIfPresent(EngineeringProjectParametersDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(EngineeringProjectParametersDO::getValue1, reqVO.getValue1())
                .eqIfPresent(EngineeringProjectParametersDO::getValue2, reqVO.getValue2())
                .eqIfPresent(EngineeringProjectParametersDO::getValue4, reqVO.getValue4())
                .eqIfPresent(EngineeringProjectParametersDO::getValue6, reqVO.getValue6())
                .eqIfPresent(EngineeringProjectParametersDO::getValue8, reqVO.getValue8())
                .eqIfPresent(EngineeringProjectParametersDO::getValue10, reqVO.getValue10())
                .eqIfPresent(EngineeringProjectParametersDO::getValue12, reqVO.getValue12())
                .eqIfPresent(EngineeringProjectParametersDO::getNum, reqVO.getNum())
                .betweenIfPresent(EngineeringProjectParametersDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EngineeringProjectParametersDO::getId));
    }
}
