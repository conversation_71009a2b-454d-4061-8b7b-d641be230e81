package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationresult.dataprocessor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.bedsedimentgradingresult.vo.BedSedimentGradingResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.bedsedimentgradingresult.BedSedimentGradingResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.bedsedimentgradingresult.BedSedimentGradingResultMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.BedSedimentGradingResultImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.RainFallStationExcerptImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.DataProcessor;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.bedsedimentgradingresult.BedSedimentGradingResultService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationdatalog.StationDataLogService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

@Slf4j
@Service
public class BedSedimentGradingResultProcessorImpl implements DataProcessor<BedSedimentGradingResultSaveReqVO> {

    @Autowired
    private StationDataLogService stationDataLogService;
    @Autowired
    private BedSedimentGradingResultService bedSedimentGradingResultService;
    @Autowired
     private BedSedimentGradingResultMapper bedSedimentGradingResultMapper;

    @Override
    public void update(BedSedimentGradingResultSaveReqVO updateReqVO) {
        List<BedSedimentGradingResultSaveReqVO.BedSedimentGradingResultData> dataList = updateReqVO.getDataList();
        // 1. 获取更新和删除的数据列表
        List<Long> deleteIds = updateReqVO.getDeleteIds();

        // 2. 若无更新或删除内容则直接返回
        if (CollectionUtil.isEmpty(dataList) && CollectionUtil.isEmpty(deleteIds)) {
            log.warn("没有要更新或删除的内容 stationId=[{}], dataType=[{}]", updateReqVO.getStationId(), updateReqVO.getDataType());
            return;
        }
        //剔除掉要删除的数据只保留要更新的元素，从更新数据list中： 处理一种特殊情况（前端操作了更新之后又删除）
        if (CollectionUtil.isNotEmpty(dataList) && CollectionUtil.isNotEmpty(deleteIds)) {
            dataList = dataList.stream().filter(item -> !deleteIds.contains(item.getId())).toList();
        }
        if (CollectionUtil.isNotEmpty(dataList)) {
            dataList.forEach(item -> {
                boolean validDateTime = DateUtils.isValidDate(item.getYear(), item.getMonth(), item.getDay());
                if (BooleanUtil.isFalse(validDateTime)) {
                    throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
                }
            });
        }
        List<BedSedimentGradingResultImportModel> importDataList = new ArrayList<>();
        List<Long> updateIdList = new ArrayList<>();
        //判断本次提交的数据是否跟库中已有的数据有重复
        if (CollectionUtil.isNotEmpty(dataList)) {
            for (BedSedimentGradingResultSaveReqVO.BedSedimentGradingResultData bedSedimentGradingResultData : dataList) {

                Long id = bedSedimentGradingResultData.getId();
                if (Objects.isNull(id)) {
                    continue;
                }
                updateIdList.add(id);

                //更新场景重复判断
                BedSedimentGradingResultDO bedResultDO = bedSedimentGradingResultService.selectOne(
                        updateReqVO.getStationId(),
                        updateReqVO.getDataType(),
                        bedSedimentGradingResultData.getYear(),
                        bedSedimentGradingResultData.getMonth(),
                        bedSedimentGradingResultData.getDay()
                );
                if (Objects.nonNull(bedResultDO) && !bedResultDO.getId().equals(id)) {
                    throw ServiceExceptionUtil.exception(ErrorCodeConstants.DATA_FORMAT_ERROR, "");
                }
            }

            //获取更新的内容
            importDataList = updateReqVO.getDataList().stream()
                    .map(item -> BedSedimentGradingResultImportModel.builder()
                            .year(item.getYear())
                            .month(item.getMonth())
                            .day(item.getDay())
                            .remark(item.getRemark())
                            .analysisMethod(item.getAnalysisMethod())
                            .sampleMethod(item.getSampleMethod())
                            .value002(item.getValue002())
                            .value004(item.getValue004())
                            .value008(item.getValue008())
                            .value016(item.getValue016())
                            .value031(item.getValue031())
                            .value062(item.getValue062())
                            .value125(item.getValue125())
                            .value250(item.getValue250())
                            .value500(item.getValue500())
                            .value1000(item.getValue1000())
                            .value2000(item.getValue2000())
                            .value4000(item.getValue4000())
                            .value8000(item.getValue8000())
                            .medianDiameter(item.getMedianDiameter())
                            .meanDiameter(item.getMeanDiameter())
                            .maxDiameter(item.getMaxDiameter())
                            .build())
                    .toList();
        }
        dayDataToDbBuilder(updateReqVO, importDataList, updateIdList, false);
    }

    @Override
    public void importExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException {
        // 获取excel内容
        List<BedSedimentGradingResultImportModel> importDataList = EasyExcel.read(file.getInputStream(),
                        BedSedimentGradingResultImportModel.class, null)
                .sheet()
                .headRowNumber(3)
                .doReadSync();

        if (CollectionUtils.isEmpty(importDataList)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
        }
        if (CollectionUtil.isNotEmpty(importDataList)) {
            for (int i = 0; i < importDataList.size(); i++) {
                String remark = importDataList.get(i).getRemark();
                if (remark != null && remark.length() > 20) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, "备注不能超过20个字符");
                }
                BedSedimentGradingResultImportModel current = importDataList.get(i);
                if (current.getMedianDiameter() != null) {
                    current.setMedianDiameter(String.format("%.4f", Double.parseDouble(current.getMedianDiameter())));
                }
                if (current.getMeanDiameter() != null) {
                    current.setMeanDiameter(String.format("%.4f", Double.parseDouble(current.getMeanDiameter())));
                }
                if (current.getMaxDiameter() != null) {
                    current.setMaxDiameter(String.format("%.4f", Double.parseDouble(current.getMaxDiameter())));
                }
                if (current.getYear() == null) {
                    for (int j = i - 1; j >= 0; j--) {
                        BedSedimentGradingResultImportModel prev = importDataList.get(j);
                        if (prev.getYear() != null) {
                            current.setYear(prev.getYear());
                            break;
                        }
                    }
                }
                if (current.getMonth() == null) {
                    for (int j = i - 1; j >= 0; j--) {
                        BedSedimentGradingResultImportModel prev = importDataList.get(j);
                        if (prev.getMonth() != null) {
                            current.setMonth(prev.getMonth());
                            break;
                        }
                    }
                }
                // 补全日期
                if (current.getDay() == null) {
                    for (int j = i - 1; j >= 0; j--) {
                        BedSedimentGradingResultImportModel prev = importDataList.get(j);
                        if (prev.getDay() != null) {
                            current.setDay(prev.getDay());
                            break;
                        }

                    }
                }
            }
        }
        // 检查数据重复
//        Map<String, Long> countMap = importDataList.stream()
//                .collect(Collectors.groupingBy(
//                        item -> item.getYear() + "-" + item.getMonth() + "-" + item.getDay(),
//                        Collectors.counting()
//                ));
//
//        countMap.forEach((k, v) -> {
//            if (v > 1) {
//                throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
//            }
//        });
        check(importDataList);
        // 构建保存请求
        BedSedimentGradingResultSaveReqVO bedSedimentGradingResultSaveReqVO = new BedSedimentGradingResultSaveReqVO();
        bedSedimentGradingResultSaveReqVO.setStationId(stationId);
        bedSedimentGradingResultSaveReqVO.setStationType(stationType);
        bedSedimentGradingResultSaveReqVO.setDataType(dataType);

        // 调用数据入库方法
        dayDataToDbBuilder(bedSedimentGradingResultSaveReqVO, importDataList, null, true);
    }

    private void check(List<BedSedimentGradingResultImportModel> importDataList) {
        for (int i = 0; i < importDataList.size()-1; i++) {
           if (!StringUtils.isBlank(importDataList.get(i).getValue002())) {
               if (!importDataList.get(i).getValue002().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR,  + 4, "0.002mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue002());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.002mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue004())) {
               if (!importDataList.get(i).getValue004().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.004mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue004());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.004mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue008())) {
               if (!importDataList.get(i).getValue008().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.008mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue008());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.008mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue016())) {
               if (!importDataList.get(i).getValue016().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.016mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue016());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.016mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue031())) {
               if (!importDataList.get(i).getValue031().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.031mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue031());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.031mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue062())) {
               if (!importDataList.get(i).getValue062().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.062mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue062());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.062mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue125())) {
               if (!importDataList.get(i).getValue125().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.125mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue125());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.125mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue250())) {
               if (!importDataList.get(i).getValue250().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.250mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue250());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.250mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue500())) {
               if (!importDataList.get(i).getValue500().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.500mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue500());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "0.500mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue1000())) {
               if (!importDataList.get(i).getValue1000().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "1.000mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue1000());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "1.000mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue2000())) {
               if (!importDataList.get(i).getValue2000().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "2.000mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue2000());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "2.000mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue4000())) {
               if (!importDataList.get(i).getValue4000().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "4.000mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue4000());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "4.000mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getValue8000())) {
               if (!importDataList.get(i).getValue8000().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "8.000mm百分数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getValue8000());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "8.000mm百分数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getMedianDiameter())) {
               if (!importDataList.get(i).getMedianDiameter().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "粒径中位数必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getMedianDiameter());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "粒径中位数不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getMeanDiameter())) {
               if (!importDataList.get(i).getMeanDiameter().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "粒径平均值必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getMeanDiameter());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "粒径平均值不能大于100");
               }
           }
           if (!StringUtils.isBlank(importDataList.get(i).getMaxDiameter())) {
               if (!importDataList.get(i).getMaxDiameter().matches("^\\d+(\\.\\d+)?$")) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "粒径最大值必须为数字");
               }
               double value = Double.parseDouble(importDataList.get(i).getMaxDiameter());
               if (value > 100) {
                   throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 4, "粒径最大值不能大于100");
               }
           }
       }
    }

    @Override
    public List<Integer> listYear(Long stationId, Integer dataType) {
        return  bedSedimentGradingResultMapper.listYear(stationId,dataType);
    }

    private void dayDataToDbBuilder(BedSedimentGradingResultSaveReqVO updateReqVO,
                                    List<BedSedimentGradingResultImportModel> importDataList,
                                    List<Long> updateIdList,
                                    boolean importFlag) {
        Long stationId = updateReqVO.getStationId();
        Integer stationType = updateReqVO.getStationType();
        Integer dataType = updateReqVO.getDataType();

        // 获取版本号（如有需要可自定义实现）
        Integer versions = fetchVersions(stationId, stationType, dataType);

        // 组装数据
        List<BedSedimentGradingResultDO> resultList = dataBuilder(stationId, stationType, dataType, importDataList, versions);

        // 处理ID集合
        List<Long> idList = new ArrayList<>();
        if (updateIdList != null) {
            idList.addAll(updateIdList);
        }
        // 如果VO有删除ID
        if (updateReqVO.getId() != null) {
            idList.add(updateReqVO.getId());
        }

        // 写入数据
        dataUpdate(stationId, stationType, dataType, resultList, idList, importFlag);
    }

    private void dataUpdate(Long stationId, Integer stationType, Integer dataType,
                            List<BedSedimentGradingResultDO> resultList,
                            List<Long> idList, boolean importFlag) {
        // 批量导入
        if (importFlag) {
            bedSedimentGradingResultService.batchImport(resultList);
        } else {
            // 更新/删除
            bedSedimentGradingResultService.insertBatch(resultList, idList);
        }
        // 如有统计信息更新等其它操作，可在此补充
    }
    /**
     * 获取下一个版本号
     */
    private Integer fetchVersions(Long stationId, Integer stationType, Integer dataType) {
        return stationDataLogService.getNextVersion(stationId, dataType);
    }

    private List<BedSedimentGradingResultDO> dataBuilder(Long stationId, Integer stationType, Integer dataType,
                                                         List<BedSedimentGradingResultImportModel> importDataList, Integer nextVersion) {
        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();
//        return importDataList.stream()
//                .filter(item -> DateUtils.isValidDate(item.getYear() + "-" + item.getMonth() + "-" + item.getDay(), DateUtils.PATTERN))
//                .map(item -> {
        List<BedSedimentGradingResultDO> resultDOList = new ArrayList<>();
        for (BedSedimentGradingResultImportModel item : importDataList) {
            if (!DateUtils.isValidDate(item.getYear() + "-" + item.getMonth() + "-" + item.getDay(), DateUtils.PATTERN)) {
                throw new ServiceException(ErrorCodeConstants.DATE_FORMAT_ERROR);
            }
                    BedSedimentGradingResultDO resultDO = new BedSedimentGradingResultDO();
                    resultDO.setStationId(stationId);
                    resultDO.setStationType(stationType);
                    resultDO.setDataType(dataType);
                    resultDO.setYear(item.getYear());
                    resultDO.setMonth(item.getMonth());
                    resultDO.setDay(item.getDay());
                    resultDO.setValue002((item.getValue002()));
                    resultDO.setValue004((item.getValue004()));
                    resultDO.setValue008((item.getValue008()));
                    resultDO.setValue016((item.getValue016()));
                    resultDO.setValue031((item.getValue031()));
                    resultDO.setValue062((item.getValue062()));
                    resultDO.setValue125((item.getValue125()));
                    resultDO.setValue250((item.getValue250()));
                    resultDO.setValue500((item.getValue500()));
                    resultDO.setValue1000((item.getValue1000()));
                    resultDO.setValue2000((item.getValue2000()));
                    resultDO.setValue4000((item.getValue4000()));
                    resultDO.setValue8000((item.getValue8000()));
                    resultDO.setMedianDiameter((item.getMedianDiameter()));
                    resultDO.setMeanDiameter((item.getMeanDiameter()));
                    resultDO.setMaxDiameter((item.getMaxDiameter()));
                    resultDO.setSampleMethod(item.getSampleMethod());
                    resultDO.setAnalysisMethod(item.getAnalysisMethod());
                    resultDO.setRemark(item.getRemark());
                    resultDO.setCreator(loginUserId);
                    resultDO.setUpdater(loginUserId);
                    resultDO.setLatest(1);
            resultDOList.add(resultDO);
        }
        return resultDOList;
    }
}