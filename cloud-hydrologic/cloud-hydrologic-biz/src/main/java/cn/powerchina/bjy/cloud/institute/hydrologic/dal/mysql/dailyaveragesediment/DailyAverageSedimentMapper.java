package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.dailyaveragesediment;


import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.dailyaveragesediment.DailyAverageSedimentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 逐日平均含沙量 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DailyAverageSedimentMapper extends BaseMapperX<DailyAverageSedimentDO> {
    @Select("SELECT count(1) FROM hydrologic_daily_average_sediment x where x.station_id=#{stationId}  and x.station_type=#{stationType} and x.data_type=#{dataType} and x.value1 like '%#{year}%'")
    Integer selectByStationInfo(@Param("stationId") Long stationId, @Param("stationType") Integer stationType, @Param("dataType") Integer dataType,@Param("year") String year);
}
