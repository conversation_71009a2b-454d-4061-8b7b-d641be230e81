package cn.powerchina.bjy.cloud.institute.hydrologic.util;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 文件下载
 * @time 2024/8/30 10:29
 */
@Slf4j
public class FileUtil {
    public static boolean isExcelFile(MultipartFile file) {
        // 获取文件名
        String fileName = file.getOriginalFilename();

        // 检查文件名是否为空
        if (fileName == null) {
            return false;
        }

        // 获取文件后缀
        String fileExtension = getFileExtension(fileName);

        // 判断是否为 Excel 格式
        return "xls".equalsIgnoreCase(fileExtension) || "xlsx".equalsIgnoreCase(fileExtension);
    }

    private static String getFileExtension(String fileName) {
        // 获取最后一个点的索引
        int lastDotIndex = fileName.lastIndexOf(".");
        // 如果存在点且不是第一个字符
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1); // 返回后缀
        }
        return ""; // 无后缀
    }


    /**
     * 文件转字节流
     */
    public static byte[] fileToByteArray(File file) {
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] byteArray = new byte[(int) file.length()];
            fis.read(byteArray);
            return byteArray;
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0];
        }
    }

    /**
     * 批量下载文件
     *
     * @param fileUrls          文件 URL 列表
     * @param downloadDirectory 下载目录
     */
    public static void downloadFiles(List<String> fileUrls, String downloadDirectory) {
        for (String fileUrl : fileUrls) {
            String localFilePath = downloadDirectory + File.separator + extractFileName(fileUrl);
            downloadFile(fileUrl, localFilePath);
        }
    }

    /**
     * 批量下载文件
     */
    public static void downloadFiles(Map<String, String> fileMap) {
        fileMap.forEach((k, v) -> downloadFile(k, v));
    }

    /**
     * 下载单个文件
     *
     * @param fileUrl       文件 URL
     * @param localFilePath 本地文件路径
     */
    private static void downloadFile(String fileUrl, String localFilePath) {
        try {
            byte[] fileBytes = HttpUtil.downloadBytes(fileUrl);
            cn.hutool.core.io.FileUtil.writeBytes(fileBytes, localFilePath);
            log.info("文件下载成功：{}",localFilePath);
        } catch (Exception e) {
            log.error("文件下载失败：{}",fileUrl,e);
        }
    }

    /**
     * 从 URL 中提取文件名
     *
     * @param fileUrl 文件 URL
     * @return 文件名
     */
    public static String extractFileName(String fileUrl) {
        return fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
    }


    /**
     * 将 List 写入文件
     * @param list 要写入的 List
     * @param filePath 文件路径
     * @throws IOException 可能发生的 I/O 异常
     */
    public static void writeListToFile(List<?> list, String filePath) throws IOException {
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(filePath))) {
            oos.writeObject(list);
        }
    }

    /**
     * 从文件中读取 List
     * @param filePath 文件路径
     * @return 读取到的 List
     * @throws IOException 可能发生的 I/O 异常
     * @throws ClassNotFoundException 可能发生的类未找到异常
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> readListFromFile(String filePath) throws IOException, ClassNotFoundException {
        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(filePath))) {
            return (List<T>) ois.readObject();
        }
    }
}
