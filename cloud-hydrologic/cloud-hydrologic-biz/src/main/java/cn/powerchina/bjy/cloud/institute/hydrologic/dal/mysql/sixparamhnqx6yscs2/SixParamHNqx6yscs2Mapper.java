package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.sixparamhnqx6yscs2;

import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sixparamhnqx6yscs2.SixParamHNqx6yscs2DO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 六要素观测成果表(南通惠能HNqx6ys-cs2) Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SixParamHNqx6yscs2Mapper extends BaseMapperX<SixParamHNqx6yscs2DO> {

    /**
     * 根据多对“站点id”，“记录时间”查询
     * @param sixParamHNqx6yscs2DOList 多对“站点id”，“记录时间”
     * @return 六要素观测成果表(南通惠能HNqx6ys-cs2)列表
     */
    List<SixParamHNqx6yscs2DO> list(@Param("list") List<SixParamHNqx6yscs2DO> sixParamHNqx6yscs2DOList);

    /**
     * 查询记录时间是否已存在
     * @param stationId 站点id
     * @param sixParamHNqx6yscs2DOList 主键id，记录时间
     * @return 已存在的记录时间
     */
    List<SixParamHNqx6yscs2DO> listExistRecordTime(@Param("stationId") Long stationId, @Param("list") List<SixParamHNqx6yscs2DO> sixParamHNqx6yscs2DOList);

    /**
     * 查询分页数据
     * @param stationId 站点id
     * @param minTime 最小记录时间
     * @param maxTime 最大记录时间
     * @param version 版本号
     * @param offset 偏移量
     * @param limit 每页条数
     * @return 分页数据
     */
    List<SixParamHNqx6yscs2DO> list4Page(@Param("stationId") Long stationId, @Param("minTime") LocalDateTime minTime,
                                         @Param("maxTime") LocalDateTime maxTime, @Param("version") Integer version,
                                         @Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 查询分页数据条数
     * @param stationId 站点id
     * @param minTime 最小记录时间
     * @param maxTime 最大记录时间
     * @param version 版本号
     * @return 分页数据条数
     */
    Long count4Page(@Param("stationId") Long stationId, @Param("minTime") LocalDateTime minTime,
                       @Param("maxTime") LocalDateTime maxTime, @Param("version") Integer version);

    /**
     * 根据id列表删除
     * @param idList id列表
     */
    void deleteByIdList(@Param("list") List<Long> idList);
}
