package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.grainresult;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.grainresult.vo.GrainResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.grainresult.GrainResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.HydrologicChecker;
import org.apache.ibatis.annotations.Mapper;

/**
 * 水文-实测悬移质颗粒级配成果 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GrainResultMapper extends BaseMapperX<GrainResultDO>, HydrologicChecker<GrainResultDO> {

    default PageResult<GrainResultDO> selectPage(GrainResultPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GrainResultDO>()
                .eqIfPresent(GrainResultDO::getStationId, reqVO.getStationId())
                .eqIfPresent(GrainResultDO::getYear, reqVO.getYear())
                .eqIfPresent(GrainResultDO::getMonth, reqVO.getMonth())
                .eqIfPresent(GrainResultDO::getDay, reqVO.getDay())
                .eqIfPresent(GrainResultDO::getGrain005, reqVO.getGrain005())
                .eqIfPresent(GrainResultDO::getGrain007, reqVO.getGrain007())
                .eqIfPresent(GrainResultDO::getGrain01, reqVO.getGrain01())
                .eqIfPresent(GrainResultDO::getGrain025, reqVO.getGrain025())
                .eqIfPresent(GrainResultDO::getGrain05, reqVO.getGrain05())
                .eqIfPresent(GrainResultDO::getGrain1, reqVO.getGrain1())
                .eqIfPresent(GrainResultDO::getGrain25, reqVO.getGrain25())
                .eqIfPresent(GrainResultDO::getGrain5, reqVO.getGrain5())
                .eqIfPresent(GrainResultDO::getGrain10, reqVO.getGrain10())
                .eqIfPresent(GrainResultDO::getGrain20, reqVO.getGrain20())
                .eqIfPresent(GrainResultDO::getGrain30, reqVO.getGrain30())
                .eqIfPresent(GrainResultDO::getGrainMid, reqVO.getGrainMid())
                .eqIfPresent(GrainResultDO::getGrainAverage, reqVO.getGrainAverage())
                .eqIfPresent(GrainResultDO::getGrainMax, reqVO.getGrainMax())
                .eqIfPresent(GrainResultDO::getAverageSinkSpeed, reqVO.getAverageSinkSpeed())
                .eqIfPresent(GrainResultDO::getWaterTemperature, reqVO.getWaterTemperature())
                .eqIfPresent(GrainResultDO::getSamplingMethod, reqVO.getSamplingMethod())
                .eqIfPresent(GrainResultDO::getAnalysisMethod, reqVO.getAnalysisMethod())
                .eqIfPresent(GrainResultDO::getRemark, reqVO.getRemark())
                .eqIfPresent(GrainResultDO::getVersion, reqVO.getVersion())
                .eqIfPresent(GrainResultDO::getLatest, reqVO.getLatest())
                .betweenIfPresent(GrainResultDO::getCurrentDay, reqVO.getCurrentDay())
                .orderByAsc(GrainResultDO::getCurrentDay));
    }

}