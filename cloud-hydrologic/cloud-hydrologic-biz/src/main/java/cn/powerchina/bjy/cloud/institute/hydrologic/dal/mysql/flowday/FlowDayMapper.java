package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.flowday;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.flowday.vo.FlowDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.flowday.FlowDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.HydrologicChecker;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 水文站—逐日平均流量 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FlowDayMapper extends BaseMapperX<FlowDayDO>, HydrologicChecker<FlowDayDO> {

    default PageResult<FlowDayDO> selectPage(FlowDayPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FlowDayDO>()
                .eqIfPresent(FlowDayDO::getStationId, reqVO.getStationId())
                .eqIfPresent(FlowDayDO::getYear, reqVO.getYear())
                .eqIfPresent(FlowDayDO::getMonth, reqVO.getMonth())
                .eqIfPresent(FlowDayDO::getDay, reqVO.getDay())
                .eqIfPresent(FlowDayDO::getValue, reqVO.getValue())
                .eqIfPresent(FlowDayDO::getRemark, reqVO.getRemark())
                .eqIfPresent(FlowDayDO::getVersion, reqVO.getVersion())
                .eqIfPresent(FlowDayDO::getLatest, reqVO.getLatest())
                .betweenIfPresent(FlowDayDO::getCurrentDay, reqVO.getCurrentDay())
                .orderByAsc(FlowDayDO::getCurrentDay));
    }

    @Select("SELECT version FROM hydrologic_flow_day WHERE station_id = #{stationId} AND latest = 1 AND deleted = 0 limit 1")
    Integer getLatestVersion(@Param("stationId") Long stationId);

}