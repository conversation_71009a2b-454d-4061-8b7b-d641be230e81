package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.waterlevelguarantee;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelguarantee.vo.WaterLevelGuaranteePageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth.StationMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.waterlevelguarantee.WaterLevelGuaranteeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 水文站-水位-各保证率水位 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WaterLevelGuaranteeMapper extends BaseMapperX<WaterLevelGuaranteeDO> {

    default PageResult<WaterLevelGuaranteeDO> selectPage(WaterLevelGuaranteePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WaterLevelGuaranteeDO>()
                .eqIfPresent(WaterLevelGuaranteeDO::getStationId, reqVO.getStationId())
                .eqIfPresent(WaterLevelGuaranteeDO::getYear, reqVO.getYear())
                .eqIfPresent(WaterLevelGuaranteeDO::getMaxValue, reqVO.getMaxValue())
                .eqIfPresent(WaterLevelGuaranteeDO::getDay15, reqVO.getDay15())
                .eqIfPresent(WaterLevelGuaranteeDO::getDay30, reqVO.getDay30())
                .eqIfPresent(WaterLevelGuaranteeDO::getDay90, reqVO.getDay90())
                .eqIfPresent(WaterLevelGuaranteeDO::getDay180, reqVO.getDay180())
                .eqIfPresent(WaterLevelGuaranteeDO::getDay270, reqVO.getDay270())
                .eqIfPresent(WaterLevelGuaranteeDO::getMinValue, reqVO.getMinValue())
                .eqIfPresent(WaterLevelGuaranteeDO::getRemark, reqVO.getRemark())
                .eqIfPresent(WaterLevelGuaranteeDO::getVersion, reqVO.getVersion())
                .eqIfPresent(WaterLevelGuaranteeDO::getLatest, reqVO.getLatest())
                .betweenIfPresent(WaterLevelGuaranteeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WaterLevelGuaranteeDO::getId));
    }

    List<WaterLevelGuaranteeDO> listByStationIds(@Param("stationIds") List<Long> stationIds);

    int insertList(@Param("list")List<WaterLevelGuaranteeDO> list);


    List<Integer> listYear(@Param("stationId") Long stationId, @Param("dataType") Integer dataType);

    List<WaterLevelGuaranteeDO> selectListTemporaryData(@Param("stationId") Long stationId);

    Integer getLatestVersion(@Param("stationId") Long stationId);

    void batchInsert(@Param("list") List<WaterLevelGuaranteeDO> list);

    void updateBatchById(@Param("list") List<WaterLevelGuaranteeDO> list);

}