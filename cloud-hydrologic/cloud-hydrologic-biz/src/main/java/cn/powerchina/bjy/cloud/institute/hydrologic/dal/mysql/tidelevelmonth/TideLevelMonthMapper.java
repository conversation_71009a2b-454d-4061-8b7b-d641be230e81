package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.tidelevelmonth;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo.TideLevelMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelmonth.TideLevelMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelyear.TideLevelYearDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 水文站—潮位月统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TideLevelMonthMapper extends BaseMapperX<TideLevelMonthDO> {

    default PageResult<TideLevelMonthDO> selectPage(TideLevelMonthPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TideLevelMonthDO>()
                .eqIfPresent(TideLevelMonthDO::getStationId, reqVO.getStationId())
                .eqIfPresent(TideLevelMonthDO::getStationType, reqVO.getStationType())
                .eqIfPresent(TideLevelMonthDO::getDataType, reqVO.getDataType())
                .eqIfPresent(TideLevelMonthDO::getYear, reqVO.getYear())
                .eqIfPresent(TideLevelMonthDO::getMonth, reqVO.getMonth())
                .eqIfPresent(TideLevelMonthDO::getValue1, reqVO.getValue1())
                .eqIfPresent(TideLevelMonthDO::getValue2, reqVO.getValue2())
                .eqIfPresent(TideLevelMonthDO::getValue3, reqVO.getValue3())
                .eqIfPresent(TideLevelMonthDO::getValue4, reqVO.getValue4())
                .eqIfPresent(TideLevelMonthDO::getValue5, reqVO.getValue5())
                .eqIfPresent(TideLevelMonthDO::getValue6, reqVO.getValue6())
                .eqIfPresent(TideLevelMonthDO::getValue7, reqVO.getValue7())
                .eqIfPresent(TideLevelMonthDO::getValue8, reqVO.getValue8())
                .eqIfPresent(TideLevelMonthDO::getValue9, reqVO.getValue9())
                .eqIfPresent(TideLevelMonthDO::getValue10, reqVO.getValue10())
                .eqIfPresent(TideLevelMonthDO::getValue11, reqVO.getValue11())
                .eqIfPresent(TideLevelMonthDO::getValue12, reqVO.getValue12())
                .eqIfPresent(TideLevelMonthDO::getValue13, reqVO.getValue13())
                .eqIfPresent(TideLevelMonthDO::getRemark, reqVO.getRemark())
                .eqIfPresent(TideLevelMonthDO::getCurrentDay, reqVO.getCurrentDay())
                .eqIfPresent(TideLevelMonthDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(TideLevelMonthDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TideLevelMonthDO::getId));
    }

    @Update("update hydrologic_tide_level_month set version = '0' where station_id = #{stationId}  and data_type=#{dataType} and year=#{year} and month=#{month}")
    void updateMonthVersion(@Param("stationId")Long stationId, @Param("dataType")Integer dataType, @Param("year")String year, @Param("month")String month);

    @Select("select CONCAT(year,month) as year from hydrologic_tide_level_month where deleted = 0 and station_id = #{stationId} and data_type = #{dataType} group by year,month order by year asc")
    List<Integer> listYear(@Param("stationId") Long stationId, @Param("dataType") Integer dataType);

    @Update("update hydrologic_tide_level_month set latest = '0' where station_id = #{stationId}  and station_type=#{stationType} and data_type=#{dataType}and year=#{year} and month=#{month}")
    void updateHistoryVersion(@Param("stationId")Long stationId, @Param("stationType")Integer stationType, @Param("dataType")Integer dataType,@Param("year")String year, @Param("month")String month);

    @Select("SELECT * FROM hydrologic_tide_level_month WHERE station_id = #{stationId} AND data_type = #{dataType} " +
            "AND station_type = #{stationType} AND version = #{version} and year=#{year} and month=#{month} AND latest = 0 AND deleted = 1")
    List<TideLevelMonthDO> selectHis(@Param("stationId") Long stationId,
                                    @Param("dataType") Integer dataType,
                                    @Param("stationType") Integer stationType,
                                    @Param("version") Integer version,
                                    @Param("year") String year, @Param("month")String month);
}