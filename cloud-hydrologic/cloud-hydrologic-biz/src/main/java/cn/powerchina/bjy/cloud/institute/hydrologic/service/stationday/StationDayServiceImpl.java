package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationday;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.util.PageSortSqlUtil;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.almanac.vo.AlmanacModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo.DataLogVersionInfoVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationday.vo.StationDayModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationday.vo.StationDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationday.vo.StationDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationday.vo.StationDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationday.StationDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth.StationMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.suspendedsedimentgradingresult.SuspendedSedimentGradingResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.velocitywindresult.VelocityWindResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationdatalog.StationDataLogMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationday.StationDayMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.RainFallStationDayImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.StationDataLogAddModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.RedisService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationdatalog.StationDataLogService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.DataProcessor;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticalinfo.StatisticalInfoService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.symbol.SymbolService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.RedisUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 站点-日统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class StationDayServiceImpl implements StationDayService {

    @Resource
    private RedisService redisService;
    @Resource
    private StationDayMapper stationDayMapper;
    @Resource
    private StationDataLogService stationDataLogService;
    @Autowired
    private StatisticalInfoService statisticalInfoService;
    @Resource
    private SymbolService symbolService;
    @Resource
    private StationDataLogMapper stationDataLogMapper;

    @Override
    public Long createStationDay(StationDaySaveReqVO createReqVO) {
        // 插入
        StationDayDO stationDay = BeanUtils.toBean(createReqVO, StationDayDO.class);
        stationDayMapper.insert(stationDay);
        // 返回
        return stationDay.getId();
    }

    /**
     * @param updateReqVO
     * @desc 更新、批量删除
     * <AUTHOR>
     * @time 2024/9/7 08:22
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStationDay(StationDaySaveReqVO updateReqVO) {
        //查询站点是否存在
        Long stationId = updateReqVO.getStationId();
        Integer dataType = updateReqVO.getDataType();
        Integer stationType = updateReqVO.getStationType();

        if (Objects.isNull(stationId)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        RLock lock = redisService.acquireDistributedLock(RedisUtils.getLockKey(stationId, stationType, dataType));
        try {
            //判断更新、批量删除提交的数据是否为空
            List<StationDaySaveReqVO.StationDayData> dataList = updateReqVO.getDataList();
            if (CollectionUtils.isEmpty(dataList) && CollectionUtil.isEmpty(updateReqVO.getDeleteIds())) {
                throw exception(ErrorCodeConstants.STATION_DATA_UPDATE_CONTENT_EMPTY_EXISTS_ERROR);
            }

            if (CollectionUtil.isNotEmpty(dataList)) {
                dataList.forEach(item->{
                    if (BooleanUtil.isFalse(DateUtils.isValidDate(item.getYear(),item.getMonth(),item.getDay()))) {
                        throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
                    }
                });
            }

            //根据dataType获取数据处理器：不同的datatype处理方式可能不通，但也有相同的地方
            DataProcessor dataProcessor = (DataProcessor) SpringUtil.getBean(StationDataTypeEnumV2.fromCode(dataType).getImportModel());
            try {
                dataProcessor.update(updateReqVO);
            }catch (ServiceException e){
                throw e;
            } catch (Throwable e) {
                log.info("未知异常：",e);
                throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void deleteStationDay(Long id) {
        // 校验存在
        validateStationDayExists(id);
        // 删除
        stationDayMapper.deleteById(id);
    }

    private void validateStationDayExists(Long id) {
        if (stationDayMapper.selectById(id) == null) {
//            throw exception(STATION_DAY_NOT_EXISTS);
        }
    }

    @Override
    public StationDayDO getStationDay(Long id) {
        return stationDayMapper.selectById(id);
    }

    @Override
    public PageResult<StationDayDO> getStationDayPage(StationDayPageReqVO pageReqVO) {
        //如果查询更新记录logId不为空
        Long logId = pageReqVO.getLogId();
        Integer dataType = pageReqVO.getDataType();
        Long stationId = pageReqVO.getStationId();
        Integer stationType = pageReqVO.getStationType();
        Long count = stationDataLogMapper.selectCount(new LambdaQueryWrapperX<StationDataLogDO>()
                .gtIfPresent(StationDataLogDO::getId, logId)
                .eq(StationDataLogDO::getStationId, stationId)
                .eq(StationDataLogDO::getDataType, dataType)
                .eq(StationDataLogDO::getStationType, stationType)
        );
        if(count==0){
            logId=null;
        }
        if (Objects.nonNull(logId)) {
            StationDataLogDO stationDataLogDO = stationDataLogService.findById(pageReqVO.getLogId());
            if (Objects.isNull(stationDataLogDO)) {
                return PageResult.empty();
            }
//            pageReqVO.setVersion(stationDataLogDO.getCurrentVersion());
//            Integer version = stationDataLogDO.getCurrentVersion();
//            List<StationDayDO>  hisdos = stationDayMapper.selectHis(stationId,dataType,stationType,version);
//            return new PageResult<>(hisdos, (long) hisdos.size());
            pageReqVO.setVersion(stationDataLogDO.getCurrentVersion());
            PageResult<StationDayDO> page=new PageResult<>();
            Integer pageno = (pageReqVO.getPageNo()-1)*pageReqVO.getPageSize();
            Integer pagesize = pageReqVO.getPageSize()*pageReqVO.getPageNo();
            List<StationDayDO> stationDOList = stationDayMapper.selectPagesql(pageReqVO.getStationId(), pageno, pagesize, stationDataLogDO.getDataType(), stationDataLogDO.getCurrentVersion());
            Long countm=stationDayMapper.selectCountsql(pageReqVO.getStationId(), stationDataLogDO.getDataType(), stationDataLogDO.getCurrentVersion());
            page.setList(stationDOList);
            page.setTotal(countm);
            return page;
        } else {
            pageReqVO.setLatest(LatestEnum.LATEST.getType());
            return stationDayMapper.selectPage(pageReqVO);
        }
    }

    @Override
    public void importStationDayExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) {

        RLock lock = redisService.acquireDistributedLock(RedisUtils.getLockKey(stationId, stationType, dataType));
        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }
            List<StationDayModel> importDataList = EasyExcel.read(file.getInputStream(), StationDayModel.class, null).sheet().headRowNumber(0).doReadSync();
            if(importDataList.get(0).getValue().contains("(g/m3)")){
                checkList(importDataList);
                file=uploadFile(file.getOriginalFilename(), importDataList);
            }

            DataProcessor dataProcessor = (DataProcessor) SpringUtil.getBean(StationDataTypeEnumV2.fromCode(dataType).getImportModel());
            dataProcessor.importExcel(file, stationId, stationType, dataType);

        } catch (IOException e) {
            log.error("importStationRainYearFeatureExcel--->error.", e);
        } finally {
            lock.unlock();
        }

    }
    private MultipartFile uploadFile(String fileName, List<StationDayModel> list) throws IOException {
        // 使用内存方式创建Excel文件
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        cn.hutool.poi.excel.ExcelWriter writer = ExcelUtil.getWriter();
        writer.write(list, false);
        writer.flush(outputStream);
        writer.close();
        // 构造MultipartFile对象
        return new MockMultipartFile(
                "file",
                fileName,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray()
        );
    }

    private void checkList(List<StationDayModel> list) throws IOException {
        for(int i=1;i<list.size();i++) {
            StationDayModel importModel = list.get(i);
                if (importModel.getValue() != null && importModel.getValue().matches("^[0-9]+(\\.[0-9]+)?$")) {
                    importModel.setValue(String.valueOf(Double.parseDouble(importModel.getValue()) / 1000));
            }
        }
    }


    /**
     * 备份上一个版本数据、先剔除本次要修改、更新的数据，然后将本次更新的数据插入
     *
     * @param stationId
     * @param stationType
     * @param dataType
     * @param stationDayList  本次更新的数据
     * @param idList          删除 id
     * @param nextVersionInfo
     * @desc 批量插入数据、更新历史数据
     * <AUTHOR>
     * @time 2024/8/20 09:02
     */
    @Override
    public void insertBatch(Long stationId, Integer stationType, Integer dataType, List<StationDayDO> stationDayList, List<Long> idList, DataLogVersionInfoVO nextVersionInfo) {

        List<StationDayDO> insertList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(stationDayList)) {
            insertList.addAll(stationDayList);
        }

        LambdaQueryWrapper<StationDayDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StationDayDO::getStationId,stationId);
        queryWrapper.eq(StationDayDO::getDataType,dataType);
        queryWrapper.eq(StationDayDO::getLatest,LatestEnum.LATEST.getType());
        List<StationDayDO> preDayList = stationDayMapper.selectList(queryWrapper);
        // 更新当前数据为历史版本
        Integer version ;
        if(preDayList.size()>0){
            version = preDayList.get(0).getVersion();
        }else{
            version = 0;
        }
        //更新当前数据为历史版本
        stationDayMapper.updateHistoryVersion(stationId, stationType, dataType);
        stationDayMapper.delete(new LambdaQueryWrapperX<StationDayDO>()
                .eq(StationDayDO::getLatest, LatestEnum.HISTORY.getType())
                .eq(StationDayDO::getStationId, stationId)
                .eq(StationDayDO::getDataType, dataType)
                .eq(StationDayDO::getStationType, stationType));

        //判断当前版本
        if (CollectionUtil.isNotEmpty(preDayList)) {
            //获取上一个版本的数据集
            String loginUserId = WebFrameworkUtils.getLoginUserId().toString();

            //挑选出本次没有未修改的数据，作为最新版本数据
            List<StationDayDO> stationDayDOS = preDayList.stream().filter(item -> (!idList.contains(item.getId())))
                    .peek(item -> {
                        item.setVersion(version +1);
                        item.setLatest(LatestEnum.LATEST.getType());
                        item.setId(null);
                        item.setCreator(loginUserId);
                        item.setUpdater(loginUserId);
                    }).toList();

            //插入的数据分为：本次修改的、本次未修改的
            //该部分为：本次未修改的部分
            insertList.addAll(stationDayDOS);
        }



        //插入新数据
        if (CollectionUtil.isNotEmpty(insertList)) {
            // 统一设置版本号
            insertList.forEach(item->{
                item.setVersion(version +1);
                item.setLatest(LatestEnum.LATEST.getType());
            });


            batchInsert(insertList,BizConstants.BATCH_SIZE);
        }

        List<Integer> years=new ArrayList<>();
        if(CollectionUtil.isNotEmpty(insertList)) {
            years = insertList.stream().map(StationDayDO::getYear).distinct().sorted().toList();
        }

        //插入变更记录
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder()
                .stationType(stationType)
                .stationId(stationId)
                .dataType(dataType)
                .currentVersion(version + 1)
                .build());

        //更新索引数据年份信息
        statisticalInfoService.updateIndexDataYearInfo(stationType, dataType, stationId);

        //记录年鉴格式日志
        List<Integer> relationDataTypes = DataTypeRelation.getRelationDataTypes(dataType);
        if (relationDataTypes.get(0) > 0 && CollectionUtil.isNotEmpty(years)) {
//            //年鉴格式最新的一条为当前数据对应的版本
//            Integer yearBookVersion = stationDataLogService.getNextVersionV2(stationId, relationDataTypes.get(0));
//            Integer monthVersion = stationDataLogService.getCurrentVersion(stationId, relationDataTypes.get(2));
//            Integer yearVersion = stationDataLogService.getCurrentVersion(stationId, relationDataTypes.get(3));
//            Integer periodDayVersion = stationDataLogService.getCurrentVersion(stationId, relationDataTypes.get(4));
            //生成各个年鉴记录
            for (int i = 0; i < years.size(); i++) {
                //年鉴格式最新的一条为当前数据对应的版本
                Integer yearBookVersion = stationDataLogService.getNextVersionV3(stationId, relationDataTypes.get(0), years.get(i));
                Integer dayVersion = stationDataLogService.getNextVersionV3(stationId, relationDataTypes.get(1), years.get(i));
                Integer monthVersion = stationDataLogService.getNextVersionV3(stationId, relationDataTypes.get(2), years.get(i));
                Integer yearVersion = stationDataLogService.getNextVersionV3(stationId, relationDataTypes.get(3), years.get(i));
                Integer periodDayVersion = stationDataLogService.getNextVersionV3(stationId, relationDataTypes.get(4), years.get(i));
                Integer ver = yearBookVersion - 1;
                //获取年鉴备注
                LambdaQueryWrapper<StationDataLogDO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(StationDataLogDO::getStationId, stationId);
                wrapper.eq(StationDataLogDO::getDataType, relationDataTypes.get(0));
                wrapper.eq(StationDataLogDO::getStationType, stationType);
                wrapper.eq(StationDataLogDO::getYear, years.get(i).toString());
                wrapper.eq(StationDataLogDO::getCurrentVersion, version);
                List<StationDataLogDO> stationDataLogDOS = stationDataLogMapper.selectList(wrapper);
                String yearRemark = null;
                if (stationDataLogDOS.size() > 0){
                     yearRemark = stationDataLogDOS.get(0).getYearRemark();
            }
                stationDataLogService.addStationDataLog(StationDataLogAddModel.builder()
                        .stationType(stationType)
                        .stationId(stationId)
                        .dataType(relationDataTypes.get(0))
                        .currentVersion(version+1)
                        .year(years.get(i).toString())
                        .dayVersion(version+1)
                        .monthVersion(version+1)
                        .yearVersion(version +1 )
                        .yearRemark(yearRemark)
                        .periodDayVersion(version+1).build());
            }
        }
    }

    /**
     * 年鉴格式最新版本记录：按照年
     */
    private void insertYearBookDataLog(DataLogVersionInfoVO logVersionInfo, Integer dataType, List<StationDayDO> insertList, Long stationId, Integer stationType) {
        if (Objects.isNull(logVersionInfo)) {
            return;
        }

        List<Integer> relationDataTypes = DataTypeRelation.getRelationDataTypes(dataType);

        Integer yearBookDataType = relationDataTypes.get(0);
        //如果跟年鉴无关则直接返回
        if (yearBookDataType < 0) {
            return;
        }

        if(CollectionUtil.isNotEmpty(insertList)){
            List<String> years = insertList.stream().map(item -> String.valueOf(item.getYear())).distinct().sorted(Comparator.comparing(Integer::valueOf)).collect(Collectors.toList());
            //生成各个年鉴最新版本号记录
            stationDataLogService.addStationDataLogOfYearBooks(
                    stationId,
                    stationType,
                    yearBookDataType,
                    logVersionInfo.getYearMarkCurrentVersion() +1,
                    logVersionInfo.getDayVersion(),
                    logVersionInfo.getMonthVersion()-1,
                    logVersionInfo.getYearVersion()-1,
                    logVersionInfo.getPeriodDayVersion()-1
                    , years);
        }

    }


    @Override
    public void batchImport(List<StationDayDO> stationDayList, DataLogVersionInfoVO logVersionInfo) {
        if (CollectionUtil.isEmpty(stationDayList)) {
            return;
        }
        List<StationDayDO> insertList = new ArrayList<>();
        Map<String, List<StationDayDO>> groupedByKey = stationDayList.stream()
                .collect(Collectors.groupingBy(this::gainIndexKey
                ));

        Set<String> keySet = groupedByKey.keySet();

        StationDayDO entity = stationDayList.get(0);

        Long stationId = entity.getStationId();
        Integer stationType = entity.getStationType();
        Integer dataType = entity.getDataType();
        Integer version = entity.getVersion();

        //有历史版本
        if (version > 1) {
            //获取上一个版本的数据集
            List<StationDayDO> preVersionStationDayDOList = findStationDayDOByVersion(stationId, stationType, dataType, version -1 );
            //更新历史版本
            stationDayMapper.updateHistoryVersion(stationId, stationType, dataType);
            stationDayMapper.delete(new LambdaQueryWrapperX<StationDayDO>()
                    .eq(StationDayDO::getLatest, LatestEnum.HISTORY.getType())
                    .eq(StationDayDO::getStationId, stationId)
                    .eq(StationDayDO::getDataType, dataType)
                    .eq(StationDayDO::getStationType, stationType)
                    .eq(StationDayDO::getVersion, version -1));
            if (CollectionUtil.isNotEmpty(preVersionStationDayDOList)) {
                String loginUserId = WebFrameworkUtils.getLoginUserId().toString();

                //旧版本中如果跟本次导入重叠，则剔除掉
                List<StationDayDO> list = preVersionStationDayDOList.stream()
                        .filter(item -> !keySet.contains(gainIndexKey(item)))
                        .peek(item -> {
                            item.setVersion(item.getVersion() + 1);
                            item.setLatest(LatestEnum.LATEST.getType());
                            item.setId(null);
                            item.setCreator(loginUserId);
                            item.setUpdater(loginUserId);
                        }).toList();

                insertList.addAll(list);
            }
        }

        insertList.addAll(stationDayList);

        stationDayMapper.batchInsert(insertList);

        StationDayDO firstEntity = insertList.get(0);

        //插入变更记录：记录上个版本
//        stationDataLogService.addStationDataLog(
//                StationDataLogAddModel.builder()
//                        .stationType(stationType)
//                        .stationId(stationId)
//                        .dataType(Integer.valueOf(dataType))
//                        .currentVersion(firstEntity.getVersion())
//                        .build());


        //插入年鉴格式最新版版信息
        insertYearBookDataLog(logVersionInfo, dataType, insertList, stationId, stationType);
    }


    @NotNull
    private String gainIndexKey(StationDayDO item) {
        return item.getYear() + "_" + item.getMonth() + "_" + item.getDay();
    }

    @Override
    public List<StationDayDO> findStationDayDOByVersion(Long stationId, Integer stationType, Integer dataType, int version) {
        Map<String, Object> map = new HashMap<>(4);
        map.put("station_id", stationId);
        map.put("station_type", stationType);
        map.put("data_type", dataType);
        map.put("version", version);
        return stationDayMapper.selectByMap(map);
    }

    @Override
    public void exportStationDayExcel(StationDayPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        StationDataTypeEnumV2 dataTypeEnum = StationDataTypeEnumV2.fromCode(pageReqVO.getDataType());
        if (Objects.isNull(dataTypeEnum)) {
            throw new ServiceException(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase());
        }
        String fileName = URLEncoder.encode(dataTypeEnum.getDescription() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        List<StationDayDO> list = this.getStationDayPage(pageReqVO).getList();
        symbolService.addSymbol(list, dataTypeEnum.getCode());
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        List<StationDayRespVO> stationDayRespVOS = BeanUtils.toBean(list, StationDayRespVO.class);
        ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + dataTypeEnum.getCode() + PlanningDesignConstants.EXCEL_SUFFIX);

        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                // 利用模板的输出流
                .withTemplate(resource.getInputStream())
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
        excelWriter.fill(stationDayRespVOS, writeSheet);
        excelWriter.finish();
    }

    @Override
    public Integer findNextStationDayVersion(Long stationId, Integer stationType, Integer dataType) {
        return stationDataLogService.getNextVersion(stationId, dataType);
    }

    @Override
    public List<StationDayDO> findDOByVersionAndYear(Long stationId, String year, Integer dayVersion, Integer dataType) {
        Long count = stationDataLogMapper.selectCount(new LambdaQueryWrapperX<StationDataLogDO>()
                .gtIfPresent(StationDataLogDO::getCurrentVersion, dayVersion)
                .eq(StationDataLogDO::getStationId, stationId)
                .eq(StationDataLogDO::getDataType, dataType)
        );
        if(count==0){
            dayVersion=-1;
        }
        QueryWrapper<StationDayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_type", dataType);
        queryWrapper.eq("station_id", stationId);
        queryWrapper.eq("year", year);
        if(dayVersion==-1){
            queryWrapper.eq("latest", LatestEnum.LATEST.getType());
            queryWrapper.orderByAsc("month").orderByAsc("day");
            return stationDayMapper.selectList(queryWrapper);
        }else{
            return stationDayMapper.selectListDel(stationId, dataType, year, dayVersion);
        }
    }

    @Override
    public Integer getLatestVersion(Long aLong, Integer dataType) {
        return stationDayMapper.getLatestVersion(aLong, dataType);
    }

    @Override
    public List<StationDayDO> findStationDayDOByVersionAndYear(Long stationId, Integer stationType, String year, Integer dayVersion, Integer dataType) {
        Long count = stationDataLogMapper.selectCount(new LambdaQueryWrapperX<StationDataLogDO>()
                .gtIfPresent(StationDataLogDO::getCurrentVersion, dayVersion)
                .eq(StationDataLogDO::getStationId, stationId)
                .eq(StationDataLogDO::getDataType, dataType)
        );
        if(count==0){
            dayVersion=-1;
        }
        QueryWrapper<StationDayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_type", dataType);
        queryWrapper.eq("station_id", stationId);
        queryWrapper.eq("year", year);
        if(dayVersion==-1){
            queryWrapper.eq("latest", LatestEnum.LATEST.getType());
            queryWrapper.orderByAsc("month").orderByAsc("day");
            return stationDayMapper.selectList(queryWrapper);
        }else{
            return stationDayMapper.selectListDel(stationId, dataType, year, dayVersion);
        }
    }


    @Override
    public void insertList(List<StationDayDO> stationDayDOS) {
        stationDayMapper.insertList(stationDayDOS);
    }

    @Override
    public List<StationDayDO> selectListTemporaryData(Long stationId) {
        return stationDayMapper.selectListTemporaryData(stationId);
    }

    @Override
    public StationDayDO selectOne(Long stationId, Integer year, Integer month, Integer day, Integer dataType) {
        QueryWrapper<StationDayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("station_id", stationId);
        queryWrapper.eq("data_type", dataType);
        queryWrapper.eq("year", year);
        queryWrapper.eq("month", month);
        queryWrapper.eq("day", day);
        queryWrapper.eq("latest", 1);
        return stationDayMapper.selectOne(queryWrapper, false);
    }

    @Override
    public int insert(StationDayDO stationDayDO) {
        return stationDayMapper.insert(stationDayDO);
    }

    @Override
    public List<StationDayDO> listByStationIds(List<Long> stationIds) {
        return stationDayMapper.listByStationIds(stationIds);
    }

    @Override
    public void batchInsert(List<StationDayDO> list, int batchSize) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        int totalSize = list.size();
        for (int start = 0; start < totalSize; start += batchSize) {
            int end = Math.min(start + batchSize, totalSize);
            List<StationDayDO> batch = list.subList(start, end);
            stationDayMapper.batchInsert(batch);
        }
    }

    @Override
    public List<StationDayDO> listCurrent(Long stationId, Integer dataType) {
        LambdaQueryWrapper<StationDayDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StationDayDO::getStationId,stationId);
        queryWrapper.eq(StationDayDO::getDataType,dataType);
        queryWrapper.eq(StationDayDO::getLatest,LatestEnum.LATEST.getType());
        return stationDayMapper.selectList(queryWrapper);
    }

    @Override
    public void updateBatchById(List<StationDayDO> updateList, int batchSize) {
        int totalSize = updateList.size();
        for (int start = 0; start < totalSize; start += batchSize) {
            int end = Math.min(start + batchSize, totalSize);
            List<StationDayDO> batch = updateList.subList(start, end);
            stationDayMapper.updateBatchById(batch);
        }
    }

}