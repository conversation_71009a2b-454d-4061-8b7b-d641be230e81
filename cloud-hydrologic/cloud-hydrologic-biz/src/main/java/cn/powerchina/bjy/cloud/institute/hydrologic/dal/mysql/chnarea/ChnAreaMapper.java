package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.chnarea;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.area.vo.area.ChnAreaPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.chnarea.ChnAreaDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 区地域 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ChnAreaMapper extends BaseMapperX<ChnAreaDO> {

    default PageResult<ChnAreaDO> selectPage(ChnAreaPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ChnAreaDO>()
                .likeIfPresent(ChnAreaDO::getName, reqVO.getName())
                .eqIfPresent(ChnAreaDO::getCityCode, reqVO.getCityCode())
                .eqIfPresent(ChnAreaDO::getProvinceCode, reqVO.getProvinceCode())
                .betweenIfPresent(ChnAreaDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ChnAreaDO::getCode));
    }

}