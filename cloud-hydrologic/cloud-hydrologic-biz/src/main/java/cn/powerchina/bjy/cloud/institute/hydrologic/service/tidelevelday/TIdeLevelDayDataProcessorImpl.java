package cn.powerchina.bjy.cloud.institute.hydrologic.service.tidelevelday;

import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodday.vo.StationRainPeriodDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationrainperiodday.StationRainPeriodDayMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.tidelevelday.TideLevelDayMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.tidelevelmonth.TideLevelMonthMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.DataProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 数据处理
 * @time 2024/9/4 14:48
 */
@Service
public class TIdeLevelDayDataProcessorImpl implements DataProcessor<StationRainPeriodDayRespVO> {

    @Autowired
    private TideLevelDayMapper tideLevelDayMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(StationRainPeriodDayRespVO updateReqVO) {

    }

    @Override
    public void importExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException {

    }

    @Override
    public List<Integer> listYear(Long stationId, Integer dataType) {
        return tideLevelDayMapper.listYear(stationId,dataType);
    }
}
