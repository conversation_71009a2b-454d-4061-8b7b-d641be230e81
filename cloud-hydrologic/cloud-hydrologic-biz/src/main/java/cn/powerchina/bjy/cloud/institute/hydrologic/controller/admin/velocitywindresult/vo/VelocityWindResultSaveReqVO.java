package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.bedsedimentgradingresult.vo.BedSedimentGradingResultSaveReqVO;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDate;
import java.util.List;

@Schema(description = "管理后台 - 气象-历年最大风速及风向新增/修改 Request VO")
@Data
public class VelocityWindResultSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15581")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11040")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "站点类型不能为空")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<VelocityWindResultData> dataList;

    @Schema(description = "本次更新删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "是否删除 true是 ")
    private Boolean isDelete;

    @Schema(description = "年份")
    private Integer year;

    @Data
    public static class VelocityWindResultData {
        @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "20781")
        private Long id;

        @Schema(description = "气象站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27048")
        private Long stationId;

        @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "2")
        private Integer stationType;

        @Schema(description = "数据类型", example = "1")
        private Integer dataType;

        @Schema(description = "年")
        private Integer year;

        @Schema(description = "最大风速(m/s)")
        private String velocityMax;

        @Schema(description = "风向1")
        private String wind1;

        @Schema(description = "风向2")
        private String wind2;

        @Schema(description = "风向3")
        private String wind3;

        @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer version;

        @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer latest;

        @Schema(description = "记录时间")
        private LocalDate currentDay;
    }
}
