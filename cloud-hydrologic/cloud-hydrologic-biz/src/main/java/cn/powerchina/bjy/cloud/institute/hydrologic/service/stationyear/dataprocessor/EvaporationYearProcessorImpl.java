package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationyear.dataprocessor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo.DataLogVersionInfoVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationyear.vo.StationYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationyear.StationYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.LatestEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.EvaporateStationYearImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.EvaporateStationYearImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.SandContentYearImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationyear.StationYearService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.MyStringUtils;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@Slf4j
public class EvaporationYearProcessorImpl extends AbstractYearDataProcessor {
    @Autowired
    private StationYearService stationYearService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(StationYearSaveReqVO updateReqVO) {
        List<StationYearSaveReqVO.StationYearData> dataList = updateReqVO.getDataList();
        List<Long> deleteIds = updateReqVO.getDeleteIds();
        //没有要更新或删除的内容直接返回
        if (CollectionUtil.isEmpty(dataList) && CollectionUtil.isEmpty(deleteIds)) {
            log.warn("没有要更新或删除的内容 stationId=[{}] ,dataType=[{}]", updateReqVO.getStationId(), updateReqVO.getDataType());
            return;
        }
        //剔除掉要删除的数据只保留要更新的元素，从更新数据list中： 处理一种特殊情况（前端操作了更新之后又删除）
        if (CollectionUtil.isNotEmpty(dataList) && CollectionUtil.isNotEmpty(deleteIds)) {
            dataList = dataList.stream().filter(item -> !deleteIds.contains(item.getId())).toList();
        }
        //本次更新的内容
        List<EvaporateStationYearImportModel> importDataList = new ArrayList<>();

        List<Long> updateIdList = new ArrayList<>();

        //判断本次提交的数据是否跟库中已有的数据有重复
        if (CollectionUtil.isNotEmpty(dataList)) {
            for (StationYearSaveReqVO.StationYearData stationYearData : dataList) {

                Long id = stationYearData.getId();
                if (Objects.isNull(id)) {
                    continue;
                }
                updateIdList.add(id);

                //更新场景重复判断
                StationYearDO stationYearDO = stationYearService.selectOne(
                        updateReqVO.getStationId(),
                        updateReqVO.getDataType(),
                        stationYearData.getYear());
                if (Objects.nonNull(stationYearDO) && !stationYearDO.getId().equals(id)) {
                    throw ServiceExceptionUtil.exception(ErrorCodeConstants.DATA_FORMAT_ERROR);
                }
            }

            importDataList = dataList.stream()
                    .map(item -> EvaporateStationYearImportModel.builder()
                            .year(item.getYear())
                            .averageValue(item.getAverageValue())
                            .maxValue(item.getMaxValue())
                            .minValue(item.getMinValue())
                            .maxValueDate(item.getMaxValueDate())
                            .minValueDate(item.getMinValueDate())
                            .firstIceDate(item.getFirstIceDate())
                            .endIceDate(item.getEndIceDate())
                            .remark(item.getRemark())
                            .build())
                    .toList();
        }
        yearDataToDbBuilder(updateReqVO, importDataList, updateIdList, false);
    }

    @Override
    public void importExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException {
        //获取excel内容
        List<EvaporateStationYearImportModel> importDataList = EasyExcel.read(file.getInputStream(), EvaporateStationYearImportModel.class, null).sheet().headRowNumber(1).doReadSync();
        if (CollectionUtils.isEmpty(importDataList)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
        }
        StationYearSaveReqVO stationYearSaveReqVO = new StationYearSaveReqVO();
        stationYearSaveReqVO.setStationId(stationId);
        stationYearSaveReqVO.setStationType(stationType);
        stationYearSaveReqVO.setDataType(dataType);
        yearDataToDbBuilder(stationYearSaveReqVO, importDataList, null, true);
    }


    /**
     * 站点-年序列数据 入库
     *
     * @param importDataList
     */
    private void yearDataToDbBuilder(StationYearSaveReqVO updateReqVO, List<EvaporateStationYearImportModel> importDataList, List<Long> updateIdList, boolean importFlag) {
        Long stationId = updateReqVO.getStationId();
        Integer stationType = updateReqVO.getStationType();
        Integer dataType = updateReqVO.getDataType();
        //获取该统计表-年鉴格式相关的所有统计表的下一个版本
        DataLogVersionInfoVO dataLogVersionInfoVO = fetchVersions(stationId, stationType, dataType);
        //获取相关统计表的下个版本信息
        List<StationYearDO> stationYearList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(importDataList)) {
            //数据校验
            dataCheck(importDataList, importFlag);
            //组装数据：本次更新或者导入的内容

            stationYearList = dataBuilder(stationId, stationType, dataType, importDataList, dataLogVersionInfoVO.getYearVersion()>1?dataLogVersionInfoVO.getYearVersion()-1:1);

        }
        List<Long> idList = new ArrayList<>();

        CollUtil.addAll(idList, updateIdList);
        CollUtil.addAll(idList, updateReqVO.getDeleteIds());

        //写入数据
        dataUpdate(stationId,
                stationType,
                dataType,
                dataLogVersionInfoVO,
                stationYearList,
                idList,
                importFlag);
    }


    private List<StationYearDO> dataBuilder(Long stationId, Integer stationType, Integer dataType, List<EvaporateStationYearImportModel> importDataList, Integer nextVersion) {
        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();
        return importDataList.stream()
                //过滤不否和要求的日期数据
                .filter(item -> Objects.nonNull(item.getYear()) && item.getYear().toString().length() == 4)
                .map(item -> {
                    StationYearDO yearDO = new StationYearDO();
                    yearDO.setStationId(stationId);
                    yearDO.setStationType(stationType);
                    yearDO.setDataType(dataType);
                    yearDO.setVersion(nextVersion);
                    yearDO.setYear(item.getYear());
                    yearDO.setAverageValue(item.getAverageValue());
                    yearDO.setMaxValue(item.getMaxValue());
                    yearDO.setMinValueDate(item.getMinValueDate());
                    yearDO.setMinValue(item.getMinValue());
                    yearDO.setMaxValueDate(item.getMaxValueDate());
                    yearDO.setFirstIceDate(item.getFirstIceDate());
                    yearDO.setEndIceDate(item.getEndIceDate());
                    yearDO.setRemark(item.getRemark());
                    yearDO.setLatest(LatestEnum.LATEST.getType());
                    yearDO.setCreator(loginUserId);
                    yearDO.setUpdater(loginUserId);
                    return yearDO;
                }).toList();
    }


    private void dataCheck(List<EvaporateStationYearImportModel> importDataList, boolean importFlag) {
        //数据唯一性判断
        List<String> repeatStr = new ArrayList<>();
        if (CollectionUtil.isEmpty(importDataList)) {
            return;
        }
        for (int i = 0; i < importDataList.size(); i++) {
            EvaporateStationYearImportModel importModel = importDataList.get(i);

            //校验年月日
            Integer year = importModel.getYear();


            cn.powerchina.bjy.cloud.institute.hydrologic.util.StringUtils.checkYear(year, i + 2, importFlag);
            if (repeatStr.contains(year.toString())) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_EXCERPT_REPEAT_EXISTS_ERROR, i + 2);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_YEAR_REPEAT_EXISTS_ERROR, year);
                }
            }
            repeatStr.add(String.valueOf(year));
        }
    }

}

