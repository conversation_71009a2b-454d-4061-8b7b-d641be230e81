package cn.powerchina.bjy.cloud.institute.hydrologic.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 气象站数据 气象资料统计导入excel model
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class WeatherDataImportModel {

    @ExcelProperty(index = 1)
    private String startYear;
    /**
     * 值1
     */
    @ExcelProperty(index = 3)
    private String value1;

    @ExcelProperty(index = 4)
    private String value2;

    @ExcelProperty(index = 5)
    private String value3;

    @ExcelProperty(index = 6)
    private String value4;

    @ExcelProperty(index = 7)
    private String value5;

    @ExcelProperty(index = 8)
    private String value6;

    @ExcelProperty(index = 9)
    private String value7;

    @ExcelProperty(index = 10)
    private String value8;

    @ExcelProperty(index = 11)
    private String value9;

    @ExcelProperty(index = 12)
    private String value10;

    @ExcelProperty(index = 13)
    private String value11;

    @ExcelProperty(index = 14)
    private String value12;

    @ExcelProperty(index = 15)
    private String value13;
}
