package cn.powerchina.bjy.cloud.institute.hydrologic.service.filedocument;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.filedocument.vo.FileDocumentPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.filedocument.vo.FileDocumentRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.filedocument.vo.FileDocumentSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filedocument.FileDocumentDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 文件资料 Service 接口
 *
 * <AUTHOR>
 */
public interface FileDocumentService {

    /**
     * 创建文件资料
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFileDocument(@Valid FileDocumentSaveReqVO createReqVO);

    /**
     * 更新文件资料
     *
     * @param updateReqVO 更新信息
     */
    void updateFileDocument(@Valid FileDocumentSaveReqVO updateReqVO);

    /**
     * 删除文件资料
     *
     * @param id 编号
     */
    void deleteFileDocument(Long id);

    /**
     * 获得文件资料
     *
     * @param id 编号
     * @return 文件资料
     */
    FileDocumentDO getFileDocument(Long id);

    /**
     * 获得文件资料分页
     *
     * @param pageReqVO 分页查询
     * @return 文件资料分页
     */
    PageResult<FileDocumentDO> getFileDocumentPage(FileDocumentPageReqVO pageReqVO);

    /**
     * 根据树节点查找
     *
     * @param treeId
     * @return
     */
    List<FileDocumentDO> findFileDocumentDOByTreeId(Long treeId);

    /**
     * 获得文件资料分页
     *
     * @param pageReqVO
     * @return
     */
    PageResult<FileDocumentRespVO> getFileDocumentPageResult(FileDocumentPageReqVO pageReqVO);

    List<FileDocumentDO> listFileDocumentByTreeIds(List<Long> treeIds);

    void insertList(List<FileDocumentDO> fileDocumentDOS);

    void updateTreeId(Long oldFileTreeId, Long newFileTreeId);

    List<FileDocumentDO> selectListTemporaryData(Long treeId);

    FileDocumentDO selectOne(Long treeId, String fileName);

    void updateFileDocument(FileDocumentDO fileDocumentDO);

    void insert(FileDocumentDO fileDocumentDO);
}