package cn.powerchina.bjy.cloud.institute.hydrologic.annotation;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo.StationIdReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.operationlog.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/7/10
 */
@Component
@Aspect
@Slf4j
public class HydrologicOperationAspect {
    /**
     * 是否索引的KEY
     */
    private final String INDEXED = "indexed";


    @Autowired
    private OperationLogService operationLogService;

    // 2. PointCut表示这是一个切点，@annotation表示这个切点切到一个注解上，后面带该注解的全类名
    // 切面最主要的就是切点，所有的故事都围绕切点发生
    // logPointCut()代表切点名称
    @Pointcut("@annotation( cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation)")
    public void operationPointCut() {
    }

    ;

    // 3. 环绕通知
    @Around("operationPointCut()")
    public Object operationAround(ProceedingJoinPoint joinPoint) throws NoSuchMethodException {
        //获取类的字节码对象，通过字节码对象获取方法信息
        Class<?> targetCls = joinPoint.getTarget().getClass();
        //获取方法签名(通过此签名获取目标方法信息)
        MethodSignature ms = (MethodSignature) joinPoint.getSignature();
        //获取目标方法上的注解指定的操作名称
        Method targetMethod =
                targetCls.getDeclaredMethod(
                        ms.getName(),
                        ms.getParameterTypes());
        HydrologicOperation operationAnnotation = targetMethod.getAnnotation(HydrologicOperation.class);
        JSONObject paramJsonObject = JSONUtil.parseObj(joinPoint.getArgs()[0]);
        if (!Objects.equals(paramJsonObject.getInt(INDEXED), 1)) {
            try {
                return joinPoint.proceed();
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
        Long stationId = null;
        if (joinPoint.getArgs()[0] instanceof Long || joinPoint.getArgs()[0] instanceof Integer) {
            Object param = joinPoint.getArgs()[0];
            stationId = Objects.isNull(param) ? null : Long.parseLong(param + "");
        } else {
            try {
                StationIdReqVO pageReqVO = JsonUtils.parseObject(JsonUtils.toJsonString(joinPoint.getArgs()[0]), StationIdReqVO.class);
                stationId = Objects.nonNull(pageReqVO) ? pageReqVO.getStationId() : null;
            } catch (Exception e) {

            }
        }
        if (Objects.isNull(stationId)) {
            throw new RuntimeException("站点id不能为空");
        }

        stationId = paramJsonObject.getLong("stationId");
        Integer stationType = paramJsonObject.getInt("stationType");
        Integer dataType = paramJsonObject.getInt("dataType");
        // 继续执行方法
        operationLogService.addOperationLog(stationId, stationType, dataType, operationAnnotation.operationType().getType(), JsonUtils.toJsonString(joinPoint.getArgs()));
        try {
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return null;
    }
}
