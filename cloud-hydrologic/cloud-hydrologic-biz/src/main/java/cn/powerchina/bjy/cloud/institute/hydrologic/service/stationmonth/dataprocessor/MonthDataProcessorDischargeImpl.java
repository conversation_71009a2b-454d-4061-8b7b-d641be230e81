package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationmonth.dataprocessor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo.DataLogVersionInfoVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationmonth.vo.StationMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth.StationMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationmonth.StationMonthMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.LatestEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.DischargeMonthImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationmonth.StationMonthService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.MyStringUtils;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @desc 月维度数据处理器-输沙量
 * @time 2024/8/23 14:31
 */
@Service
@Slf4j
public class MonthDataProcessorDischargeImpl extends AbstractMonthDataProcessor {

    @Autowired
    private StationMonthMapper stationMonthMapper;
    @Autowired
    private StationMonthService stationMonthService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(StationMonthSaveReqVO updateReqVO) {
       List<StationMonthSaveReqVO.StationMonthData> dataList = updateReqVO.getDataList();
        List<Long> deleteIds = updateReqVO.getDeleteIds();
        //没有要更新或删除的内容直接返回
        if (CollectionUtil.isEmpty(dataList) && CollectionUtil.isEmpty(deleteIds)) {
            log.warn("没有要更新或删除的内容 stationId=[{}] ,dataType=[{}]", updateReqVO.getStationId(), updateReqVO.getDataType());
            return;
        }

        //剔除掉要删除的数据只保留要更新的元素，从更新数据list中： 处理一种特殊情况（前端操作了更新之后又删除）
        if (CollectionUtil.isNotEmpty(dataList) && CollectionUtil.isNotEmpty(deleteIds)) {
            dataList = dataList.stream().filter(item -> !deleteIds.contains(item.getId())).toList();
        }


        //本次更新的内容
        List<DischargeMonthImportModel> importDataList = new ArrayList<>();

        List<Long> updateIdList = new ArrayList<>();

        //判断本次提交的数据是否跟库中已有的数据有重复
        if (CollectionUtil.isNotEmpty(dataList)) {
            for (StationMonthSaveReqVO.StationMonthData stationMonthData : dataList) {

                Long id = stationMonthData.getId();
                if (Objects.isNull(id)) {
                    continue;
                }
                updateIdList.add(id);
                //更新场景重复判断
                try{
                    int year = Integer.parseInt(stationMonthData.getYear());
                    try{
                        int month = Integer.parseInt(stationMonthData.getMonth());
                        StationMonthDO stationMonthDO = stationMonthService.selectOne(
                                stationMonthData.getStationId(),
                                stationMonthData.getDataType(),
                                year,
                                month
                        );
                        if (Objects.nonNull(stationMonthDO) && !stationMonthDO.getId().equals(id)) {
                            throw ServiceExceptionUtil.exception(ErrorCodeConstants.DATA_FORMAT_ERROR);
                        }
                    }catch (NumberFormatException e) {
                        throw exception(ErrorCodeConstants.EXCEL_UPDATE_RAINFALL_MONTH_FORMAT_ERROR);
                    }
                } catch (NumberFormatException e) {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_RAINFALL_YEAR_FORMAT_ERROR);
                }
            }

            importDataList = dataList.stream()
                    .map(item -> DischargeMonthImportModel.builder()
                            .year(item.getYear())
                            .month(item.getMonth())
                            .averageValue(item.getAverageValue())
                            .averageValueRemark(item.getAverageValueRemark())
                            .maxValue(item.getMaxValue())
                            .maxValueDate(item.getMaxValueDate())
                            .maxValueRemark(item.getMaxValueRemark())
                            .build())
                    .toList();
        }


        monthDataToDbBuilder(updateReqVO, importDataList, updateIdList, false);

    }


    @Override
    public void importExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException {
        //获取excel内容
        List<DischargeMonthImportModel> importDataList = EasyExcel.read(file.getInputStream(),DischargeMonthImportModel.class, null).sheet().headRowNumber(1).doReadSync();
        if (CollectionUtils.isEmpty(importDataList)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
        }
        StationMonthSaveReqVO stationMonthSaveReqVO = new StationMonthSaveReqVO();
        stationMonthSaveReqVO.setStationId(stationId);
        stationMonthSaveReqVO.setStationType(stationType);
        stationMonthSaveReqVO.setDataType(dataType);
        monthDataToDbBuilder(stationMonthSaveReqVO, importDataList, null, true);
    }
    @Override
    public List<Integer> listYear(Long stationId, Integer dataType) {
        return stationMonthMapper.listYear(stationId,dataType);
    }
    /**
     * 站点-月序列数据 入库
     *
     * @param importDataList
     */
    private void monthDataToDbBuilder(StationMonthSaveReqVO updateReqVO, List<DischargeMonthImportModel> importDataList, List<Long> updateIdList, boolean importFlag) {
        Long stationId = updateReqVO.getStationId();
        Integer stationType = updateReqVO.getStationType();
        Integer dataType = updateReqVO.getDataType();

        //获取该统计表-年鉴格式相关的所有统计表的下一个版本
        DataLogVersionInfoVO dataLogVersionInfoVO = fetchVersions(stationId, stationType, dataType);
        //获取相关统计表的下个版本信息
        List<StationMonthDO> stationMonthList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(importDataList)) {
            //数据校验
            dataCheck(importDataList, importFlag);
            //组装数据：本次更新或者导入的内容

            stationMonthList = dataBuilder(stationId, stationType, dataType, importDataList, dataLogVersionInfoVO.getMonthVersion()-1);

        }

        List<Long> idList = new ArrayList<>();

        CollUtil.addAll(idList, updateIdList);
        CollUtil.addAll(idList, updateReqVO.getDeleteIds());

        //写入数据
        dataUpdate(stationId,
                stationType,
                dataType,
                dataLogVersionInfoVO,
                stationMonthList,
                idList,
                importFlag);
    }


    private void dataCheck(List<DischargeMonthImportModel> importDataList, boolean importFlag) {
        //数据唯一性判断
        List<String> repeatStr = new ArrayList<>();
        if (CollectionUtil.isEmpty(importDataList)) {
            return;
        }
        for (int i = 0; i < importDataList.size(); i++) {
            DischargeMonthImportModel importModel = importDataList.get(i);
            // 校验年份
            try {
                Integer year = Integer.valueOf(importModel.getYear());
                // 校验月份
                try {
                    Integer month = Integer.valueOf(importModel.getMonth());

                    // 调用 MyStringUtils.checkYearMonth 进行进一步校验
                    MyStringUtils.checkYearMonth(importModel.getYear(), importModel.getMonth(), i + 3, importFlag);

                    // 检查重复
                    String key = year + "-" + month;
                    if (repeatStr.contains(key)) {
                        if (importFlag) {
                            throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_EXCERPT_REPEAT_EXISTS_ERROR, i + 3);
                        } else {
                            throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_MONTH_REPEAT_EXISTS_ERROR, year, month);
                        }
                    }
                    repeatStr.add(year + "-" + month);
                } catch (NumberFormatException e) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_MONTH_FORMAT_ERROR, i + 3);
                }
            } catch (NumberFormatException e) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_YEAR_FORMAT_ERROR, i + 3);
            }

        }
    }

    private List<StationMonthDO> dataBuilder(Long stationId, Integer stationType, Integer dataType, List<DischargeMonthImportModel> importDataList, Integer monthVersion) {
        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();

        return importDataList.stream()
                //过滤不符合要求的日期数据
                .filter(item -> DateUtils.isValidDateWithMonth(item.getYear() + "-" + item.getMonth()))
                .map(item -> {
                    StationMonthDO monthDO = new StationMonthDO();
                    monthDO.setStationId(stationId);
                    monthDO.setStationType(stationType);
                    monthDO.setDataType(dataType);
                    monthDO.setVersion(monthVersion);
                    monthDO.setYear(Integer.parseInt(item.getYear()));
                    monthDO.setMonth(Integer.parseInt(item.getMonth()));
                    monthDO.setCurrentDay(DateUtils.getFirstDayOfMonth(item.getYear() ,item.getMonth()));
                    monthDO.setAverageValue(item.getAverageValue());
                    monthDO.setAverageValueRemark(item.getAverageValueRemark());
                    monthDO.setMaxValue(item.getMaxValue());
                    monthDO.setMaxValueRemark(item.getMaxValueRemark());
                    monthDO.setMaxValueDate(item.getMaxValueDate());
                    monthDO.setLatest(LatestEnum.LATEST.getType());
                    monthDO.setCreator(loginUserId);
                    monthDO.setUpdater(loginUserId);
                    monthDO.setLatest(LatestEnum.LATEST.getType());
                    return monthDO;
                }).toList();
    }
}


