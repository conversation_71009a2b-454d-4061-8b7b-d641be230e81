package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationday.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 站点-日统计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StationDayPageReqVO extends PageParam {

    @Schema(description = "记录id")
    private Long logId;

    @Schema(description = "雨量站id", example = "28155")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "1")
    private Integer stationType;

    @Schema(description = "数据类型", example = "2")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "月")
    private Integer month;

    @Schema(description = "日")
    private Integer day;

    @Schema(description = "记录日期")
    private LocalDate[] currentDay;

    @Schema(description = "降水量(mm)")
    private String value;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "逐日气温表-平均气温")
    private String averageValue;

    @Schema(description = "逐日气温表-最高气温")
    private String maxValue;

    @Schema(description = "逐日气温表-最低气温")
    private String minValue;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /** 是否检索：0：否 、 1：是 */
    private Integer indexed;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}