package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.weatherday;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherday.vo.WeatherDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.weatherday.WeatherDayDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 气象站-逐日气温 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WeatherDayMapper extends BaseMapperX<WeatherDayDO> {

    default PageResult<WeatherDayDO> selectPage(WeatherDayPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WeatherDayDO>()
                .eqIfPresent(WeatherDayDO::getStationId, reqVO.getStationId())
                .eqIfPresent(WeatherDayDO::getVersion, reqVO.getVersion())
                .eqIfPresent(WeatherDayDO::getLatest, reqVO.getLatest())
                .betweenIfPresent(WeatherDayDO::getCurrentDay, reqVO.getCreateTime())
                .orderByDesc(WeatherDayDO::getYear)
                .orderByAsc(WeatherDayDO::getMonth)
                .orderByAsc(WeatherDayDO::getDay));
    }

    @Select("SELECT id FROM hydrologic_weather_day WHERE station_id = #{stationId} AND deleted = 0")
    List<Long> selectByStationId(@Param("stationId") Long stationId);

    /**
     * 查找最大版本
     *
     * @param stationId
     * @return
     */
    @Select("select max(version) from hydrologic_weather_day where deleted=0 and station_id = #{stationId}")
    Integer selectMaxVersion(@Param("stationId") Long stationId);

    /**
     * 更新历史版本
     *
     * @param stationId
     * @param latest
     */
    @Update("update hydrologic_weather_day set latest = #{latest} where station_id = #{stationId} and latest!=#{latest}")
    void updateHistoryVersion(@Param("stationId") Long stationId, @Param("latest") Integer latest);
}