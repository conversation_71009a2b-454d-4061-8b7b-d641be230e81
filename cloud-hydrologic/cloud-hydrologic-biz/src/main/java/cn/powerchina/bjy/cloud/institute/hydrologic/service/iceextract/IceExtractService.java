package cn.powerchina.bjy.cloud.institute.hydrologic.service.iceextract;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.iceextract.vo.IceExtractPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.iceextract.vo.IceExtractRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.iceextract.vo.IceExtractSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.iceextract.IceExtractDO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 冰情摘录 Service 接口
 *
 * <AUTHOR>
 */
public interface IceExtractService {

    /**
     * 创建冰情摘录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createIceExtract(@Valid IceExtractSaveReqVO createReqVO);

    /**
     * 更新冰情摘录
     *
     * @param updateReqVO 更新信息
     */
    void updateIceExtract(@Valid IceExtractSaveReqVO updateReqVO);

    /**
     * 删除冰情摘录
     *
     * @param id 编号
     */
    void deleteIceExtract(Long id);

    /**
     * 获得冰情摘录
     *
     * @param id 编号
     * @return 冰情摘录
     */
    IceExtractDO getIceExtract(Long id);

    /**
     * 获得冰情摘录分页
     *
     * @param pageReqVO 分页查询
     * @return 冰情摘录分页
     */
    PageResult<IceExtractRespVO> getIceExtractPage(IceExtractPageReqVO pageReqVO);

    /**
     * 导入Excel
     *
     * @param file 文件
     * @param stationId 站点id
     */
    void importData(MultipartFile file, Long stationId) throws IOException;

    /**
     * 导出数据
     *
     * @param response
     * @param pageReqVO
     */
    void exportExcel(HttpServletResponse response, IceExtractPageReqVO pageReqVO);

}