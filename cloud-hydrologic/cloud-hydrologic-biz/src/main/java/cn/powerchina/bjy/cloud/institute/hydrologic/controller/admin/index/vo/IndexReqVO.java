package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class IndexReqVO {
    @Schema(description = "检索类型")
    private String searchType;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "地市")
    private String city;

    @Schema(description = "站点类型")
    private String stationType;

    @Schema(description = "检测项目-系列长度集合")
    private List<monLength> monLengthList;

    @Schema(description = "工作深度")
    private String workDepth;

    @Schema(description = "是否纳规")
    private String isNaGui;

    @Schema(description = "装机容量M")
    private String installedCapacityM;

    @Schema(description = "装机容量W")
    private String installedCapacityW;

    @Schema(description = "连续满发小时数")
    private String continuousHours;

    @Schema(description = "站点集合")
    private List<String> stationIdList;

    @Schema(description = "页面类型，1：数据录入，2：数据搜索")
    private String pageType;

    @Schema(description = "监测项目 - 系列长度")
    @Data
    public static class monLength {

        @Schema(description = "监测项目")
        private String monitoringItems;

        @Schema(description = "系列长度")
        private String seriesLength;

    }

}
