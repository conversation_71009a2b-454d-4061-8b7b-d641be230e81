package cn.powerchina.bjy.cloud.institute.hydrologic.service.velocitywindresult;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedsedimentgradingresult.vo.SuspendedSedimentGradingResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo.VelocityWindResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo.VelocityWindResultRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo.VelocityWindResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.suspendedsedimentgradingresult.SuspendedSedimentGradingResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.velocitywindresult.VelocityWindResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationdatalog.StationDataLogMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.velocitywindresult.VelocityWindResultMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.LatestEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.PlanningDesignConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnumV2;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.StationDataLogAddModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.VelocityWindResultImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.RedisService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationdatalog.StationDataLogService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticalinfo.StatisticalInfoService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.RedisUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 气象-历年最大风速及风向 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class VelocityWindResultServiceImpl implements VelocityWindResultService {
    @Resource
    private VelocityWindResultMapper velocityWindResultMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private StationDataLogService stationDataLogService;
    @Resource
    private StatisticalInfoService statisticalInfoService;
    @Resource
    private StationDataLogMapper stationDataLogMapper;
    @Override
    public Long createVelocityWindResult(VelocityWindResultSaveReqVO createReqVO) {
        // 插入
        VelocityWindResultDO velocityWindResult = BeanUtils.toBean(createReqVO, VelocityWindResultDO.class);
        velocityWindResultMapper.insert(velocityWindResult);
        // 返回
        return velocityWindResult.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVelocityWindResult(VelocityWindResultSaveReqVO updateReqVO) {
        Long stationId = updateReqVO.getStationId();
        Integer stationType = updateReqVO.getStationType();
        Integer dataType = updateReqVO.getDataType();
        Integer year = updateReqVO.getYear();
        // 校验存在
        if (Objects.isNull(updateReqVO.getStationId())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        RLock lock = redisService.acquireDistributedLock(RedisUtils.getLockKey(updateReqVO.getStationId(), updateReqVO.getStationType(), updateReqVO.getDataType()));
        try {
            StationDataTypeEnumV2 thisEnum = EnumUtil.getBy(StationDataTypeEnumV2::getCode, updateReqVO.getDataType());
            if (thisEnum == null) {
                throw exception(ErrorCodeConstants.DATA_TYPE_NOT_EXISTS);
            }
            //数据处理器
            try {
                List<VelocityWindResultSaveReqVO.VelocityWindResultData> dataList = updateReqVO.getDataList();
                if(CollectionUtil.isNotEmpty(dataList)||updateReqVO.getDeleteIds().size()>0){
                    if(CollectionUtil.isNotEmpty(dataList)) {
                        for (int i = 0; i < dataList.size(); i++) {
                            VelocityWindResultSaveReqVO.VelocityWindResultData importData = dataList.get(i);
                            // 年份非空校验
                            if (importData.getYear() == null) {
                                throw exception(ErrorCodeConstants.VELOCITY_WIND_ERROR, "年份不能为空");
                            }
                            if (!String.valueOf(importData.getYear()).trim().matches("^\\d{4}$")) {
                                throw exception(ErrorCodeConstants.VELOCITY_WIND_UPDATE_ERROR, "年份必须为4位数字", importData.getYear());
                            }
                            VelocityWindResultDO windResultDO = velocityWindResultMapper.selectById(importData.getId());
                            if (windResultDO == null) {
                                throw exception(ErrorCodeConstants.VELOCITY_WIND_ERROR, "id不存在");
                            }
                            LambdaQueryWrapper<VelocityWindResultDO> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(VelocityWindResultDO::getStationId, stationId);
                            queryWrapper.eq(VelocityWindResultDO::getDataType, dataType);
                            queryWrapper.eq(VelocityWindResultDO::getLatest, 1);
                            queryWrapper.eq(VelocityWindResultDO::getYear, importData.getYear());// 1为最新
                            VelocityWindResultDO windResultDO1 = velocityWindResultMapper.selectOne(queryWrapper);
                            if (windResultDO1 != null) {
                                if (! windResultDO1.getId().equals(windResultDO.getId())) {
                                    throw exception(ErrorCodeConstants.VELOCITY_WIND_UPDATE_ERROR, "修改年份已存在", importData.getYear());
                                }
                            }
                            if (importData.getVelocityMax() == null || importData.getVelocityMax().isBlank()) {
                                throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 2, "最大风速不能为空");
                            }
                        }
                    }
                    updateDataToDb(stationId, stationType, dataType, dataList, updateReqVO.getIsDelete(),year,updateReqVO.getDeleteIds());
                }
            }catch (ServiceException e){
                throw e;
            } catch (Throwable e) {
                log.info("未知异常：",e);
                throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
            }
        } finally {
            lock.unlock();
        }
    }
    private void updateDataToDb(Long stationId, Integer stationType, Integer dataType, List<VelocityWindResultSaveReqVO.VelocityWindResultData> dataList, Boolean b, Integer year, List<Long> deleteIds) {
        //历史数据
        List<VelocityWindResultDO>  hisdos = velocityWindResultMapper.selectList(new LambdaQueryWrapperX<VelocityWindResultDO>()
                .eq(VelocityWindResultDO::getStationId, stationId)
                .eq(VelocityWindResultDO::getDataType, dataType)
                .eq(VelocityWindResultDO::getStationType, stationType)
//                .eq(GrainUnitResultDO::getYear, year)
                .eq(VelocityWindResultDO::getLatest, 1));
        if (hisdos.isEmpty()) {
            return;
        }
        // 更新当前数据为历史版本
        Integer version = hisdos.get(0).getVersion();
        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();
        if(deleteIds != null) {
            velocityWindResultMapper.updateHistoryVersion(stationId, stationType,dataType);
            velocityWindResultMapper.delete(new LambdaQueryWrapperX<VelocityWindResultDO>()
                    .eq(VelocityWindResultDO::getLatest, LatestEnum.HISTORY.getType())
                    .eq(VelocityWindResultDO::getStationId, stationId)
                    .eq(VelocityWindResultDO::getDataType, dataType)
                    .eq(VelocityWindResultDO::getStationType, stationType)
                    .eq(VelocityWindResultDO::getVersion, version));
//                    .eq(GrainUnitResultDO::getYear, year));
            // 挑选出本次没有未修改的数据，作为最新版本数据
            List<VelocityWindResultDO> unmodifiedList = hisdos.stream()
                    .filter(item -> (!deleteIds.contains(item.getId())))
                    .peek(item -> {
                        item.setLatest(LatestEnum.LATEST.getType());
                        item.setId(null);
                        item.setCreator(loginUserId);
                        item.setUpdater(loginUserId);
                    }).toList();
            // 插入的数据分为：本次修改的、本次未修改的
            writeProcess(stationId, dataType, unmodifiedList, stationType);
        }else{
            List<VelocityWindResultDO> velocityWindResultDOS=new ArrayList<>();
            for(VelocityWindResultSaveReqVO.VelocityWindResultData data:dataList){
                VelocityWindResultDO resultDO = new VelocityWindResultDO();
                resultDO.setStationId(stationId);
                resultDO.setDataType(dataType);
                resultDO.setStationType(stationType);
                resultDO.setYear(data.getYear());
                resultDO.setLatest(LatestEnum.LATEST.getType());
                resultDO.setVelocityMax(data.getVelocityMax());
                resultDO.setWind1(data.getWind1());
                resultDO.setWind2(data.getWind2());
                resultDO.setWind3(data.getWind3());
                resultDO.setVersion(version+1);
                velocityWindResultDOS.add(resultDO);
            }
            // 获取dataList中的所有ID
            Set<Long> dataListIds = dataList.stream()
                    .map(VelocityWindResultSaveReqVO.VelocityWindResultData::getId)
                    .collect(Collectors.toSet());
            // 挑选出本次没有未修改的数据，作为最新版本数据
            List<VelocityWindResultDO> unmodifiedList = hisdos.stream()
                    .filter(item -> (!dataListIds.contains(item.getId())))
                    .peek(item -> {
                        item.setLatest(LatestEnum.LATEST.getType());
                        item.setId(null);
                        item.setCreator(loginUserId);
                        item.setUpdater(loginUserId);
                    }).toList();
            velocityWindResultDOS.addAll(unmodifiedList);
            velocityWindResultMapper.updateHistoryVersion(stationId, stationType,dataType);
            velocityWindResultMapper.delete(new LambdaQueryWrapperX<VelocityWindResultDO>()
                    .eq(VelocityWindResultDO::getLatest, LatestEnum.HISTORY.getType())
                    .eq(VelocityWindResultDO::getStationId, stationId)
                    .eq(VelocityWindResultDO::getDataType, dataType)
                    .eq(VelocityWindResultDO::getStationType, stationType)
                    .eq(VelocityWindResultDO::getVersion, version));
            if(velocityWindResultDOS!=null&&velocityWindResultDOS.size() >0){
                writeProcess(stationId, dataType, velocityWindResultDOS, stationType);
            }
        }
    }
    @Override
    public void deleteVelocityWindResult(Long id) {
        // 校验存在
        validateVelocityWindResultExists(id);
        // 删除
        velocityWindResultMapper.deleteById(id);
    }

    private void validateVelocityWindResultExists(Long id) {
        if (velocityWindResultMapper.selectById(id) == null) {
//            throw exception(VELOCITY_WIND_RESULT_NOT_EXISTS);
        }
    }

    @Override
    public VelocityWindResultDO getVelocityWindResult(Long id) {
        return velocityWindResultMapper.selectById(id);
    }

    @Override
    public PageResult<VelocityWindResultDO> getVelocityWindResultPage(VelocityWindResultPageReqVO pageReqVO) {
        //如果查询更新记录logId不为空
        Long logId = pageReqVO.getLogId();
        Integer dataType = pageReqVO.getDataType();
        Long stationId = pageReqVO.getStationId();
        Integer stationType = pageReqVO.getStationType();
        Long count = stationDataLogMapper.selectCount(new LambdaQueryWrapperX<StationDataLogDO>()
                .gtIfPresent(StationDataLogDO::getId, logId)
                .eq(StationDataLogDO::getStationId, stationId)
                .eq(StationDataLogDO::getDataType, dataType)
                .eq(StationDataLogDO::getStationType, stationType)
        );
        if(count==0){
            logId=null;
        }
        // 查询最新的
        if (Objects.nonNull(logId)) {
            StationDataLogDO stationDataLogDO = stationDataLogService.findById(pageReqVO.getLogId());
            if (Objects.isNull(stationDataLogDO)) {
                return PageResult.empty();
            }
            pageReqVO.setVersion(stationDataLogDO.getCurrentVersion());
            pageReqVO.setLatest(null);
            //历史数据
            Integer version = stationDataLogDO.getCurrentVersion();
            List<VelocityWindResultDO>  hisdos = velocityWindResultMapper.selectHis(stationId,dataType,stationType,version);
            return new PageResult<>(hisdos, (long) hisdos.size());
        } else {
            pageReqVO.setLatest(LatestEnum.LATEST.getType());
            return velocityWindResultMapper.selectPage(pageReqVO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException {
        RLock lock = redisService.acquireDistributedLock(RedisUtils.getLockKey(stationId, stationType, dataType));
        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }

            importeVlocityExcel(file, stationId, stationType, dataType);

        } catch (IOException e) {
            log.error("importStationRainYearFeatureExcel--->error.", e);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public boolean checkImportExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) {
        RLock lock = redisService.acquireDistributedLock(RedisUtils.getLockKey(stationId, stationType, dataType));
        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }

            // 获取excel内容
            List<VelocityWindResultImportModel> importDataList = EasyExcel.read(file.getInputStream(),
                            VelocityWindResultImportModel.class, null)
                    .sheet()
                    .headRowNumber(1)
                    .doReadSync();
            if (CollectionUtils.isEmpty(importDataList)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
            }
            for (VelocityWindResultImportModel importData : importDataList) {
                VelocityWindResultDO windResultDO = velocityWindResultMapper.selectOne(new LambdaQueryWrapper<VelocityWindResultDO>()
                        .eq(VelocityWindResultDO::getStationId, stationId)
                        .eq(VelocityWindResultDO::getStationType, stationType)
                        .eq(VelocityWindResultDO::getDataType, dataType)
                        .eq(VelocityWindResultDO::getYear, importData.getYear())
                );
                if (Objects.nonNull(windResultDO)) {
                    return  true;
                }
            }
        } catch (IOException e) {
            log.error("importStationRainYearFeatureExcel--->error.", e);
        } finally {
            lock.unlock();
        }
        return false;
    }

    @Override
    public void exportExcel(VelocityWindResultPageReqVO pageReqVO, HttpServletResponse response) throws IOException{
        StationDataTypeEnumV2 dataTypeEnum = StationDataTypeEnumV2.fromCode(pageReqVO.getDataType());

        String fileName = URLEncoder.encode(dataTypeEnum.getDescription() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        List<VelocityWindResultDO> list = this.getVelocityWindResultPage(pageReqVO).getList();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        List<VelocityWindResultRespVO> velocityWindResultRespVOS = BeanUtils.toBean(list, VelocityWindResultRespVO.class);
        ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + dataTypeEnum.getCode() + PlanningDesignConstants.EXCEL_SUFFIX);
        ExcelWriter excelWriter = null;
        excelWriter = EasyExcel.write(response.getOutputStream())
                // 利用模板的输出流
                .withTemplate(resource.getInputStream())
                .build();

        WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
        excelWriter.fill(velocityWindResultRespVOS, writeSheet);
        excelWriter.finish();
    }

    public  void importeVlocityExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException {
        // 获取excel内容
        List<VelocityWindResultImportModel> importDataList = EasyExcel.read(file.getInputStream(),
                        VelocityWindResultImportModel.class, null)
                .sheet()
                .headRowNumber(1)
                .doReadSync();
        if (CollectionUtils.isEmpty(importDataList)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
        }
        Set<String> seenYears = new HashSet<>();
        for (int i = 0; i < importDataList.size(); i++) {
            VelocityWindResultImportModel importData = importDataList.get(i);
            // 年份非空校验
            if (importData.getYear() == null ||importData.getYear().isBlank()) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR,  i + 2 ,"年份不能为空");
            }
            // 年份格式校验（必须为4位数字）
            if (!importData.getYear().trim().matches("^\\d{4}$")) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, i + 2, "年份必须为4位数字");
            }
            // 年份重复校验
            if (!seenYears.add(importData.getYear().trim())) {
                throw exception(ErrorCodeConstants.GRAIN_CHECK_ERROR,  i+2 , "年份填写重复");
            }

            if (importData.getVelocityMax() == null ||importData.getVelocityMax().isBlank()) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR,  i + 2 , "最大风速不能为空");
            }
        }
        VelocityWindResultSaveReqVO windResultSaveReqVO = new VelocityWindResultSaveReqVO();
        windResultSaveReqVO.setStationId(stationId);
        windResultSaveReqVO.setStationType(stationType);
        windResultSaveReqVO.setDataType(dataType);
        dayDataToDbBuilder(windResultSaveReqVO, importDataList, null, true);
    }
    private void dayDataToDbBuilder(VelocityWindResultSaveReqVO updateReqVO,
                                    List<VelocityWindResultImportModel> importDataList,
                                    List<Long> updateIdList,
                                    boolean importFlag){
        Long stationId = updateReqVO.getStationId();
        Integer stationType = updateReqVO.getStationType();
        Integer dataType = updateReqVO.getDataType();

        // 获取版本号（如有需要可自定义实现）
        Integer versions = fetchVersions(stationId, stationType, dataType);

        // 组装数据
        List<VelocityWindResultDO> resultList = dataBuilder(stationId, stationType, dataType, importDataList, versions);

        // 处理ID集合
        List<Long> idList = new ArrayList<>();
        if (updateIdList != null) {
            idList.addAll(updateIdList);
        }
        // 如果VO有删除ID
        if (updateReqVO.getId() != null) {
            idList.add(updateReqVO.getId());
        }

        // 写入数据
        dataUpdate(stationId, stationType, dataType, resultList, idList, importFlag);
    }
    private void dataUpdate(Long stationId, Integer stationType, Integer dataType,
                            List<VelocityWindResultDO> resultList,
                            List<Long> idList, boolean importFlag) {
        // 批量导入
        if (importFlag) {
            batchImport(resultList);
        } else {
            // 更新/删除
            insertBatch(resultList, idList);
        }
        // 如有统计信息更新等其它操作，可在此补充
    }

    public void insertBatch(List<VelocityWindResultDO> resultList, List<Long> idList) {
        List<VelocityWindResultDO> insertList = new ArrayList<>();

        VelocityWindResultDO windResultDO ;

        if (CollectionUtil.isNotEmpty(resultList)) {
            // 本次修改的数据
            insertList.addAll(resultList);
            windResultDO = resultList.get(0);
        } else if (CollectionUtil.isNotEmpty(idList)) {
            Long id = idList.get(0);
            // 历史数据
            windResultDO = velocityWindResultMapper.selectById(id);
        } else {
            return;
        }

        // 更新当前数据为历史版本
        Long stationId = windResultDO.getStationId();
        Integer stationType = windResultDO.getStationType();
        Integer dataType = windResultDO.getDataType();

        List<VelocityWindResultDO> preVersionList = listCurrent(stationId, dataType);

        velocityWindResultMapper.updateHistoryVersion(stationId, stationType, dataType);

        // 获取上一个版本的数据集
        if (CollectionUtil.isNotEmpty(preVersionList)) {
            String loginUserId = WebFrameworkUtils.getLoginUserId().toString();

            // 挑选出本次没有未修改的数据，作为最新版本数据
            List<VelocityWindResultDO> unmodifiedList = preVersionList.stream()
                    .filter(item -> (!idList.contains(item.getId())))
                    .peek(item -> {
                        item.setLatest(LatestEnum.LATEST.getType());
                        item.setId(null);
                        item.setCreator(loginUserId);
                        item.setUpdater(loginUserId);
                    }).toList();

            // 插入的数据分为：本次修改的、本次未修改的
            // 该部分为：本次未修改的部分
            insertList.addAll(unmodifiedList);
        }

        writeProcess(stationId, dataType, insertList, stationType);
    }

    private void batchImport(List<VelocityWindResultDO> dataList) {
        // 1. 导入数据为空直接返回
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        List<VelocityWindResultDO> insertList = new ArrayList<>();
        // 1. 按关键字段分组去重
        Map<String, List<VelocityWindResultDO>> groupedByKey = dataList.stream()
                .collect(Collectors.groupingBy(this::gainIndexKey));
        Set<String> keySet = groupedByKey.keySet();

        VelocityWindResultDO entity = dataList.get(0);
        Long stationId = entity.getStationId();
        Integer stationType = entity.getStationType();
        Integer dataType = entity.getDataType();
        // 2. 查询当前最新版本的历史数据
        List<VelocityWindResultDO> preList = listCurrent(stationId, dataType);
        Integer version =0;
        if (preList.size()>0){
            version = preList.get(0).getVersion();
        }
        // 3. 批量更新历史数据为历史版本
        velocityWindResultMapper.updateHistoryVersion(stationId, stationType, dataType);
        velocityWindResultMapper.delete(new LambdaQueryWrapperX<VelocityWindResultDO>()
                .eq(VelocityWindResultDO::getLatest, LatestEnum.HISTORY.getType())
                .eq(VelocityWindResultDO::getStationId, stationId)
                .eq(VelocityWindResultDO::getDataType, dataType)
                .eq(VelocityWindResultDO::getStationType, stationType)
                .eq(VelocityWindResultDO::getVersion, version));
        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(preList)) {
            List<VelocityWindResultDO> list = preList.stream()
                    .filter(item -> !keySet.contains(gainIndexKey(item)))
                    .peek(item -> {
                        item.setVersion(item.getVersion() + 1);
                        item.setLatest(1); // 最新
                        item.setId(null);  // 新插入
                        item.setCreator(loginUserId);
                        item.setUpdater(loginUserId);
                    })
                    .toList();
            insertList.addAll(list);
        }
        insertList.addAll(dataList);
        writeProcess(stationId,dataType,insertList,stationType);
    }
    private void writeProcess(Long stationId, Integer dataType, List<VelocityWindResultDO> insertList, Integer stationType) {
        // 获取下一个版本号
//        Integer nextVersion = stationDataLogService.getNextVersion(stationId, dataType);
        // 生成年鉴记录
        Integer nextVersion = stationDataLogService.findStationDataLogDOLatestCurrentVersionByStationType(stationId, stationType, dataType);

        // 数据唯一性校验（以年月日为例，可根据实际业务调整）
//        Map<String, Long> countMap = insertList.stream()
//                .collect(Collectors.groupingBy(
//                        item -> item.getYear() + "-" + item.getMonth() + "-" + item.getDay(),
//                        Collectors.counting()
//                ));
//        countMap.forEach((k, v) -> {
//            if (v > 1) {
//                throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
//            }
//        });

        // 统一设置版本号
        insertList.forEach(item->{
            item.setVersion(nextVersion +1);
            item.setLatest(LatestEnum.LATEST.getType());
        });
        // 批量插入
        velocityWindResultMapper.insertBatch(insertList);
        //更新索引数据年份信息
        statisticalInfoService.updateIndexDataYearInfo(stationType, dataType, stationId);
        // 记录变更日志
        stationDataLogService.addStationDataLog(
                StationDataLogAddModel.builder()
                        .stationType(stationType)
                        .stationId(stationId)
                        .dataType(dataType)
                        .currentVersion(nextVersion+1)
                        .build());
    }
    public List<VelocityWindResultDO> listCurrent(Long stationId, Integer dataType) {
        LambdaQueryWrapper<VelocityWindResultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VelocityWindResultDO::getStationId, stationId);
        queryWrapper.eq(VelocityWindResultDO::getDataType, dataType);
        queryWrapper.eq(VelocityWindResultDO::getLatest, 1); // 1为最新
        return velocityWindResultMapper.selectList(queryWrapper);
    }
    private String gainIndexKey(VelocityWindResultDO item) {
        return "" + item.getStationId()
                + item.getStationType()
                + item.getDataType()
                + item.getYear();
        // 如果有其它唯一性字段也可以加上
    }
    /**
     * 获取下一个版本号
     */
    private Integer fetchVersions(Long stationId, Integer stationType, Integer dataType) {
        return stationDataLogService.getNextVersion(stationId, dataType);
    }

    private List<VelocityWindResultDO> dataBuilder(Long stationId, Integer stationType, Integer dataType,
                                                               List<VelocityWindResultImportModel> importDataList, Integer nextVersion) {
        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();
        return importDataList.stream()
                .map(item -> {
                    VelocityWindResultDO resultDO = new VelocityWindResultDO();
                    resultDO.setStationId(stationId);
                    resultDO.setStationType(stationType);
                    resultDO.setDataType(dataType);
                    resultDO.setYear(Integer.valueOf(item.getYear()));
                    resultDO.setVelocityMax(item.getVelocityMax());
                    resultDO.setWind1(item.getWind1());
                    resultDO.setWind2(item.getWind2());
                    resultDO.setWind3(item.getWind3());
                    resultDO.setCreator(loginUserId);
                    resultDO.setUpdater(loginUserId);
                    resultDO.setLatest(1);
                    return resultDO;
                }).toList();
    }
    public List<Integer> listYear(Long stationId, Integer dataType) {
        return  velocityWindResultMapper.listYear(stationId,dataType);
    }
}
