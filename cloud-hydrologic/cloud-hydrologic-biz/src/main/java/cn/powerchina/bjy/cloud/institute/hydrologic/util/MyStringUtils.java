package cn.powerchina.bjy.cloud.institute.hydrologic.util;

import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * @Description: 错误校验
 * @Author: yhx
 * @CreateDate: 2024/7/19
 */
@Slf4j
public class MyStringUtils {

    /**
     * 校验年份
     *
     * @param year
     * @param lineNumber
     */
    public static void checkYear(String year, Integer lineNumber, boolean importFlag) {
        try {
            if (org.apache.commons.lang3.StringUtils.isBlank(year)) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_YEAR_EMPTY_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_YEAR_EMPTY_ERROR);
                }
            }
            if (Integer.parseInt(year) <= 0) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_YEAR_FORMAT_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_YEAR_FORMAT_ERROR, year);
                }
            }
        } catch (NumberFormatException e) {
            log.error("checkYear---error", e);
            if (importFlag) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_YEAR_FORMAT_ERROR, lineNumber);
            } else {
                throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_YEAR_FORMAT_ERROR, year);
            }
        }
    }

    /**
     * 校验年月
     *
     * @param year
     * @param month
     * @param lineNumber
     */
    public static void checkYearMonth(String year, String month, Integer lineNumber, boolean importFlag) {
        checkYear(year, lineNumber, importFlag);
        try {
            if (org.apache.commons.lang3.StringUtils.isBlank(month)) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_MONTH_EMPTY_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_MONTH_EMPTY_ERROR, year);
                }
            }
            if (Integer.parseInt(month) <= 0 || Integer.parseInt(month) > 12) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_MONTH_FORMAT_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_MONTH_FORMAT_ERROR, year, month);
                }
            }
        } catch (NumberFormatException e) {
            log.error("checkYearMonth---error", e);
            if (importFlag) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_MONTH_FORMAT_ERROR, lineNumber);
            } else {
                throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_MONTH_FORMAT_ERROR, year, month);
            }
        }
    }

    // 判断是否为闰年
    private static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    public static boolean isMonthDayFormat(String content, Integer year, boolean importFlag) {
        if (StringUtil.isEmpty(content)) {
            return true;
        }
        // 支持多种格式
        String[] formats = {"yyyy-M-d","yyyy-MM-dd"};

        for (String format : formats) {
            SimpleDateFormat dateFormat = new SimpleDateFormat(format);
            dateFormat.setLenient(false); // 设置严格的解析模式
            try {
                // 尝试将字符串按照当前格式解析
                dateFormat.parse(year + "-" +content);

                // 检查是否为闰年
                boolean isLeapYear = isLeapYear(year);

                // 获取月份和日期
                String[] parts = content.split("-");
                int month = Integer.parseInt(parts[0]);
                int day = Integer.parseInt(parts[1]);

                // 验证日期的有效性
                if (month < 1 || month > 12) {
                    continue; // 月份无效，尝试下一个格式
                }

                if (day < 1) {
                    continue; // 日期无效，尝试下一个格式
                }

                if (month == 2) {
                    if (isLeapYear && day > 29) {
                        continue; // 闰年2月最多29天
                    } else if (!isLeapYear && day > 28) {
                        continue; // 平年2月最多28天
                    }
                } else if (month == 4 || month == 6 || month == 9 || month == 11) {
                    if (day > 30) {
                        continue; // 30天的月份
                    }
                } else if (day > 31) {
                    continue; // 31天的月份
                }

                return true; // 格式和日期都有效
            } catch (ParseException e) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_PERIOD_FORMAT_ERROR, content);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_PERIOD_FORMAT_ERROR, content);
                }
            }
        }
        return false;
    }
    /**
     * 校验年月日
     *
     * @param month
     * @param lineNumber
     */
    public static void checkYearMonthDay(String year, String month, String day, Integer lineNumber, boolean importFlag) {
        checkYear(year, lineNumber, importFlag);
        checkYearMonth(year, month, lineNumber, importFlag);

        try {
            int y = Integer.parseInt(year);
            int m = Integer.parseInt(month);
            int d = Integer.parseInt(day);

            if (org.apache.commons.lang3.StringUtils.isBlank(day)) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_DAY_EMPTY_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_DAY_EMPTY_ERROR, year, month);
                }
            }

            int[] daysInMonth = {31, isLeapYear(y) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
            if (d <= 0 || d > daysInMonth[m - 1]) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_DAY_FORMAT_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_DAY_FORMAT_ERROR, year, month, day);
                }
            }
        } catch (NumberFormatException e) {
            if (importFlag) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_DAY_FORMAT_ERROR, lineNumber);
            } else {
                throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_DAY_FORMAT_ERROR, year, month, day);
            }
        }
    }


    /**
     * 校验年月日
     */
    public static Boolean checkYearMonthDay(Integer year, Integer month, Integer day) {
        try {
            int[] daysInMonth = {31, isLeapYear(year) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
            if (day <= 0 || day > daysInMonth[month - 1]) {
                return false;
            }
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }

    public static void checkYearMonthDayHoursMinute(String year, String month, String day, String hoursMinute, Integer lineNumber, boolean importFlag) {
        checkYear(year, lineNumber, importFlag);
        checkYearMonth(year, month, lineNumber, importFlag);
        checkYearMonthDay(year, month, day, lineNumber, importFlag);
        try {
//            if (org.apache.commons.lang3.StringUtils.isBlank(hoursMinute)) {
//                if (importFlag) {
//                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_HOURSMINUTE_EMPTY_ERROR, lineNumber);
//                } else {
//                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_HOURSMINUTE_EMPTY_ERROR, year, month, day);
//                }
//            }
            // 如果时分为null或空字符串,直接返回允许通过
            if (hoursMinute == null || org.apache.commons.lang3.StringUtils.isBlank(hoursMinute)) {
                return;
            }
            int timeInt = Integer.parseInt(hoursMinute);
            if (timeInt < 0 || timeInt >= 24) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_HOURSMINUTE_FORMAT_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_HOURSMINUTE_FORMAT_ERROR, year, month, day, hoursMinute);
                }
            }
        } catch (NumberFormatException e) {
            // 不是整数，尝试解析为 HH:MM 格式
            if (!isValidTimeString(hoursMinute)) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_HOURSMINUTE_FORMAT_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_HOURSMINUTE_FORMAT_ERROR, year, month, day, hoursMinute);
                }
            }
        }

    }

    public static void checkYearMonthDayStartHoursMinuteEndHoursMinute(String year, String month, String day, String startHourMinute, String endHourMinute, Integer lineNumber, boolean importFlag) {
        checkYear(year, lineNumber, importFlag);
        checkYearMonth(year, month, lineNumber, importFlag);
        checkYearMonthDay(year, month, day, lineNumber, importFlag);
        try {
            if (org.apache.commons.lang3.StringUtils.isBlank(startHourMinute)) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_STARTHOURMINUTE_EMPTY_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_STARTHOURMINUTE_EMPTY_ERROR, year, month, day);
                }
            }
            int timeInt = Integer.parseInt(startHourMinute);
            if (timeInt < 0 || timeInt > 24) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_STARTHOURMINUTE_FORMAT_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_STARTHOURMINUTE_FORMAT_ERROR, year, month, day, startHourMinute);
                }
            }
        } catch (NumberFormatException e) {
            // 不是整数，尝试解析为 HH:MM 格式
            if (!isValidTimeString(startHourMinute)) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_STARTHOURMINUTE_FORMAT_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_STARTHOURMINUTE_FORMAT_ERROR, year, month, day, startHourMinute);
                }
            }
        }
        try {
            if (org.apache.commons.lang3.StringUtils.isBlank(endHourMinute)) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_ENDHOURMINUTE_EMPTY_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_ENDHOURMINUTE_EMPTY_ERROR, year, month, day);
                }
            }
            // 特殊处理 "24:00" 和 "00:00"
            if ("24:00".equals(endHourMinute) || "00:00".equals(endHourMinute)) {
                return; // 这两种格式都是合法的
            }
            int timeInt = Integer.parseInt(endHourMinute);
            if (timeInt < 0 || timeInt > 24) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_ENDHOURMINUTE_FORMAT_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_ENDHOURMINUTE_FORMAT_ERROR, year, month, day, endHourMinute);
                }
            }
        } catch (NumberFormatException e) {
            // 不是整数，尝试解析为 HH:MM 格式
            if (!isValidTimeString(endHourMinute)) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_ENDHOURMINUTE_FORMAT_ERROR, lineNumber);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_ENDHOURMINUTE_FORMAT_ERROR, year, month, day, endHourMinute);
                }
            }
        }
    }

    private static boolean isValidTimeString(String timeStr) {
        Pattern pattern = Pattern.compile("^(([01]?[0-9]|2[0-3]):[0-5][0-9]|24:00|00:00)$");
        Matcher matcher = pattern.matcher(timeStr);
        if (!matcher.matches()) {
            return false;
        }
        return true;
    }

    public static boolean isMonthDayFormatHint(String content, Integer year, boolean importFlag, int i) {
        if (StringUtil.isEmpty(content)) {
            return true;
        }
        // 支持多种格式
        String[] formats = {"yyyy-M-d","yyyy-MM-dd"};

        for (String format : formats) {
            SimpleDateFormat dateFormat = new SimpleDateFormat(format);
            dateFormat.setLenient(false); // 设置严格的解析模式
            try {
                // 尝试将字符串按照当前格式解析
                dateFormat.parse(year + "-" +content);

                // 检查是否为闰年
                boolean isLeapYear = isLeapYear(year);

                // 获取月份和日期
                String[] parts = content.split("-");
                int month = Integer.parseInt(parts[0]);
                int day = Integer.parseInt(parts[1]);

                // 验证日期的有效性
                if (month < 1 || month > 12) {
                    continue; // 月份无效，尝试下一个格式
                }

                if (day < 1) {
                    continue; // 日期无效，尝试下一个格式
                }

                if (month == 2) {
                    if (isLeapYear && day > 29) {
                        continue; // 闰年2月最多29天
                    } else if (!isLeapYear && day > 28) {
                        continue; // 平年2月最多28天
                    }
                } else if (month == 4 || month == 6 || month == 9 || month == 11) {
                    if (day > 30) {
                        continue; // 30天的月份
                    }
                } else if (day > 31) {
                    continue; // 31天的月份
                }

                return true; // 格式和日期都有效
            } catch (ParseException e) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_HINT_DATA_PERIOD_FORMAT_ERROR, i+3,content);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_HINT_DATA_PERIOD_FORMAT_ERROR, i+3,content);
                }
            }
        }
        return false;
    }
}


