package cn.powerchina.bjy.cloud.institute.hydrologic.service.graininfoyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.granininfoyear.vo.GrainInfoYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.granininfoyear.vo.GrainInfoYearRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.granininfoyear.vo.GrainInfoYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.graininfoyear.GrainInfoYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.graininfoyear.GrainInfoYearMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.GrainInfoImportYearModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.StationDataLogAddModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.BaseExcelProcessorAdapter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.GRAIN_INFO_NOT_EXISTS;

/**
 * 水文-月年平均悬移质颗粒级配 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class GrainInfoYearServiceImpl extends BaseExcelProcessorAdapter<GrainInfoYearDO, GrainInfoImportYearModel> implements GrainInfoYearService {

    @Resource
    private GrainInfoYearMapper grainInfoMapper;

    @Override
    public Long createGrainInfo(GrainInfoYearSaveReqVO createReqVO) {
        // 插入
        GrainInfoYearDO grainInfo = BeanUtils.toBean(createReqVO, GrainInfoYearDO.class);
        grainInfoMapper.insert(grainInfo);
        // 返回
        return grainInfo.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGrainInfo(GrainInfoYearSaveReqVO updateReqVO) {
        if (null == updateReqVO || null == updateReqVO.getStationId() || CollectionUtils.isEmpty(updateReqVO.getDataList())) {
            return;
        }
        List<GrainInfoYearDO> updateDoList = new ArrayList<>(updateReqVO.getDataList().size());
        Set<Long> ids = new HashSet<>(updateReqVO.getDataList().size());
        updateReqVO.getDataList().forEach(item -> {
            GrainInfoYearDO grainInfoDO = BeanUtils.toBean(item, GrainInfoYearDO.class);
            updateDoList.add(grainInfoDO);
            ids.add(item.getId());
        });
        validateDosExists(ids, GRAIN_INFO_NOT_EXISTS);
        try {
            EXCEL.set(updateReqVO.getStationId());
            dataProcess(updateDoList, ShowTypeEnum.LIST);
            grainInfoMapper.insertBatch(updateDoList);
        } finally {
            EXCEL.remove();
        }
    }

    @Override
    public void deleteGrainInfo(Long id) {
        // 校验存在
        validateGrainInfoExists(id);
        // 删除
        grainInfoMapper.deleteById(id);
    }

    private void validateGrainInfoExists(Long id) {
        if (grainInfoMapper.selectById(id) == null) {
            throw exception(GRAIN_INFO_NOT_EXISTS);
        }
    }

    @Override
    public GrainInfoYearDO getGrainInfo(Long id) {
        return grainInfoMapper.selectById(id);
    }

    @Override
    public PageResult<GrainInfoYearRespVO> getGrainInfoPage(GrainInfoYearPageReqVO pageReqVO) {
        // 查询最新的
        StationDataLogDO dataLogDO;
        if (Objects.nonNull(pageReqVO.getLogId())) {
            dataLogDO = stationDataLogService.findById(pageReqVO.getLogId());
            if (Objects.isNull(dataLogDO)) {
                return PageResult.empty();
            }
            pageReqVO.setVersion(dataLogDO.getCurrentVersion());
            pageReqVO.setLatest(null);
        } else {
            pageReqVO.setLatest(LatestEnum.LATEST.getType());
            pageReqVO.setVersion(null);
        }
        PageResult<GrainInfoYearDO> pageDO = grainInfoMapper.selectPage(pageReqVO);
        if (pageDO == null || CollectionUtils.isEmpty(pageDO.getList())) {
            return null;
        }
        PageResult<GrainInfoYearRespVO> result = PageResult.empty();
        result.setTotal(pageDO.getTotal());

        List<GrainInfoYearRespVO> pageData = new ArrayList<>(pageDO.getList().size());
        pageDO.getList().forEach(item -> {
            GrainInfoYearRespVO data = BeanUtils.toBean(item, GrainInfoYearRespVO.class);
            pageData.add(data);
        });
        result.setList(pageData);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importData(MultipartFile file, Long stationId) throws IOException {
        EXCEL.set(stationId);
        String lockKey = String.format(PlanningDesignConstants.HYDROLOGIC_GRAIN_INFO_YEAR_IMPORT_KEY, stationId);
        importData(file, lockKey, 2, null);
    }

    @Override
    public void exportGrainInfoExcel(HttpServletResponse response, GrainInfoYearPageReqVO pageReqVO) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(StationDataTypeEnum.HYDROLOGIC_STATION_GRAIN_INFO_YEAR.getDesc() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            PageResult<GrainInfoYearRespVO> pageResult = getGrainInfoPage(pageReqVO);
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + StationDataTypeEnum.HYDROLOGIC_STATION_GRAIN_INFO_YEAR.getType() + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream())
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            excelWriter.fill(pageResult.getList(), writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("exportGrainInfoExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }

    @Override
    protected BaseMapperX<GrainInfoYearDO> getMapper() {
        return grainInfoMapper;
    }

    @Override
    protected List<GrainInfoYearDO> validateStationExistsImport(List<GrainInfoImportYearModel> importData) {
        if (CollectionUtils.isEmpty(importData)) {
            return new ArrayList<>();
        }
        List<GrainInfoYearDO> result = new ArrayList<>(importData.size());
        importData.forEach(item -> {
            GrainInfoYearDO data = BeanUtils.toBean(item, GrainInfoYearDO.class);
            result.add(data);
        });
        return result;
    }

    @Override
    protected void dataProcess(List<GrainInfoYearDO> grainInfoDOS, ShowTypeEnum source) {
        if (CollectionUtils.isEmpty(grainInfoDOS)) {
            return;
        }
        Set<String> currentIdentify = new HashSet<>(grainInfoDOS.size());
        // 默认版本为0
        for (int i = 0; i < grainInfoDOS.size(); i++) {
            GrainInfoYearDO item = grainInfoDOS.get(i);
            // 判断为更新接口用
            if (null == item.getStationId()) {
                item.setStationId(EXCEL.get());
            }
            item.setId(null);
            item.setVersion(0);
            item.setLatest(LatestEnum.LATEST.getType());
            currentIdentify.add(item.getYear() + "");
        }
        // 没有历史数据则不处理
        List<GrainInfoYearDO> beforeData = getBeforeData(GrainInfoYearDO::getStationId, GrainInfoYearDO::getLatest, GrainInfoYearDO::getDeleted);
        if (CollectionUtils.isEmpty(beforeData)) {
            // 插入变更记录
            stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get())
                    .currentVersion(0).dataType(StationDataTypeEnum.HYDROLOGIC_STATION_GRAIN_INFO_YEAR.getType()).build());
            return;
        }
        // 赋默认值，避免全量导入时无法正常赋值情况
        AtomicInteger oldVersion = new AtomicInteger(beforeData.get(0).getVersion());
        // 过滤掉本次新增的数据，并加入入库集合中
        beforeData.stream().filter(item -> !currentIdentify.contains(item.getYear() + "")).forEach(oldData -> {
            oldVersion.set(oldData.getVersion());
            oldData.setId(null);
            oldData.setLatest(LatestEnum.LATEST.getType());
            grainInfoDOS.add(oldData);
        });
        // 将版本号加一
        grainInfoDOS.forEach(item -> {
            item.setId(null);
            item.setLatest(LatestEnum.LATEST.getType());
            item.setVersion(oldVersion.get() + 1);
        });
        // 更新历史数据
        UpdateWrapper<GrainInfoYearDO> updateWrapper = getUpdateWrapper(
                GrainInfoYearDO::getStationId, GrainInfoYearDO::getLatest, GrainInfoYearDO::getVersion,
                GrainInfoYearDO::getDeleted, oldVersion.get());
        GrainInfoYearDO entity = new GrainInfoYearDO();
        entity.setLatest(LatestEnum.HISTORY.getType());
        grainInfoMapper.update(entity, updateWrapper);
        // 插入变更记录
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get())
                .currentVersion(oldVersion.get() + 1).dataType(StationDataTypeEnum.HYDROLOGIC_STATION_GRAIN_INFO_YEAR.getType()).build());
    }
}