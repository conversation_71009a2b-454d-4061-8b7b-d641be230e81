package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.floodextract;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.floodextract.vo.FloodExtractPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.floodextract.FloodExtractDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.HydrologicChecker;
import org.apache.ibatis.annotations.Mapper;

/**
 * 水文站-洪水水文要素摘录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FloodExtractMapper extends BaseMapperX<FloodExtractDO>, HydrologicChecker<FloodExtractDO> {

    default PageResult<FloodExtractDO> selectPage(FloodExtractPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FloodExtractDO>()
                .eqIfPresent(FloodExtractDO::getStationId, reqVO.getStationId())
                .eqIfPresent(FloodExtractDO::getYear, reqVO.getYear())
                .eqIfPresent(FloodExtractDO::getMonth, reqVO.getMonth())
                .eqIfPresent(FloodExtractDO::getDay, reqVO.getDay())
                .eqIfPresent(FloodExtractDO::getHoursMinute, reqVO.getHoursMinute())
                .eqIfPresent(FloodExtractDO::getWaterLevel, reqVO.getWaterLevel())
                .eqIfPresent(FloodExtractDO::getFlow, reqVO.getFlow())
                .eqIfPresent(FloodExtractDO::getSandValue, reqVO.getSandValue())
                .eqIfPresent(FloodExtractDO::getVersion, reqVO.getVersion())
                .eqIfPresent(FloodExtractDO::getLatest, reqVO.getLatest())
                .betweenIfPresent(FloodExtractDO::getCurrentDay, reqVO.getCurrentDay())
                .orderByAsc(FloodExtractDO::getCurrentDay));
    }

}