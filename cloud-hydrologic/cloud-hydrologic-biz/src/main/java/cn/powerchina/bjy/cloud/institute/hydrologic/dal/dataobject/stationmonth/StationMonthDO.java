package cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationday.StationDayDO;
import lombok.*;

import java.time.LocalDate;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import lombok.experimental.Accessors;

/**
 * 站点-月统计 DO
 *
 * <AUTHOR>
 */
@TableName("hydrologic_station_month")
@KeySequence("hydrologic_station_month_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = false)
public class StationMonthDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 水文站id
     */
    private Long stationId;
    /**
     * 站点类型，1：雨量站，2：水文站，3：气象站
     */
    private Integer stationType;
    /**
     * 数据类型
     */
    private Integer dataType;
    /**
     * 年
     */
    private Integer year;
    /**
     * 月
     */
    private Integer month;
    /**
     * 月平均值
     */
    private String averageValue;
    /**
     * 月平均值备注
     */
    private String averageValueRemark;
    /**
     * 月最大值
     */
    private String maxValue;
    /**
     * 月最大值出现日期
     */
    private String maxValueDate;
    /**
     * 月最大值备注
     */
    private String maxValueRemark;
    /**
     * 月最小值
     */
    private String minValue;
    /**
     * 月最小值出现日期
     */
    private String minValueDate;
    /**
     * 月最小值备注
     */
    private String minValueRemark;
    /**
     * 降水-月降水量(mm)
     */
    private String value;
    /**
     * 降水-月降水日数
     */
    private String valueDays;
    /**
     * 降水-最大日降水量(mm)
     */
    private String maxDayValue;


    /**
     * 0.005粒径级（mm）
     */
    private String grain005;
    /**
     * 0.007粒径级（mm）
     */
    private String grain007;
    /**
     * 0.01粒径级（mm）
     */
    private String grain01;
    /**
     * 0.025粒径级（mm）
     */
    private String grain025;
    /**
     * 0.05粒径级（mm）
     */
    private String grain05;
    /**
     * 0.1粒径级（mm）
     */
    private String grain1;
    /**
     * 0.25粒径级（mm）
     */
    private String grain25;
    /**
     * 0.5粒径级（mm）
     */
    private String grain5;
    /**
     * 1.0粒径级（mm）
     */
    private String grain10;
    /**
     * 2.0粒径级（mm）
     */
    private String grain20;
    /**
     * 3.0粒径级（mm）
     */
    private String grain30;
    /**
     * 中数粒径(mm)
     */
    private String grainMid;
    /**
     * 平均粒径(mm)
     */
    private String grainAverage;
    /**
     * 最大粒径(mm)
     */
    private String grainMax;


    /**
     * 版本（根据原型待定）
     */
    private Integer version;
    /**
     * 最新版本（1：最新，0：历史版本，默认为1）
     */
    private Integer latest;
    /**
     * 记录时间
     */
    private LocalDate currentDay;
    /**
     * 备注
     */
    private String remark;


    public boolean hasDifference(StationMonthDO target) {
        return
                ObjectUtil.notEqual(this.getAverageValue(),target.getAverageValue())||
                        ObjectUtil.notEqual(this.getAverageValueRemark(),target.getAverageValueRemark())||
                        ObjectUtil.notEqual(this.getMaxValue(),target.getMaxValue())||
                        ObjectUtil.notEqual(this.getMaxValueDate(),target.getMaxValueDate())||
                        ObjectUtil.notEqual(this.getMaxValueRemark(),target.getMaxValueRemark())||
                        ObjectUtil.notEqual(this.getMinValue(),target.getMinValue())||
                        ObjectUtil.notEqual(this.getMinValueDate(),target.getMinValueDate())||
                        ObjectUtil.notEqual(this.getMinValueRemark(),target.getMinValueRemark())||
                        ObjectUtil.notEqual(this.getValue(),target.getValue())||
                        ObjectUtil.notEqual(this.getValueDays(),target.getValueDays())||
                        ObjectUtil.notEqual(this.getMaxDayValue(),target.getMaxDayValue())||
                        ObjectUtil.notEqual(this.getGrain005(),target.getGrain005())||
                        ObjectUtil.notEqual(this.getGrain007(),target.getGrain007())||
                        ObjectUtil.notEqual(this.getGrain01(),target.getGrain01())||
                        ObjectUtil.notEqual(this.getGrain025(),target.getGrain025())||
                        ObjectUtil.notEqual(this.getGrain05(),target.getGrain05())||
                        ObjectUtil.notEqual(this.getGrain1(),target.getGrain1())||
                        ObjectUtil.notEqual(this.getGrain25(),target.getGrain25())||
                        ObjectUtil.notEqual(this.getGrain5(),target.getGrain5())||
                        ObjectUtil.notEqual(this.getGrain10(),target.getGrain10())||
                        ObjectUtil.notEqual(this.getGrain20(),target.getGrain20())||
                        ObjectUtil.notEqual(this.getGrain30(),target.getGrain30())||
                        ObjectUtil.notEqual(this.getGrainMid(),target.getGrainMid())||
                        ObjectUtil.notEqual(this.getGrainAverage(),target.getGrainAverage())||
                        ObjectUtil.notEqual(this.getRemark(),target.getRemark())||
                        ObjectUtil.notEqual(this.getGrainMax(),target.getGrainMax());

    }


}