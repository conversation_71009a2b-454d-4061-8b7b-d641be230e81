package cn.powerchina.bjy.cloud.institute.hydrologic.service.tidelevelyear;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TideLevelDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TidelevelDayUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo.TideLevelMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo.TidelevelMonthUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo.TideLevelYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo.TideLevelYearRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo.TideLevelYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo.TidelevelYearUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo.VelocityWindResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sectionsurveyinfo.SectionSurveyInfoDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.suspendedgradingresult.SuspendedGradingResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelday.TideLevelDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelmonth.TideLevelMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelyear.TideLevelYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.velocitywindresult.VelocityWindResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.station.StationMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationdatalog.StationDataLogMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.tidelevelyear.TideLevelYearMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.LatestEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.PlanningDesignConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnumV2;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.RedisService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sectionsurveyinfo.SectionSurveyInfoService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationdatalog.StationDataLogService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticalinfo.StatisticalInfoService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
/**
 * 水文站—潮位年统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TideLevelYearServiceImpl implements TideLevelYearService {

    @Resource
    private TideLevelYearMapper tideLevelYearMapper;
    @Autowired
    private StationMapper stationMapper;

    @Autowired
    private StationDataLogService stationDataLogService;

    @Autowired
    private StatisticalInfoService statisticalInfoService;

    @Autowired
    private RedisService redisService;
    @Resource
    private StationDataLogMapper stationDataLogMapper;

    @Override
    public Long createTideLevelYear(TideLevelYearSaveReqVO createReqVO) {
        // 插入
        TideLevelYearDO tideLevelYear = BeanUtils.toBean(createReqVO, TideLevelYearDO.class);
        tideLevelYearMapper.insert(tideLevelYear);
        // 返回
        return tideLevelYear.getId();
    }

    @Override
    public void updateTideLevelYear(TideLevelYearSaveReqVO updateReqVO) {
        // 校验存在
        validateTideLevelYearExists(updateReqVO.getId());
        // 更新
        TideLevelYearDO updateObj = BeanUtils.toBean(updateReqVO, TideLevelYearDO.class);
        tideLevelYearMapper.updateById(updateObj);
    }

    @Override
    public void deleteTideLevelYear(Long id) {
        // 校验存在
        validateTideLevelYearExists(id);
        // 删除
        tideLevelYearMapper.deleteById(id);
    }

    private void validateTideLevelYearExists(Long id) {
        if (tideLevelYearMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.TRY_LOCK_ERROR);
        }
    }

    @Override
    public TideLevelYearDO getTideLevelYear(Long id) {
        return tideLevelYearMapper.selectById(id);
    }

    @Override
    public PageResult<TideLevelYearDO> getTideLevelYearPage(TideLevelYearPageReqVO pageReqVO) {
        //如果查询更新记录logId不为空
        Long logId = pageReqVO.getLogId();
        Integer dataType = pageReqVO.getDataType();
        Long stationId = pageReqVO.getStationId();
        Integer stationType = pageReqVO.getStationType();
        Long count = tideLevelYearMapper.selectCount(new LambdaQueryWrapperX<TideLevelYearDO>()
                .gtIfPresent(TideLevelYearDO::getId, logId)
                .eq(TideLevelYearDO::getStationId, stationId)
                .eq(TideLevelYearDO::getDataType, dataType)
                .eq(TideLevelYearDO::getStationType, stationType)
               .eq(TideLevelYearDO::getYear,pageReqVO.getYear())
        );
        if(count==0){
            logId=null;
        } // 查询最新的
        if (Objects.nonNull(logId)) {
            StationDataLogDO stationDataLogDO = stationDataLogService.findById(pageReqVO.getLogId());
            if (Objects.isNull(stationDataLogDO)) {
                return PageResult.empty();
            }
            pageReqVO.setVersion(stationDataLogDO.getCurrentVersion());
            pageReqVO.setLatest(null);
            //历史数据
            Integer version = stationDataLogDO.getCurrentVersion();
            List<TideLevelYearDO>  hisdos = tideLevelYearMapper.selectHis(stationId,dataType,stationType,version,pageReqVO.getYear());
            return new PageResult<>(hisdos, (long) hisdos.size());
        } else {
            pageReqVO.setLatest(LatestEnum.LATEST.getType());
            return tideLevelYearMapper.selectPage(pageReqVO);
        }
    }

    @Override
    public void updateAndDeleteTideLevelYear(TidelevelYearUpdateReqVO reqVO) {
        Long stationId = reqVO.getStationId();
        Integer stationType = reqVO.getStationType();
        Integer dataType = reqVO.getDataType();
        Integer year = reqVO.getYear();
        //查询站点是否存在
        if (Objects.isNull(stationId) || Objects.isNull(stationMapper.selectById(stationId))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        //增加分布式锁，只能有一个导入,针对站点增加锁，站点的所有类型，只能有一个导入,页面新增时也要增加锁控制
        String key = String.format(PlanningDesignConstants.STATION_TIDE_IMPORT_TIDE_KEY, stationId, year,"");
        RLock rLock = redisService.acquireDistributedLock(key);

        try {
//            if (CollectionUtils.isEmpty(updateReqVO.getDataList())&&CollectionUtils.isEmpty(updateReqVO.getDeleteIds())) {
//                throw exception(ErrorCodeConstants.STATION_DATA_UPDATE_CONTENT_EMPTY_EXISTS_ERROR);
//            }
            if(reqVO.getIsDelete()==null){
                reqVO.setIsDelete(false);
            }
            List<TidelevelYearUpdateReqVO.TidelevelYearUpdateData> dataList = reqVO.getDataList();
            if(CollectionUtil.isNotEmpty(dataList)||reqVO.getIsDelete()){
                updateDataToDb(stationId, stationType, dataType, dataList, reqVO.getIsDelete(),year);
            }
        }catch (ServiceException e){
            throw e;
        }catch (Throwable e){
            throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
        }finally {
            rLock.unlock();
        }
    }

    private void updateDataToDb(Long stationId, Integer stationType, Integer dataType, List<TidelevelYearUpdateReqVO.TidelevelYearUpdateData> dataList, boolean b, Integer year) {
        //历史数据
        List<TideLevelYearDO> hisdos = tideLevelYearMapper.selectList(new LambdaQueryWrapperX<TideLevelYearDO>()
                .eq(TideLevelYearDO::getStationId, stationId)
                .eq(TideLevelYearDO::getDataType, dataType)
                .eq(TideLevelYearDO::getStationType, stationType)
                .eq(TideLevelYearDO::getYear, year)
                .eq(TideLevelYearDO::getLatest, 1));
        if (hisdos.isEmpty()) {
            return;
        }
        Integer version = hisdos.get(0).getVersion();
        String yearRemark = hisdos.get(hisdos.size()-1).getRemark();
        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();
        if (b) {
            tideLevelYearMapper.updateHistoryVersion(stationId, stationType, dataType, year);
            tideLevelYearMapper.delete(new LambdaQueryWrapperX<TideLevelYearDO>()
                    .eq(TideLevelYearDO::getLatest, LatestEnum.HISTORY.getType())
                    .eq(TideLevelYearDO::getStationId, stationId)
                    .eq(TideLevelYearDO::getDataType, dataType)
                    .eq(TideLevelYearDO::getStationType, stationType)
                    .eq(TideLevelYearDO::getYear, year));
        } else {
            for(TidelevelYearUpdateReqVO.TidelevelYearUpdateData data:dataList) {
                if(data.getId()==null){
                    yearRemark=data.getValue6();
                }
            }
            // 获取dataList中的所有ID
            Set<Long> dataListIds = dataList.stream()
                    .map(TidelevelYearUpdateReqVO.TidelevelYearUpdateData::getId)
                    .collect(Collectors.toSet());
            tideLevelYearMapper.update(new LambdaUpdateWrapper<TideLevelYearDO>()
                    .set(TideLevelYearDO::getLatest, 0).set(TideLevelYearDO::getDeleted, true)
                    .eq(TideLevelYearDO::getStationId, stationId)
                    .eq(TideLevelYearDO::getDataType, dataType)
                    .eq(TideLevelYearDO::getStationType, stationType)
                    .eq(TideLevelYearDO::getYear, year)
                    .eq(TideLevelYearDO::getLatest, 1));
            // 挑选出本次没有未修改的数据，作为最新版本数据

            for(TidelevelYearUpdateReqVO.TidelevelYearUpdateData info2 :dataList){
                for(TideLevelYearDO info1:hisdos){
                   if(info2.getId()!=null&& info2.getId().equals(info1.getId())){
                       info1.setYear( year);
                       info1.setStationId(stationId);
                       info1.setDataType(dataType);
                       info1.setRemark(info2.getRemark());
                       info1.setStationType(stationType);
                       info1.setValue1(info2.getValue6());
                       info1.setValue2(info2.getValue7());
                       info1.setValue3(info2.getValue8());
                       info1.setValue4(info2.getValue9());
                       info1.setValue5(info2.getValue10());
                       info1.setValue6(info2.getValue11());
                       info1.setValue7(info2.getValue12());
                       info1.setValue8(info2.getValue13());
                       info1.setValue9(info2.getValue14());
                       info1.setValue10(info2.getValue15());
                       info1.setValue11(info2.getValue16());
                       info1.setValue12(info2.getValue17());
                       info1.setValue13(info2.getValue18());
                   }
                }
            }
            String finalYearRemark = yearRemark;
            List<TideLevelYearDO> unmodifiedList = hisdos.stream()
                    .filter(item -> (!dataListIds.contains(item.getId())))
                    .peek(item -> {
                        item.setLatest(LatestEnum.LATEST.getType());
                        item.setVersion(version + 1);
                        item.setId(null);
                        item.setCreator(loginUserId);
                        item.setUpdater(loginUserId);
                    }).toList();
             unmodifiedList.get(unmodifiedList.size()-1).setRemark(finalYearRemark);
            if (unmodifiedList != null && unmodifiedList.size() > 0) {
                // 统一设置版本号
                unmodifiedList.forEach(item->{
                    item.setVersion(version +1);
                    item.setLatest(LatestEnum.LATEST.getType());
                });
                tideLevelYearMapper.insertBatch(unmodifiedList);
            }
        }
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder()
                .stationType(stationType)
                .stationId(stationId)
                .year(year.toString())
                .dataType(dataType)
                .currentVersion(version +1 )
                .build());
        statisticalInfoService.updateIndexDataYearInfo(stationType, dataType, stationId);
    }

    @Override
    public void exportTideLevelDayExcel(TideLevelYearPageReqVO pageReqVO, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(StationDataTypeEnumV2.TIDE_YEAR_EVAPORATION_CHARACTERISTICS.getDescription() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            List<TideLevelYearDO> dayList = getTideLevelYearList(pageReqVO);
            List<TideLevelYearRespVO> pageResult1 = BeanUtils.toBean(dayList, TideLevelYearRespVO.class);
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + StationDataTypeEnumV2.TIDE_YEAR_EVAPORATION_CHARACTERISTICS.getCode() + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream()) // 利用模板的输出流
                    .build();
            //组装日数据

            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            excelWriter.fill(pageResult1, writeSheet);
            //TODO 附注待完善
            Map<String, String> otherMap = new HashMap<>();
            otherMap.put("yearName", pageResult1.get(0).getYear().toString());
            otherMap.put("remark", pageResult1.get(0).getRemark());
            otherMap.put("remark1", pageResult1.get(pageResult1.size()-1).getRemark());
            excelWriter.fill(otherMap, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("exportStationRainPeriodMinuteExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }

    private List<TideLevelYearDO> getTideLevelYearList(TideLevelYearPageReqVO pageReqVO) {
        return tideLevelYearMapper.selectList(new LambdaQueryWrapperX<TideLevelYearDO>()
                .eq(TideLevelYearDO::getYear, pageReqVO.getYear())
                .eq(TideLevelYearDO::getStationId, pageReqVO.getStationId()));
    }

    @Override
    public void impotDayExcel(Long stationId, Integer dataType, Integer stationType, MultipartFile file, Integer year) {
        //查询站点是否存在
        if (Objects.isNull(stationId) || Objects.isNull(stationMapper.selectById(stationId))){
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        //增加分布式锁，只能有一个导入,针对站点增加锁，站点的所有类型，只能有一个导入,页面新增时也要增加锁控制
        String key = String.format(PlanningDesignConstants.STATION_TIDE_IMPORT_TIDE_KEY, stationId, dataType,"YEAR");
        RLock rLock = redisService.acquireDistributedLock(key);

        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }
            //获取excel内容
            List<TideYearImportModel> importDataList = EasyExcel.read(file.getInputStream(), TideYearImportModel.class, null).sheet().headRowNumber(0).doReadSync();
            if (CollectionUtils.isEmpty(importDataList)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
            }
            validateYearParameters(importDataList);
            dayDataToDb(stationId, stationType, dataType, importDataList, true,year);
        } catch (IOException e) {
            log.error("importStationRainPeriodMinuteFeatureExcel--->error.", e);
        } finally {
            rLock.unlock();
        }
    }

    private void validateYearParameters(List<TideYearImportModel> importDataList) {
        int a=0;
        String year = importDataList.get(0).getYearName();
        if (StringUtils.isBlank(year)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, 1, "年份填写错误；");
        }
        for(TideYearImportModel model: importDataList){
            Map<String,Object> map = BeanUtil.beanToMap(model);
            if(map.get("valueName")!=null&&map.get("valueName").toString().contains("潮位")){
                for(int i=1;i<13;i++){
                    if (!map.get("value"+i).toString().matches("^[-]?\\d{1,4}(\\.\\d{2})?$")) {
//                        throw exception(new ErrorCode(500,"潮位：允许输入数字（有正负），保留两位小数，整数位不大于4位。"));
//                        throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, a+1, "潮位：允许输入数字（有正负），保留两位小数，整数位不大于4位。");
                    }
                }
            }
            if(map.get("valueDate")!=null&&map.get("valueDate").toString().equals("公历：日")){
                for(int i=1;i<13;i++){
                    if (NumberUtils.isParsable(map.get("value"+i).toString())) {
                        try{
                            int num = Integer.parseInt(map.get("value"+i).toString());
                            if (num < 1 || num > 31) {
                                throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, a+1, "公历：日:只允许输入1-31");
                            }
                        }catch (Exception e){
                            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, a+1, "公历：日:只允许输入1-31");
                        }
                    }else{
                        throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, a+1, "公历：日:只允许输入1-31");
                    }
                }
            }
            if(map.get("valueDate")!=null&&map.get("valueDate").toString().equals("公历：时分")){
                for(int i=1;i<13;i++){
                    if (!isTimeFormat(map.get("value"+i).toString())) {
//                            throw exception(new ErrorCode(500,"公历、时分：时间格式，时：分"));
                        throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, a+1, "公历、时分：时间格式，时：分");
                    }
                }
            }
            if(map.get("valueDate")!=null&&map.get("valueDate").toString().equals("农历：月-日")){
                for(int i=1;i<13;i++){
                    if (map.get("value"+i).toString().length()>200) {
                        throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, a+1, "农历：月-日不大于200字");
//                        throw exception(new ErrorCode(500,"农历：月-日不大于200字"));
                    }
                }
            }
            a++;
        }
    }

    public static boolean isTimeFormat(String timeStr) {
        try {
            if(timeStr.split(":")[0].length()==1){
                timeStr="0"+timeStr;
            }
            // 定义时间格式为"时:分"
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            // 解析字符串为LocalTime对象
            LocalTime time = LocalTime.parse(timeStr, formatter);
            return true; // 如果没有抛出异常，说明格式正确
        } catch (DateTimeParseException e) {
            // 如果解析失败，捕获异常并返回false
            return false;
        }
    }

    private void dayDataToDb(Long stationId, Integer stationType, Integer dataType, List<TideYearImportModel> importDataList, boolean b, Integer year) {
        if(importDataList.get(0)!=null&&!importDataList.get(0).getYearName().isEmpty()){
            year=Integer.parseInt(importDataList.get(0).getYearName());
        }
        String remark1="";
        if(importDataList.get(1)!=null&&!importDataList.get(1).getValue4().isEmpty()){
            remark1=importDataList.get(1).getValue4();
        }
        importDataList.remove(0);
        importDataList.remove(0);
        importDataList.remove(0);
        importDataList.remove(0);
        //组装数据
        List<TideLevelYearDO> bean = BeanUtils.toBean(importDataList, TideLevelYearDO.class);
        TideLevelYearDO tideLevelYearDO1 = bean.get(bean.size() - 1);
        String remark="";
        if(tideLevelYearDO1.getValue1()!=null){
             remark = tideLevelYearDO1.getValue1();
        }
        bean.remove(bean.size() - 1);
        TideLevelYearDO ti=tideLevelYearMapper.selectOne(new LambdaQueryWrapperX<TideLevelYearDO>()
                .select(TideLevelYearDO::getVersion)
                .eq(TideLevelYearDO::getStationId, stationId)
                .eq(TideLevelYearDO::getDataType, dataType)
                .eq(TideLevelYearDO::getStationType, stationType)
                .eq(TideLevelYearDO::getYear, year)
                .last("LIMIT 1"));
        Integer version=1;
        if(ti!=null&&ti.getVersion()!=null){
            tideLevelYearMapper.delete(new LambdaQueryWrapperX<TideLevelYearDO>()
                    .eq(TideLevelYearDO::getStationId, stationId)
                    .eq(TideLevelYearDO::getDataType, dataType)
                    .eq(TideLevelYearDO::getStationType, stationType)
                    .eq(TideLevelYearDO::getYear, year)
            );
            version=ti.getVersion()+1;
        }
        int i=0;
        for (TideLevelYearDO tideLevelYearDO : bean) {
            tideLevelYearDO.setYear(year);
            tideLevelYearDO.setStationId(stationId);
            tideLevelYearDO.setStationType(stationType);
            tideLevelYearDO.setDataType(dataType);
//            tideLevelYearDO.setRemark(remark);
            tideLevelYearDO.setVersion(version);
            if (i==0){
                tideLevelYearDO.setRemark(remark1);
            }else {
                tideLevelYearDO.setRemark(remark);
            }
            i++;
        }
        insertBatch(bean);
    }

    private void insertBatch(List<TideLevelYearDO> importDataList) {
        if (CollectionUtils.isEmpty(importDataList)) {
            return;
        }
        TideLevelYearDO tideDayImportModel = importDataList.get(importDataList.size()-1);
        //更新历史版本
        Long stationId = tideDayImportModel.getStationId();
        //当前版本（即将变为历史版本）
        Integer dataType = tideDayImportModel.getDataType();
        Integer year = tideDayImportModel.getYear();
        String remark = tideDayImportModel.getRemark();
        Integer stationType = tideDayImportModel.getStationType();
        // 2. 查询当前最新版本的历史数据
        LambdaQueryWrapper<TideLevelYearDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TideLevelYearDO::getStationId, stationId);
        queryWrapper.eq(TideLevelYearDO::getDataType, dataType);
        queryWrapper.eq(TideLevelYearDO::getYear, year);
        queryWrapper.eq(TideLevelYearDO::getStationType, stationType);
        queryWrapper.eq(TideLevelYearDO::getLatest, 1); // 1为最新
        List<TideLevelYearDO> preList  = tideLevelYearMapper.selectList(queryWrapper);
        Integer version;
        if (preList.size()>0){
            version = preList.get(0).getVersion();
        } else {
            version = 0;
        }
        // 3. 批量更新历史数据为历史版本
        tideLevelYearMapper.updateHistoryVersion(stationId, stationType, dataType, year);
        tideLevelYearMapper.delete(new LambdaQueryWrapperX<TideLevelYearDO>()
                .eq(TideLevelYearDO::getLatest, LatestEnum.HISTORY.getType())
                .eq(TideLevelYearDO::getStationId, stationId)
                .eq(TideLevelYearDO::getDataType, dataType)
                .eq(TideLevelYearDO::getStationType, stationType)
                .eq(TideLevelYearDO::getYear, year)
                .eq(TideLevelYearDO::getVersion, version));
        // 统一设置版本号
        importDataList.forEach(item -> {
            item.setVersion(version + 1);
            item.setLatest(LatestEnum.LATEST.getType());
        });
        tideLevelYearMapper.insertBatch(importDataList);
        //插入变更记录
//        Integer nextVersion = stationDataLogService.getNextVersionV2(stationId, dataType);
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder()
                .stationType(tideDayImportModel.getStationType())
                .stationId(stationId)
                .year(year.toString())
                .dataType(dataType)
                .currentVersion(version +1 )
                .yearRemark(remark)
                .build());
        statisticalInfoService.updateIndexDataYearInfo(stationType, dataType, stationId);
    }

    @Override
    public List<TideLevelYearForm> getYearBookList(TideLevelYearPageReqVO reqVO) {
        //如果查询更新记录logId不为空
        Long logId = reqVO.getLogId();
        Integer dataType = reqVO.getDataType();
        Long stationId = reqVO.getStationId();
        Integer stationType = reqVO.getStationType();
        Long count = stationDataLogMapper.selectCount(new LambdaQueryWrapperX<StationDataLogDO>()
                        .gtIfPresent(StationDataLogDO::getId, logId)
                        .eq(StationDataLogDO::getStationId, stationId)
                        .eq(StationDataLogDO::getDataType, dataType)
                        .eq(StationDataLogDO::getStationType, stationType)
        );
        List<TideLevelYearDO> tideLevelYearDOS;
        if(count==0){
            logId=null;
        } // 查询最新的
        if (Objects.nonNull(logId)) {
            StationDataLogDO stationDataLogDO = stationDataLogService.findById(reqVO.getLogId());
            if (Objects.isNull(stationDataLogDO)) {
                return null;
            }
            reqVO.setVersion(stationDataLogDO.getCurrentVersion());
            reqVO.setLatest(null);
            //历史数据
            Integer version = stationDataLogDO.getCurrentVersion();
            tideLevelYearDOS = tideLevelYearMapper.selectHis(stationId, dataType, stationType, version, reqVO.getYear());
            //组装表数据(组装潮位)
            List<TideLevelYearForm> tideLevelYearForms = new ArrayList<>();
            assemblyTable(tideLevelYearForms,tideLevelYearDOS);
            //组装表数据(组装潮差)
            assemblyTableTidalRange(tideLevelYearForms,tideLevelYearDOS);
            //组装表数据(组装潮历时)
            assemblyTableData(tideLevelYearForms,tideLevelYearDOS);
            //组装附注
            assemblyRemark(tideLevelYearForms,tideLevelYearDOS);
            return tideLevelYearForms;
        }else{
            reqVO.setVersion(null);
            reqVO.setLatest(LatestEnum.LATEST.getType());
             tideLevelYearDOS = tideLevelYearMapper.selectList(new LambdaQueryWrapperX<TideLevelYearDO>()
                    .eq(TideLevelYearDO::getYear, reqVO.getYear())
                    .eq(TideLevelYearDO::getStationId, reqVO.getStationId()));
        }
        List<TideLevelYearForm> tideLevelYearForms = new ArrayList<>();
        //组装表头
//        assemblyHeader(tideLevelYearForms,tideLevelYearDOS);
        //组装表数据(组装潮位)
        assemblyTable(tideLevelYearForms,tideLevelYearDOS);
        //组装表数据(组装潮差)
        assemblyTableTidalRange(tideLevelYearForms,tideLevelYearDOS);
        //组装表数据(组装潮历时)
        assemblyTableData(tideLevelYearForms,tideLevelYearDOS);
        //组装附注
        assemblyRemark(tideLevelYearForms,tideLevelYearDOS);
        return tideLevelYearForms;
    }

    private void assemblyHeader(List<TideLevelYearForm> tideLevelYearForms, List<TideLevelYearDO> tideLevelYearDOS) {
        TideLevelYearDO tideLevelYearDO = tideLevelYearDOS.get(0);
        String remark = tideLevelYearDO.getRemark();

        tideLevelYearForms.add(TideLevelYearForm.builder().value9(remark).build());
        tideLevelYearForms.add(TideLevelYearForm.builder().value1("项目/月").value6("一月").value7("二月").value8("三月")
                .value9("四月").value10("五月").value11("六月").value12("七月").value13("八月").value14("九月").value15("十月")
                .value16("十一月").value17("十二月").value18("全年").build());
        tideLevelYearForms.add(TideLevelYearForm.builder().build());
    }

    private void assemblyTable(List<TideLevelYearForm> tideLevelYearForms, List<TideLevelYearDO> tideLevelYearDOS) {
        //====================组装潮位=====================
        TideLevelYearDO yes0 = tideLevelYearDOS.get(0);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).remark(yes0.getRemark()).value1("潮位").value2("高潮").value3("最高").value4("潮位(m)").value6(yes0.getValue1())
                .value7(yes0.getValue2()).value8(yes0.getValue3()).value9(yes0.getValue4()).value10(yes0.getValue5())
                .value11(yes0.getValue6()).value12(yes0.getValue7()).value13(yes0.getValue8()).value14(yes0.getValue9())
                .value15(yes0.getValue10()).value16(yes0.getValue11()).value17(yes0.getValue12()).value18(yes0.getValue13()).id(yes0.getId()).build());

        TideLevelYearDO yes1 = tideLevelYearDOS.get(1);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes1.getValue1())
                .value7(yes1.getValue2()).value8(yes1.getValue3()).value9(yes1.getValue4()).value10(yes1.getValue5())
                .value11(yes1.getValue6()).value12(yes1.getValue7()).value13(yes1.getValue8()).value14(yes1.getValue9())
                .value15(yes1.getValue10()).value16(yes1.getValue11()).value17(yes1.getValue12()).value18(yes1.getValue13()).id(yes1.getId()).build());

        TideLevelYearDO yes2 = tideLevelYearDOS.get(2);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("公历:时分").value6(yes2.getValue1())
                .value7(yes2.getValue2()).value8(yes2.getValue3()).value9(yes2.getValue4()).value10(yes2.getValue5())
                .value11(yes2.getValue6()).value12(yes2.getValue7()).value13(yes2.getValue8()).value14(yes2.getValue9())
                .value15(yes2.getValue10()).value16(yes2.getValue11()).value17(yes2.getValue12()).value18(yes2.getValue13()).id(yes2.getId()).build());

        TideLevelYearDO yes3 = tideLevelYearDOS.get(3);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:日-月").value6(yes3.getValue1())
                .value7(yes3.getValue2()).value8(yes3.getValue3()).value9(yes3.getValue4()).value10(yes3.getValue5())
                .value11(yes3.getValue6()).value12(yes3.getValue7()).value13(yes3.getValue8()).value14(yes3.getValue9())
                .value15(yes3.getValue10()).value16(yes3.getValue11()).value17(yes3.getValue12()).value18(yes3.getValue13()).id(yes3.getId()).build());

        TideLevelYearDO yes4 = tideLevelYearDOS.get(4);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("最低").value4("潮位(m)").value6(yes4.getValue1())
                .value7(yes4.getValue2()).value8(yes4.getValue3()).value9(yes4.getValue4()).value10(yes4.getValue5())
                .value11(yes4.getValue6()).value12(yes4.getValue7()).value13(yes4.getValue8()).value14(yes4.getValue9())
                .value15(yes4.getValue10()).value16(yes4.getValue11()).value17(yes4.getValue12()).value18(yes4.getValue13()).id(yes4.getId()).build());

        TideLevelYearDO yes5 = tideLevelYearDOS.get(5);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes5.getValue1())
                .value7(yes5.getValue2()).value8(yes5.getValue3()).value9(yes5.getValue4()).value10(yes5.getValue5())
                .value11(yes5.getValue6()).value12(yes5.getValue7()).value13(yes5.getValue8()).value14(yes5.getValue9())
                .value15(yes5.getValue10()).value16(yes5.getValue11()).value17(yes5.getValue12()).value18(yes5.getValue13()).id(yes5.getId()).build());

        TideLevelYearDO yes6 = tideLevelYearDOS.get(6);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("公历:时分").value6(yes6.getValue1())
                .value7(yes6.getValue2()).value8(yes6.getValue3()).value9(yes6.getValue4()).value10(yes6.getValue5())
                .value11(yes6.getValue6()).value12(yes6.getValue7()).value13(yes6.getValue8()).value14(yes6.getValue9())
                .value15(yes6.getValue10()).value16(yes6.getValue11()).value17(yes6.getValue12()).value18(yes6.getValue13()).id(yes6.getId()).build());

        TideLevelYearDO yes7 = tideLevelYearDOS.get(7);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes7.getValue1())
                .value7(yes7.getValue2()).value8(yes7.getValue3()).value9(yes7.getValue4()).value10(yes7.getValue5())
                .value11(yes7.getValue6()).value12(yes7.getValue7()).value13(yes7.getValue8()).value14(yes7.getValue9())
                .value15(yes7.getValue10()).value16(yes7.getValue11()).value17(yes7.getValue12()).value18(yes7.getValue13()).id(yes7.getId()).build());

        TideLevelYearDO yes8 = tideLevelYearDOS.get(8);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("平均潮位(m)").value6(yes8.getValue1())
                .value7(yes8.getValue2()).value8(yes8.getValue3()).value9(yes8.getValue4()).value10(yes8.getValue5())
                .value11(yes8.getValue6()).value12(yes8.getValue7()).value13(yes8.getValue8()).value14(yes8.getValue9())
                .value15(yes8.getValue10()).value16(yes8.getValue11()).value17(yes8.getValue12()).value18(yes8.getValue13()).id(yes8.getId()).build());

        TideLevelYearDO yes9 = tideLevelYearDOS.get(9);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value2("低潮").value3("最高").value4("潮位(m)").value6(yes9.getValue1())
                .value7(yes9.getValue2()).value8(yes9.getValue3()).value9(yes9.getValue4()).value10(yes9.getValue5())
                .value11(yes9.getValue6()).value12(yes9.getValue7()).value13(yes9.getValue8()).value14(yes9.getValue9())
                .value15(yes9.getValue10()).value16(yes9.getValue11()).value17(yes9.getValue12()).value18(yes9.getValue13()).id(yes9.getId()).build());

        TideLevelYearDO yes10 = tideLevelYearDOS.get(10);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes10.getValue1())
                .value7(yes10.getValue2()).value8(yes10.getValue3()).value9(yes10.getValue4()).value10(yes10.getValue5())
                .value11(yes10.getValue6()).value12(yes10.getValue7()).value13(yes10.getValue8()).value14(yes10.getValue9())
                .value15(yes10.getValue10()).value16(yes10.getValue11()).value17(yes10.getValue12()).value18(yes10.getValue13()).id(yes10.getId()).build());

        TideLevelYearDO yes11 = tideLevelYearDOS.get(11);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("公历:时分").value6(yes11.getValue1())
                .value7(yes11.getValue2()).value8(yes11.getValue3()).value9(yes11.getValue4()).value10(yes11.getValue5())
                .value11(yes11.getValue6()).value12(yes11.getValue7()).value13(yes11.getValue8()).value14(yes11.getValue9())
                .value15(yes11.getValue10()).value16(yes11.getValue11()).value17(yes11.getValue12()).value18(yes11.getValue13()).id(yes11.getId()).build());

        TideLevelYearDO yes12 = tideLevelYearDOS.get(12);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:日-月").value6(yes12.getValue1())
                .value7(yes12.getValue2()).value8(yes12.getValue3()).value9(yes12.getValue4()).value10(yes12.getValue5())
                .value11(yes12.getValue6()).value12(yes12.getValue7()).value13(yes12.getValue8()).value14(yes12.getValue9())
                .value15(yes12.getValue10()).value16(yes12.getValue11()).value17(yes12.getValue12()).value18(yes12.getValue13()).id(yes12.getId()).build());

        TideLevelYearDO yes13 = tideLevelYearDOS.get(13);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("最低").value4("潮位(m)").value6(yes13.getValue1())
                .value7(yes13.getValue2()).value8(yes13.getValue3()).value9(yes13.getValue4()).value10(yes13.getValue5())
                .value11(yes13.getValue6()).value12(yes13.getValue7()).value13(yes13.getValue8()).value14(yes13.getValue9())
                .value15(yes13.getValue10()).value16(yes13.getValue11()).value17(yes13.getValue12()).value18(yes13.getValue13()).id(yes13.getId()).build());

        TideLevelYearDO yes14 = tideLevelYearDOS.get(14);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes14.getValue1())
                .value7(yes14.getValue2()).value8(yes14.getValue3()).value9(yes14.getValue4()).value10(yes14.getValue5())
                .value11(yes14.getValue6()).value12(yes14.getValue7()).value13(yes14.getValue8()).value14(yes14.getValue9())
                .value15(yes14.getValue10()).value16(yes14.getValue11()).value17(yes14.getValue12()).value18(yes14.getValue13()).id(yes14.getId()).build());

        TideLevelYearDO yes15 = tideLevelYearDOS.get(15);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("公历:时分").value6(yes15.getValue1())
                .value7(yes15.getValue2()).value8(yes15.getValue3()).value9(yes15.getValue4()).value10(yes15.getValue5())
                .value11(yes15.getValue6()).value12(yes15.getValue7()).value13(yes15.getValue8()).value14(yes15.getValue9())
                .value15(yes15.getValue10()).value16(yes15.getValue11()).value17(yes15.getValue12()).value18(yes15.getValue13()).id(yes15.getId()).build());

        TideLevelYearDO yes16 = tideLevelYearDOS.get(16);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes16.getValue1())
                .value7(yes16.getValue2()).value8(yes16.getValue3()).value9(yes16.getValue4()).value10(yes16.getValue5())
                .value11(yes16.getValue6()).value12(yes16.getValue7()).value13(yes16.getValue8()).value14(yes16.getValue9())
                .value15(yes16.getValue10()).value16(yes16.getValue11()).value17(yes16.getValue12()).value18(yes16.getValue13()).id(yes16.getId()).build());

        TideLevelYearDO yes17 = tideLevelYearDOS.get(17);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("平均潮位(m)").value6(yes17.getValue1())
                .value7(yes17.getValue2()).value8(yes17.getValue3()).value9(yes17.getValue4()).value10(yes17.getValue5())
                .value11(yes17.getValue6()).value12(yes17.getValue7()).value13(yes17.getValue8()).value14(yes17.getValue9())
                .value15(yes17.getValue10()).value16(yes17.getValue11()).value17(yes17.getValue12()).value18(yes17.getValue13()).id(yes17.getId()).build());

//        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value1("潮位").value2("高潮").value3("最高").value4("潮位").value5("").value6("").value7("").value8("")
//                .value9("").value10("").value11("").value12("").value13("").value14("").value15("")
//                .value16("").value17("").value18("").build());
    }

    private void assemblyTableTidalRange(List<TideLevelYearForm> tideLevelYearForms, List<TideLevelYearDO> tideLevelYearDOS) {
        //====================组装潮差=====================
        TideLevelYearDO yes0 = tideLevelYearDOS.get(18);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value1("潮差").value2("涨潮").value3("最高").value4("潮差(m)").value6(yes0.getValue1())
                .value7(yes0.getValue2()).value8(yes0.getValue3()).value9(yes0.getValue4()).value10(yes0.getValue5())
                .value11(yes0.getValue6()).value12(yes0.getValue7()).value13(yes0.getValue8()).value14(yes0.getValue9())
                .value15(yes0.getValue10()).value16(yes0.getValue11()).value17(yes0.getValue12()).value18(yes0.getValue13()).id(yes0.getId()).build());

        TideLevelYearDO yes1 = tideLevelYearDOS.get(19);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes1.getValue1())
                .value7(yes1.getValue2()).value8(yes1.getValue3()).value9(yes1.getValue4()).value10(yes1.getValue5())
                .value11(yes1.getValue6()).value12(yes1.getValue7()).value13(yes1.getValue8()).value14(yes1.getValue9())
                .value15(yes1.getValue10()).value16(yes1.getValue11()).value17(yes1.getValue12()).value18(yes1.getValue13()).id(yes1.getId()).build());

        TideLevelYearDO yes3 = tideLevelYearDOS.get(20);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes3.getValue1())
                .value7(yes3.getValue2()).value8(yes3.getValue3()).value9(yes3.getValue4()).value10(yes3.getValue5())
                .value11(yes3.getValue6()).value12(yes3.getValue7()).value13(yes3.getValue8()).value14(yes3.getValue9())
                .value15(yes3.getValue10()).value16(yes3.getValue11()).value17(yes3.getValue12()).value18(yes3.getValue13()).id(yes3.getId()).build());

        TideLevelYearDO yes4 = tideLevelYearDOS.get(21);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("最低").value4("潮差(m)").value6(yes4.getValue1())
                .value7(yes4.getValue2()).value8(yes4.getValue3()).value9(yes4.getValue4()).value10(yes4.getValue5())
                .value11(yes4.getValue6()).value12(yes4.getValue7()).value13(yes4.getValue8()).value14(yes4.getValue9())
                .value15(yes4.getValue10()).value16(yes4.getValue11()).value17(yes4.getValue12()).value18(yes4.getValue13()).id(yes4.getId()).build());

        TideLevelYearDO yes5 = tideLevelYearDOS.get(22);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes5.getValue1())
                .value7(yes5.getValue2()).value8(yes5.getValue3()).value9(yes5.getValue4()).value10(yes5.getValue5())
                .value11(yes5.getValue6()).value12(yes5.getValue7()).value13(yes5.getValue8()).value14(yes5.getValue9())
                .value15(yes5.getValue10()).value16(yes5.getValue11()).value17(yes5.getValue12()).value18(yes5.getValue13()).id(yes5.getId()).build());

        TideLevelYearDO yes7 = tideLevelYearDOS.get(23);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes7.getValue1())
                .value7(yes7.getValue2()).value8(yes7.getValue3()).value9(yes7.getValue4()).value10(yes7.getValue5())
                .value11(yes7.getValue6()).value12(yes7.getValue7()).value13(yes7.getValue8()).value14(yes7.getValue9())
                .value15(yes7.getValue10()).value16(yes7.getValue11()).value17(yes7.getValue12()).value18(yes7.getValue13()).id(yes7.getId()).build());

        TideLevelYearDO yes8 = tideLevelYearDOS.get(24);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("平均潮差(m)").value6(yes8.getValue1())
                .value7(yes8.getValue2()).value8(yes8.getValue3()).value9(yes8.getValue4()).value10(yes8.getValue5())
                .value11(yes8.getValue6()).value12(yes8.getValue7()).value13(yes8.getValue8()).value14(yes8.getValue9())
                .value15(yes8.getValue10()).value16(yes8.getValue11()).value17(yes8.getValue12()).value18(yes8.getValue13()).id(yes8.getId()).build());

        TideLevelYearDO yes9 = tideLevelYearDOS.get(25);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value2("落潮").value3("最高").value4("潮差(m)").value6(yes9.getValue1())
                .value7(yes9.getValue2()).value8(yes9.getValue3()).value9(yes9.getValue4()).value10(yes9.getValue5())
                .value11(yes9.getValue6()).value12(yes9.getValue7()).value13(yes9.getValue8()).value14(yes9.getValue9())
                .value15(yes9.getValue10()).value16(yes9.getValue11()).value17(yes9.getValue12()).value18(yes9.getValue13()).id(yes9.getId()).build());

        TideLevelYearDO yes10 = tideLevelYearDOS.get(26);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes10.getValue1())
                .value7(yes10.getValue2()).value8(yes10.getValue3()).value9(yes10.getValue4()).value10(yes10.getValue5())
                .value11(yes10.getValue6()).value12(yes10.getValue7()).value13(yes10.getValue8()).value14(yes10.getValue9())
                .value15(yes10.getValue10()).value16(yes10.getValue11()).value17(yes10.getValue12()).value18(yes10.getValue13()).id(yes10.getId()).build());

        TideLevelYearDO yes12 = tideLevelYearDOS.get(27);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes12.getValue1())
                .value7(yes12.getValue2()).value8(yes12.getValue3()).value9(yes12.getValue4()).value10(yes12.getValue5())
                .value11(yes12.getValue6()).value12(yes12.getValue7()).value13(yes12.getValue8()).value14(yes12.getValue9())
                .value15(yes12.getValue10()).value16(yes12.getValue11()).value17(yes12.getValue12()).value18(yes12.getValue13()).id(yes12.getId()).build());

        TideLevelYearDO yes13 = tideLevelYearDOS.get(28);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("最低").value4("潮差(m)").value6(yes13.getValue1())
                .value7(yes13.getValue2()).value8(yes13.getValue3()).value9(yes13.getValue4()).value10(yes13.getValue5())
                .value11(yes13.getValue6()).value12(yes13.getValue7()).value13(yes13.getValue8()).value14(yes13.getValue9())
                .value15(yes13.getValue10()).value16(yes13.getValue11()).value17(yes13.getValue12()).value18(yes13.getValue13()).id(yes13.getId()).build());

        TideLevelYearDO yes14 = tideLevelYearDOS.get(29);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes14.getValue1())
                .value7(yes14.getValue2()).value8(yes14.getValue3()).value9(yes14.getValue4()).value10(yes14.getValue5())
                .value11(yes14.getValue6()).value12(yes14.getValue7()).value13(yes14.getValue8()).value14(yes14.getValue9())
                .value15(yes14.getValue10()).value16(yes14.getValue11()).value17(yes14.getValue12()).value18(yes14.getValue13()).id(yes14.getId()).build());

        TideLevelYearDO yes16 = tideLevelYearDOS.get(30);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes16.getValue1())
                .value7(yes16.getValue2()).value8(yes16.getValue3()).value9(yes16.getValue4()).value10(yes16.getValue5())
                .value11(yes16.getValue6()).value12(yes16.getValue7()).value13(yes16.getValue8()).value14(yes16.getValue9())
                .value15(yes16.getValue10()).value16(yes16.getValue11()).value17(yes16.getValue12()).value18(yes16.getValue13()).id(yes16.getId()).build());

        TideLevelYearDO yes17 = tideLevelYearDOS.get(31);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("平均潮差(m)").value6(yes17.getValue1())
                .value7(yes17.getValue2()).value8(yes17.getValue3()).value9(yes17.getValue4()).value10(yes17.getValue5())
                .value11(yes17.getValue6()).value12(yes17.getValue7()).value13(yes17.getValue8()).value14(yes17.getValue9())
                .value15(yes17.getValue10()).value16(yes17.getValue11()).value17(yes17.getValue12()).value18(yes17.getValue13()).id(yes17.getId()).build());
    }

    private void assemblyTableData(List<TideLevelYearForm> tideLevelYearForms, List<TideLevelYearDO> tideLevelYearDOS) {
        //====================组装历时间=====================
        TideLevelYearDO yes0 = tideLevelYearDOS.get(32);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value1("历时").value2("涨潮").value3("最大").value4("历时(时分)").value6(yes0.getValue1())
                .value7(yes0.getValue2()).value8(yes0.getValue3()).value9(yes0.getValue4()).value10(yes0.getValue5())
                .value11(yes0.getValue6()).value12(yes0.getValue7()).value13(yes0.getValue8()).value14(yes0.getValue9())
                .value15(yes0.getValue10()).value16(yes0.getValue11()).value17(yes0.getValue12()).value18(yes0.getValue13()).id(yes0.getId()).build());

        TideLevelYearDO yes1 = tideLevelYearDOS.get(33);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes1.getValue1())
                .value7(yes1.getValue2()).value8(yes1.getValue3()).value9(yes1.getValue4()).value10(yes1.getValue5())
                .value11(yes1.getValue6()).value12(yes1.getValue7()).value13(yes1.getValue8()).value14(yes1.getValue9())
                .value15(yes1.getValue10()).value16(yes1.getValue11()).value17(yes1.getValue12()).value18(yes1.getValue13()).id(yes1.getId()).build());

        TideLevelYearDO yes3 = tideLevelYearDOS.get(34);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes3.getValue1())
                .value7(yes3.getValue2()).value8(yes3.getValue3()).value9(yes3.getValue4()).value10(yes3.getValue5())
                .value11(yes3.getValue6()).value12(yes3.getValue7()).value13(yes3.getValue8()).value14(yes3.getValue9())
                .value15(yes3.getValue10()).value16(yes3.getValue11()).value17(yes3.getValue12()).value18(yes3.getValue13()).id(yes3.getId()).build());

        TideLevelYearDO yes4 = tideLevelYearDOS.get(35);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("最小").value4("历时(时分)").value6(yes4.getValue1())
                .value7(yes4.getValue2()).value8(yes4.getValue3()).value9(yes4.getValue4()).value10(yes4.getValue5())
                .value11(yes4.getValue6()).value12(yes4.getValue7()).value13(yes4.getValue8()).value14(yes4.getValue9())
                .value15(yes4.getValue10()).value16(yes4.getValue11()).value17(yes4.getValue12()).value18(yes4.getValue13()).id(yes4.getId()).build());

        TideLevelYearDO yes5 = tideLevelYearDOS.get(36);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes5.getValue1())
                .value7(yes5.getValue2()).value8(yes5.getValue3()).value9(yes5.getValue4()).value10(yes5.getValue5())
                .value11(yes5.getValue6()).value12(yes5.getValue7()).value13(yes5.getValue8()).value14(yes5.getValue9())
                .value15(yes5.getValue10()).value16(yes5.getValue11()).value17(yes5.getValue12()).value18(yes5.getValue13()).id(yes5.getId()).build());

        TideLevelYearDO yes7 = tideLevelYearDOS.get(37);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes7.getValue1())
                .value7(yes7.getValue2()).value8(yes7.getValue3()).value9(yes7.getValue4()).value10(yes7.getValue5())
                .value11(yes7.getValue6()).value12(yes7.getValue7()).value13(yes7.getValue8()).value14(yes7.getValue9())
                .value15(yes7.getValue10()).value16(yes7.getValue11()).value17(yes7.getValue12()).value18(yes7.getValue13()).id(yes7.getId()).build());

        TideLevelYearDO yes8 = tideLevelYearDOS.get(38);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("平均历时(时分)").value6(yes8.getValue1())
                .value7(yes8.getValue2()).value8(yes8.getValue3()).value9(yes8.getValue4()).value10(yes8.getValue5())
                .value11(yes8.getValue6()).value12(yes8.getValue7()).value13(yes8.getValue8()).value14(yes8.getValue9())
                .value15(yes8.getValue10()).value16(yes8.getValue11()).value17(yes8.getValue12()).value18(yes8.getValue13()).id(yes8.getId()).build());

        TideLevelYearDO yes9 = tideLevelYearDOS.get(39);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value2("落潮").value3("最大").value4("历时(时分)").value6(yes9.getValue1())
                .value7(yes9.getValue2()).value8(yes9.getValue3()).value9(yes9.getValue4()).value10(yes9.getValue5())
                .value11(yes9.getValue6()).value12(yes9.getValue7()).value13(yes9.getValue8()).value14(yes9.getValue9())
                .value15(yes9.getValue10()).value16(yes9.getValue11()).value17(yes9.getValue12()).value18(yes9.getValue13()).id(yes9.getId()).build());

        TideLevelYearDO yes10 = tideLevelYearDOS.get(40);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes10.getValue1())
                .value7(yes10.getValue2()).value8(yes10.getValue3()).value9(yes10.getValue4()).value10(yes10.getValue5())
                .value11(yes10.getValue6()).value12(yes10.getValue7()).value13(yes10.getValue8()).value14(yes10.getValue9())
                .value15(yes10.getValue10()).value16(yes10.getValue11()).value17(yes10.getValue12()).value18(yes10.getValue13()).id(yes10.getId()).build());

        TideLevelYearDO yes12 = tideLevelYearDOS.get(41);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes12.getValue1())
                .value7(yes12.getValue2()).value8(yes12.getValue3()).value9(yes12.getValue4()).value10(yes12.getValue5())
                .value11(yes12.getValue6()).value12(yes12.getValue7()).value13(yes12.getValue8()).value14(yes12.getValue9())
                .value15(yes12.getValue10()).value16(yes12.getValue11()).value17(yes12.getValue12()).value18(yes12.getValue13()).id(yes12.getId()).build());

        TideLevelYearDO yes13 = tideLevelYearDOS.get(42);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("最小").value4("历时(时分)").value6(yes13.getValue1())
                .value7(yes13.getValue2()).value8(yes13.getValue3()).value9(yes13.getValue4()).value10(yes13.getValue5())
                .value11(yes13.getValue6()).value12(yes13.getValue7()).value13(yes13.getValue8()).value14(yes13.getValue9())
                .value15(yes13.getValue10()).value16(yes13.getValue11()).value17(yes13.getValue12()).value18(yes13.getValue13()).id(yes13.getId()).build());

        TideLevelYearDO yes14 = tideLevelYearDOS.get(43);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value4("日期").value5("公历:日").value6(yes14.getValue1())
                .value7(yes14.getValue2()).value8(yes14.getValue3()).value9(yes14.getValue4()).value10(yes14.getValue5())
                .value11(yes14.getValue6()).value12(yes14.getValue7()).value13(yes14.getValue8()).value14(yes14.getValue9())
                .value15(yes14.getValue10()).value16(yes14.getValue11()).value17(yes14.getValue12()).value18(yes14.getValue13()).id(yes14.getId()).build());

        TideLevelYearDO yes16 = tideLevelYearDOS.get(44);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value5("农历:月-日").value6(yes16.getValue1())
                .value7(yes16.getValue2()).value8(yes16.getValue3()).value9(yes16.getValue4()).value10(yes16.getValue5())
                .value11(yes16.getValue6()).value12(yes16.getValue7()).value13(yes16.getValue8()).value14(yes16.getValue9())
                .value15(yes16.getValue10()).value16(yes16.getValue11()).value17(yes16.getValue12()).value18(yes16.getValue13()).id(yes16.getId()).build());

        TideLevelYearDO yes17 = tideLevelYearDOS.get(45);
        tideLevelYearForms.add(TideLevelYearForm.builder().remark(yes0.getRemark()).value3("平均历时(时分)").value6(yes17.getValue1())
                .value7(yes17.getValue2()).value8(yes17.getValue3()).value9(yes17.getValue4()).value10(yes17.getValue5())
                .value11(yes17.getValue6()).value12(yes17.getValue7()).value13(yes17.getValue8()).value14(yes17.getValue9())
                .value15(yes17.getValue10()).value16(yes17.getValue11()).value17(yes17.getValue12()).value18(yes17.getValue13()).id(yes17.getId()).build());
    }

    private void assemblyRemark(List<TideLevelYearForm> tideLevelYearForms, List<TideLevelYearDO> tideLevelYearDOS) {
        TideLevelYearDO tideLevelYearDO = tideLevelYearDOS.get(tideLevelYearDOS.size()-1);
        String remark = tideLevelYearDO.getRemark();
        tideLevelYearForms.add(TideLevelYearForm.builder().value1("附注").value6(remark).build());
    }
}