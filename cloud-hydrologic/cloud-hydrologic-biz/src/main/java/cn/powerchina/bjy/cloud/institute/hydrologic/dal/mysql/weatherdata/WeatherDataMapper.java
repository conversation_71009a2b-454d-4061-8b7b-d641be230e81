package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.weatherdata;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherdata.vo.WeatherDataPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth.StationMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.velocitywindresult.VelocityWindResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.weatherdata.WeatherDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 气象站-气象资料统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WeatherDataMapper extends BaseMapperX<WeatherDataDO> {

    default PageResult<WeatherDataDO> selectPage(WeatherDataPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WeatherDataDO>()
                .eqIfPresent(WeatherDataDO::getStationId, reqVO.getWeatherStationId())
                .eqIfPresent(WeatherDataDO::getMonth, reqVO.getMonth())
                .eqIfPresent(WeatherDataDO::getTemperatureAverageValue, reqVO.getTemperatureAverageValue())
                .eqIfPresent(WeatherDataDO::getTemperatureMaxExtremum, reqVO.getTemperatureMaxExtremum())
                .betweenIfPresent(WeatherDataDO::getTemperatureMaxDate, reqVO.getTemperatureMaxDate())
                .eqIfPresent(WeatherDataDO::getTemperatureMaxYear, reqVO.getTemperatureMaxYear())
                .eqIfPresent(WeatherDataDO::getTemperatureMinExtremum, reqVO.getTemperatureMinExtremum())
                .betweenIfPresent(WeatherDataDO::getTemperatureMinDate, reqVO.getTemperatureMinDate())
                .eqIfPresent(WeatherDataDO::getTemperatureMinYear, reqVO.getTemperatureMinYear())
                .eqIfPresent(WeatherDataDO::getPrecipitationAverageValue, reqVO.getPrecipitationAverageValue())
                .eqIfPresent(WeatherDataDO::getPrecipitationMaxValue, reqVO.getPrecipitationMaxValue())
                .eqIfPresent(WeatherDataDO::getPrecipitation01Days, reqVO.getPrecipitation01Days())
                .eqIfPresent(WeatherDataDO::getPrecipitation05Days, reqVO.getPrecipitation05Days())
                .eqIfPresent(WeatherDataDO::getPrecipitation2Days, reqVO.getPrecipitation2Days())
                .eqIfPresent(WeatherDataDO::getPrecipitation5Days, reqVO.getPrecipitation5Days())
                .eqIfPresent(WeatherDataDO::getPrecipitation10Days, reqVO.getPrecipitation10Days())
                .eqIfPresent(WeatherDataDO::getPrecipitation25Days, reqVO.getPrecipitation25Days())
                .eqIfPresent(WeatherDataDO::getPrecipitation30Days, reqVO.getPrecipitation30Days())
                .eqIfPresent(WeatherDataDO::getPrecipitation50Days, reqVO.getPrecipitation50Days())
                .eqIfPresent(WeatherDataDO::getWindLeadDirection, reqVO.getWindLeadDirection())
                .eqIfPresent(WeatherDataDO::getWindLeadDirectionFrequency, reqVO.getWindLeadDirectionFrequency())
                .eqIfPresent(WeatherDataDO::getWindAverageValue, reqVO.getWindAverageValue())
                .eqIfPresent(WeatherDataDO::getWindMaxExtremum, reqVO.getWindMaxExtremum())
                .betweenIfPresent(WeatherDataDO::getWindMaxDate, reqVO.getWindMaxDate())
                .eqIfPresent(WeatherDataDO::getWindMaxYear, reqVO.getWindMaxYear())
                .eqIfPresent(WeatherDataDO::getWindMaxDirection, reqVO.getWindMaxDirection())
                .eqIfPresent(WeatherDataDO::getWindDays5, reqVO.getWindDays5())
                .eqIfPresent(WeatherDataDO::getWindDays10, reqVO.getWindDays10())
                .eqIfPresent(WeatherDataDO::getWindDays12, reqVO.getWindDays12())
                .eqIfPresent(WeatherDataDO::getWindDays15, reqVO.getWindDays15())
                .eqIfPresent(WeatherDataDO::getGroundTemAverageValue, reqVO.getGroundTemAverageValue())
                .eqIfPresent(WeatherDataDO::getGroundTemMaxExtremum, reqVO.getGroundTemMaxExtremum())
                .betweenIfPresent(WeatherDataDO::getGroundTemMaxDate, reqVO.getGroundTemMaxDate())
                .eqIfPresent(WeatherDataDO::getGroundTemMaxYear, reqVO.getGroundTemMaxYear())
                .eqIfPresent(WeatherDataDO::getGroundTemMinExtremum, reqVO.getGroundTemMinExtremum())
                .betweenIfPresent(WeatherDataDO::getGroundTemMinDate, reqVO.getGroundTemMinDate())
                .eqIfPresent(WeatherDataDO::getGroundTemMinYear, reqVO.getGroundTemMinYear())
                .eqIfPresent(WeatherDataDO::getHumidityAverageValue, reqVO.getHumidityAverageValue())
                .eqIfPresent(WeatherDataDO::getHumidityDayMin, reqVO.getHumidityDayMin())
                .eqIfPresent(WeatherDataDO::getSnowDepthMaxExtremum, reqVO.getSnowDepthMaxExtremum())
                .betweenIfPresent(WeatherDataDO::getSnowDepthMaxDate, reqVO.getSnowDepthMaxDate())
                .eqIfPresent(WeatherDataDO::getSnowDepthMaxYear, reqVO.getSnowDepthMaxYear())
                .eqIfPresent(WeatherDataDO::getFrozenDepthMaxExtremum, reqVO.getFrozenDepthMaxExtremum())
                .betweenIfPresent(WeatherDataDO::getFrozenDepthMaxDate, reqVO.getFrozenDepthMaxDate())
                .eqIfPresent(WeatherDataDO::getFrozenDepthMaxYear, reqVO.getFrozenDepthMaxYear())
                .eqIfPresent(WeatherDataDO::getAverageAirPressure, reqVO.getAverageAirPressure())
                .eqIfPresent(WeatherDataDO::getSunlightHours, reqVO.getSunlightHours())
                .eqIfPresent(WeatherDataDO::getNumberOfFoggyDays, reqVO.getNumberOfFoggyDays())
                .eqIfPresent(WeatherDataDO::getNumberOfHailDays, reqVO.getNumberOfHailDays())
                .eqIfPresent(WeatherDataDO::getNumberOfThunderstormDays, reqVO.getNumberOfThunderstormDays())
                .eqIfPresent(WeatherDataDO::getNumberOfSnowfallDays, reqVO.getNumberOfSnowfallDays())
                .eqIfPresent(WeatherDataDO::getNumberOfSnowCoveredDays, reqVO.getNumberOfSnowCoveredDays())
                .eqIfPresent(WeatherDataDO::getEvaporationCapacity, reqVO.getEvaporationCapacity())
                .eqIfPresent(WeatherDataDO::getVersion, reqVO.getVersion())
                .eqIfPresent(WeatherDataDO::getLatest, reqVO.getLatest())
                .betweenIfPresent(WeatherDataDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WeatherDataDO::getId));
    }

    @Select("SELECT id FROM hydrologic_weather_data WHERE station_id = #{stationId} AND deleted = 0")
    List<Long> selectByStationId(@Param("stationId") Long stationId);

    /**
     * 查找最大版本
     *
     * @param stationId
     * @return
     */
    @Select("select max(version) from hydrologic_weather_data where deleted=0 and station_id = #{stationId}")
    Integer selectMaxVersion(@Param("stationId") Long stationId);

    /**
     * 更新历史版本
     *
     * @param stationId
     * @param latest
     */
    @Update("update hydrologic_weather_data set latest = #{latest} where station_id = #{stationId} and latest!=#{latest}")
    void updateHistoryVersion(@Param("stationId") Long stationId, @Param("latest") Integer latest);

    List<WeatherDataDO> listByStationIds(@Param("stationIds") List<Long> stationIds);

    int insertList(@Param("list")List<WeatherDataDO> list);

    List<WeatherDataDO> selectListTemporaryData(@Param("stationId") Long stationId);

    void batchInsert(@Param("list") List<WeatherDataDO> list);

    void updateBatchById(@Param("list") List<WeatherDataDO> list);



    @Select("SELECT * FROM hydrologic_weather_data WHERE station_id = #{stationId} AND data_type = #{dataType} " +
            " AND version = #{version} AND latest = 0 AND deleted = 1")
    List<WeatherDataDO> selectHis(@Param("stationId") Long stationId,
                                         @Param("dataType") Long dataType,
                                         @Param("version") Integer version);
}