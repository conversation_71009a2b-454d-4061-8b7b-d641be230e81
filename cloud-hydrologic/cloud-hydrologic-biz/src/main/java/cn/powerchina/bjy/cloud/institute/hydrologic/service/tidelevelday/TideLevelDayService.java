package cn.powerchina.bjy.cloud.institute.hydrologic.service.tidelevelday;


import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TideLevelDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TideLevelDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TidelevelDayUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelday.TideLevelDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.TideLevelDayForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.YearForm;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 水文站—潮位日统计 Service 接口
 *
 * <AUTHOR>
 */
public interface TideLevelDayService {

    /**
     * 创建水文站—潮位日统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTideLevelDay(@Valid TideLevelDaySaveReqVO createReqVO);

    /**
     * 更新水文站—潮位日统计
     *
     * @param updateReqVO 更新信息
     */
    void updateTideLevelDay(@Valid TideLevelDaySaveReqVO updateReqVO);

    /**
     * 删除水文站—潮位日统计
     *
     * @param id 编号
     */
    void deleteTideLevelDay(Long id);

    /**
     * 获得水文站—潮位日统计
     *
     * @param id 编号
     * @return 水文站—潮位日统计
     */
    TideLevelDayDO getTideLevelDay(Long id);

    /**
     * 获得水文站—潮位日统计分页
     *
     * @param pageReqVO 分页查询
     * @return 水文站—潮位日统计分页
     */
    PageResult<TideLevelDayDO> getTideLevelDayPage(TideLevelDayPageReqVO pageReqVO);

    void impotDayExcel(Long stationId, Integer dataType, Integer stationType, MultipartFile file);

    List<TideLevelDayForm> getYearBookList(TideLevelDayPageReqVO reqVO);

    void exportTideLevelDayExcel(TideLevelDayPageReqVO pageReqVO, HttpServletResponse response);

    /**
     * 删除水文站—潮位日统计
     *
     * @param id 编号
     */
    void updateAndDeleteTideLevelDay(TidelevelDayUpdateReqVO tidelevelDayUpdateReqVO);
}
