package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationevaporationyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationyear.vo.StationEvaporationYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationyear.vo.StationEvaporationYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationevaporationyear.StationEvaporationYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.RainFallDataRainYearModel;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 蒸发-年水面蒸发量特征值 Service 接口
 *
 * <AUTHOR>
 */
public interface StationEvaporationYearService {

    /**
     * 创建蒸发-年水面蒸发量特征值
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createStationEvaporationYear(@Valid StationEvaporationYearSaveReqVO createReqVO);

    /**
     * 更新蒸发-年水面蒸发量特征值
     *
     * @param updateReqVO 更新信息
     */
    void updateStationEvaporationYear(@Valid StationEvaporationYearSaveReqVO updateReqVO);

    /**
     * 删除蒸发-年水面蒸发量特征值
     *
     * @param id 编号
     */
    void deleteStationEvaporationYear(Long id);

    /**
     * 获得蒸发-年水面蒸发量特征值
     *
     * @param id 编号
     * @return 蒸发-年水面蒸发量特征值
     */
    StationEvaporationYearDO getStationEvaporationYear(Long id);

    /**
     * 获得蒸发-年水面蒸发量特征值分页
     *
     * @param pageReqVO 分页查询
     * @return 蒸发-年水面蒸发量特征值分页
     */
    PageResult<StationEvaporationYearDO> getStationEvaporationYearPage(StationEvaporationYearPageReqVO pageReqVO);

    /**
     * 导入年水面蒸发量特征值
     *
     * @param stationId
     * @param stationType
     * @param file
     */
    void importStationEvaporateYearFeatureExcel(Long stationId, Integer stationType, MultipartFile file);

    /**
     * 查找蒸发年的下一版本
     *
     * @param stationId
     * @param stationType
     * @return
     */
    Integer findNextStationEvaporateYearVersion(Long stationId, Integer stationType);

    /**
     * 插入
     *
     * @param yearDOList
     */
    void insertStationEvaporateYearDO(List<StationEvaporationYearDO> yearDOList);

    /**
     * 根据版本查找
     *
     * @param stationId
     * @param stationType
     * @param version
     * @return
     */
    List<StationEvaporationYearDO> findStationEvaporateYearDOByVersion(Long stationId, Integer stationType, Integer version);

    /**
     * 年水面蒸发量-导入年鉴格式
     *
     * @param stationId
     * @param stationType
     * @param file
     */
    void importStationEvaporateYearBookExcel(Long stationId, Integer stationType, MultipartFile file);

    /**
     * 获取逐日水面蒸发量-年鉴格式
     *
     * @param stationId
     * @param stationType
     * @param logId
     * @return
     */
    List<RainFallDataRainYearModel> findStationYearBookList(Long stationId, Integer stationType, Long logId, Integer year);

    /**
     * 根据年和版本查找
     *
     * @param stationId
     * @param stationType
     * @param year
     * @param version
     * @return
     */
    StationEvaporationYearDO findStationEvaporateDOByVersionAndYear(Long stationId, Integer stationType, String year, Integer version);

    /**
     * 修改年鉴数据
     *
     * @param stationId
     * @param stationType
     * @param dataMode
     */
    void modifyStationYearBookList(Long stationId, Integer stationType, List<RainFallDataRainYearModel> dataMode);

    /**
     * 蒸发导出年特征值
     *
     * @param response
     * @param pageReqVO
     */
    void exportStationEvaporationYear(HttpServletResponse response, StationEvaporationYearPageReqVO pageReqVO);

    /**
     * @param response
     * @param stationId
     * @param stationType
     * @param logId
     * @param year
     */
    void exportStationEvaporationYearMark(HttpServletResponse response, Long stationId, Integer stationType, Long logId, Integer year);
}