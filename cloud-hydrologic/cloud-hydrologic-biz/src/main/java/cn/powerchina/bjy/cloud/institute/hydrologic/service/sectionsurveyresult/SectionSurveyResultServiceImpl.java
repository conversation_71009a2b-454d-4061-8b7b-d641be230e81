package cn.powerchina.bjy.cloud.institute.hydrologic.service.sectionsurveyresult;

import cn.hutool.core.collection.CollUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.common.util.spring.SpringUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveydetail.vo.SectionSurveyDetailSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyinfo.vo.SectionSurveyInfoPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyinfo.vo.SectionSurveyInfoSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.elementobservationszkzdresult.ElementObservationsZkzdResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sectionsurveydetail.SectionSurveyDetailDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sectionsurveyinfo.SectionSurveyInfoDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.sectionsurveydetail.SectionSurveyDetailMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.sectionsurveyinfo.SectionSurveyInfoMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationdatalog.StationDataLogMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.SectionSurveyImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.StationDataLogAddModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.YearForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.DataProcessor;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.RedisService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sectionsurveydetail.SectionSurveyDetailService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sectionsurveyinfo.SectionSurveyInfoService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationdatalog.StationDataLogService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticalinfo.StatisticalInfoService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.RedisUtils;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;


@Service
@Validated
@Slf4j
public class SectionSurveyResultServiceImpl implements SectionSurveyResultService {

    @Resource
    private SectionSurveyInfoMapper sectionSurveyInfoMapper;

    @Resource
    private StationDataLogMapper stationDataLogMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private SectionSurveyInfoService sectionSurveyInfoService;

    @Resource
    private SectionSurveyDetailService sectionSurveyDetailService;

    @Resource
    private StationDataLogService stationDataLogService;

    @Resource
    private StatisticalInfoService statisticalInfoService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private SectionSurveyDetailMapper sectionSurveyDetailMapper;

    /**
     * 获取实测大断面成果分页数据
     *
     * @param pageReqVO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult<SectionSurveyRespVO> getSectionSurveyPage(SectionSurveyPageReqVO pageReqVO) {
        //如果查询更新记录logId不为空
        Long logId = pageReqVO.getLogId();
        Integer dataType = pageReqVO.getDataType();
        Long stationId = pageReqVO.getStationId();
        Integer stationType = pageReqVO.getStationType();

        Long count = stationDataLogMapper.selectCount(new LambdaQueryWrapperX<StationDataLogDO>()
                .gtIfPresent(StationDataLogDO::getId, logId)
                .eq(StationDataLogDO::getStationId, stationId)
                .eq(StationDataLogDO::getDataType, dataType)
        );
        if (count == 0) {
            logId = null;
        }
        // 查询最新的
        if (Objects.nonNull(logId)) {
            StationDataLogDO stationDataLogDO = stationDataLogService.findById(pageReqVO.getLogId());
            if (Objects.isNull(stationDataLogDO)) {
                return PageResult.empty();
            }
            //历史数据
            Integer version = stationDataLogDO.getCurrentVersion();
            List<SectionSurveyInfoDO>  sectionSurveyInfoDOS = sectionSurveyInfoMapper.selectHis(stationId,dataType,stationType,version,Integer.valueOf(pageReqVO.getYear()));
//            List<SectionSurveyInfoDO> sectionSurveyInfoDOS = sectionSurveyInfoMapper.selectList(new LambdaQueryWrapperX<SectionSurveyInfoDO>()
//                    .eq(SectionSurveyInfoDO::getStationId, stationId)
//                    .eq(SectionSurveyInfoDO::getDataType, dataType)
//                    .eq(SectionSurveyInfoDO::getStationType, stationType)
//                    .eq(SectionSurveyInfoDO::getVersion, version)
//                    .eq(SectionSurveyInfoDO::getLatest, LatestEnum.HISTORY.getType())
//                    .eq(SectionSurveyInfoDO::getDeleted, false)
//            );
            // 将基本信息转换为响应VO

            List<SectionSurveyRespVO> bean = BeanUtils.toBean(sectionSurveyInfoDOS, SectionSurveyRespVO.class);
            return new PageResult<>(bean, (long) bean.size());

        } else {
            // 转换查询参数
            SectionSurveyInfoPageReqVO infoPageReqVO = new SectionSurveyInfoPageReqVO();
            infoPageReqVO.setStationId(pageReqVO.getStationId());
            infoPageReqVO.setDataType(pageReqVO.getDataType());
            infoPageReqVO.setYear(pageReqVO.getYear());
            infoPageReqVO.setSectionName(pageReqVO.getSectionName());
            infoPageReqVO.setWaterLevel(pageReqVO.getWaterLevel());
            if (pageReqVO.getSurveyDate() != null) {
                infoPageReqVO.setSurveyDate(pageReqVO.getSurveyDate());
            }

            // 1. 查询基本信息
            PageResult<SectionSurveyInfoDO> pageResult = sectionSurveyInfoService.getSectionSurveyInfoPage(infoPageReqVO);
            if (CollUtil.isEmpty(pageResult.getList())) {
                return PageResult.empty();
            }

            // 2. 转换并返回数据
            List<SectionSurveyRespVO> respVOList = new ArrayList<>();
            for (SectionSurveyInfoDO info : pageResult.getList()) {
                // 查询垂线明细数据
                List<SectionSurveyDetailDO> detailList = sectionSurveyDetailService.getDetailListByStationAndYear(info);

                // 将基本信息转换为响应VO
                SectionSurveyRespVO respVO = BeanUtils.toBean(info, SectionSurveyRespVO.class);

                // 如果有垂线明细数据，将第一条明细数据的字段值直接设置到respVO中
                if (CollUtil.isNotEmpty(detailList)) {
                    SectionSurveyDetailDO detail = detailList.get(0);
                    respVO.setVerticalNo(detail.getVerticalNo());
                    respVO.setStartDistance(detail.getStartDistance());
                    respVO.setRiverBedElevation(detail.getRiverbedElevation());
                }

                respVOList.add(respVO);
            }
            return new PageResult<>(respVOList, pageResult.getTotal());
        }
    }

    /**
     * 创建实测大断面成果
     *
     * @param createReqVO 创建信息
     * @return 成果ID
     */
    @Override
    public Long createSectionSurvey(SectionSurveySaveReqVO createReqVO) {
        // 1. 保存基本信息
        SectionSurveyInfoSaveReqVO infoSaveReqVO = new SectionSurveyInfoSaveReqVO();
        // 设置基本信息字段
        infoSaveReqVO.setStationId(createReqVO.getStationId());
        infoSaveReqVO.setStationType(createReqVO.getStationType());
        infoSaveReqVO.setDataType(createReqVO.getDataType());
        // 从 dataList 中获取第一条数据的基本信息
        if (!CollUtil.isEmpty(createReqVO.getDataList())) {
            SectionSurveyDataVO firstData = createReqVO.getDataList().get(0);
            infoSaveReqVO.setYear(firstData.getYear());
            infoSaveReqVO.setSurveyDate(firstData.getSurveyDateAsLocalDate());
            infoSaveReqVO.setSectionName(firstData.getSectionName());
            infoSaveReqVO.setWaterLevel(firstData.getWaterLevel());
            infoSaveReqVO.setPeriodType(firstData.getPeriodType());
            infoSaveReqVO.setRemark(firstData.getRemark());
        }
        Long infoId = sectionSurveyInfoService.createSectionSurveyInfo(infoSaveReqVO);

        // 2. 保存垂线明细数据
        if (CollUtil.isNotEmpty(createReqVO.getDetailList())) {
            for (SectionSurveyImportModel detail : createReqVO.getDetailList()) {
                // 创建保存请求对象
                SectionSurveyDetailSaveReqVO detailSaveReqVO = new SectionSurveyDetailSaveReqVO();
                // 设置关联字段
                detailSaveReqVO.setStationId(createReqVO.getStationId());
                detailSaveReqVO.setYear(detail.getYear());
                detailSaveReqVO.setStationType(createReqVO.getStationType());
                detailSaveReqVO.setDataType(createReqVO.getDataType());

                // 设置明细字段
                detailSaveReqVO.setVerticalNo(detail.getVerticalNo());
                // Double 转 BigDecimal
                detailSaveReqVO.setStartDistance(detail.getStartDistance() != null ?
                        BigDecimal.valueOf(detail.getStartDistance()) : null);
                detailSaveReqVO.setRiverbedElevation(detail.getRiverbedElevation() != null ?
                        BigDecimal.valueOf(detail.getRiverbedElevation()) : null);
                detailSaveReqVO.setRemark(detail.getRemark());

                sectionSurveyDetailService.createSectionSurveyDetail(detailSaveReqVO);
            }
        }

        return infoId;
    }
    /**
     * 根据数据类型确定汛期类型
     *
     * @param dataType 数据类型
     * @return 1-汛前，2-汛后
     */
    private Integer determinePeriodType(Integer dataType) {
        // 根据dataType判断periodType
        if (dataType == null) {
            throw exception(ErrorCodeConstants.POWER_DATA_ERROR, "数据类型不能为空");
        }

        switch (dataType) {
            case 220130:
                return 1; // 汛前
            case 220131:
                return 2; // 汛后
            default:
                log.error("无效的dataType值：{}", dataType);
                throw exception(ErrorCodeConstants.POWER_DATA_ERROR, "无效的数据类型");
        }
    }

    /**
     * 更新实测大断面成果
     *
     * @param updateReqVO 更新信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSectionSurvey(SectionSurveyUpdateReqVO updateReqVO) throws IOException{
        if (updateReqVO.getIsDelete() != null && updateReqVO.getIsDelete()) {
            // 构建删除请求对象
            SectionSurveyDeleteReqVO deleteReqVO = new SectionSurveyDeleteReqVO();
            deleteReqVO.setStationId(updateReqVO.getStationId());
            deleteReqVO.setStationType(updateReqVO.getStationType());
            deleteReqVO.setDataType(updateReqVO.getDataType());
            deleteReqVO.setYear(updateReqVO.getYear());
            deleteReqVO.setIsDelete(true);  // 设置为年删除

            // 直接调用删除方法
            deleteSectionSurvey(deleteReqVO);
            return;
        }

        // 1. 获取periodType
        Integer periodType = determinePeriodType(updateReqVO.getDataType());
        Long stationId = updateReqVO.getStationId();
        Integer stationType = updateReqVO.getStationType();
        Integer dataType = updateReqVO.getDataType();
        //历史数据
        List<SectionSurveyInfoDO>  hisdos = sectionSurveyInfoMapper.selectList(new LambdaQueryWrapperX<SectionSurveyInfoDO>()
                .eq(SectionSurveyInfoDO::getStationId, stationId)
                .eq(SectionSurveyInfoDO::getDataType, dataType)
                .eq(SectionSurveyInfoDO::getStationType, stationType)
//                .eq(GrainUnitResultDO::getYear, year)
                .eq(SectionSurveyInfoDO::getLatest, 1));
        if (hisdos.isEmpty()) {
            return;
        }
        // 1. 获取下一个版本号
        Integer nextVersion = stationDataLogService.findStationDataLogDOLatestCurrentVersionByStationType(stationId, stationType, dataType);
        // 删除数据库上一版本的所有数据
        sectionSurveyInfoMapper.update(new LambdaUpdateWrapper<SectionSurveyInfoDO>()
                .set(SectionSurveyInfoDO::getLatest, 0).set(SectionSurveyInfoDO::getDeleted, true)
                .eq(SectionSurveyInfoDO::getStationId, stationId)
                .eq(SectionSurveyInfoDO::getLatest, 1));
        // 删除数据库上一版本的所有数据
        sectionSurveyDetailMapper.update(new LambdaUpdateWrapper<SectionSurveyDetailDO>()
                .set(SectionSurveyDetailDO::getLatest, 0).set(SectionSurveyDetailDO::getDeleted, true)
                .eq(SectionSurveyDetailDO::getStationId, stationId)
                .eq(SectionSurveyDetailDO::getLatest, 1));
        try {
            // 2. 将原有数据的latest标志设置为0
            UpdateWrapper<SectionSurveyInfoDO> infoUpdateWrapper = new UpdateWrapper<>();
            infoUpdateWrapper.eq("station_id", stationId)
                    .eq("station_type", stationType)
                    .eq("data_type", dataType)
                    .eq("year", updateReqVO.getYear())
                    .eq("latest", LatestEnum.LATEST.getType())
                    .set("latest", LatestEnum.HISTORY.getType());
            sectionSurveyInfoService.update(infoUpdateWrapper);

            UpdateWrapper<SectionSurveyDetailDO> detailUpdateWrapper = new UpdateWrapper<>();
            detailUpdateWrapper.eq("station_id", stationId)
                    .eq("station_type", stationType)
                    .eq("data_type", dataType)
                    .eq("year", updateReqVO.getYear())
                    .eq("latest", LatestEnum.LATEST.getType())
                    .set("latest", LatestEnum.HISTORY.getType());
            sectionSurveyDetailService.update(detailUpdateWrapper);

            // 1. 解析remark
            String remark = null;
            if (updateReqVO.getDataList() != null) {
                for (YearForm item : updateReqVO.getDataList()) {
                    if ("附注".equals(item.getColumnName())) {
                        remark = item.getValue1();
                        break;
                    }
                }
            }

            // 3. 保存基本信息
            SectionSurveyInfoDO infoData = new SectionSurveyInfoDO();
            infoData.setStationId(stationId);
            infoData.setStationType(stationType);
            infoData.setDataType(dataType);
            infoData.setYear(updateReqVO.getYear());
            infoData.setSurveyDate(DateUtils.parseMonthDay(updateReqVO.getSurveyDate()));
            infoData.setSectionName(updateReqVO.getSectionName());
            infoData.setWaterLevel(updateReqVO.getWaterLevel());
            infoData.setRemark(remark);
            infoData.setLatest(LatestEnum.LATEST.getType());
            infoData.setPeriodType(periodType);  // 设置periodType
            infoData.setVersion(nextVersion +1);
            infoData.setCreator(WebFrameworkUtils.getLoginUserId().toString());
            infoData.setUpdater(WebFrameworkUtils.getLoginUserId().toString());
            sectionSurveyInfoService.save(infoData);

            // 校验垂线号
//            for (int i = 0; i < updateReqVO.getDataList().size(); i++) {
//                YearForm data = updateReqVO.getDataList().get(i);
//                if (data.getColumnName() == null) {
//                    throw exception(ErrorCodeConstants.EXCEL_VERTICAL_EMPTY_ERROR, i+6);
//                }
//            }
            // 5. 保存垂线明细数据
            for (YearForm data : updateReqVO.getDataList()) {
//                // 跳过空数据
//                if (data == null || StringUtils.isBlank(data.getColumnName())) {
//                    continue;
//                }
                // 先判断是否是附注行
                // 跳过附注行，附注信息已经在基本信息中保存
                if ("附注".equals(data.getColumnName())) {
                    continue;
                }
                String verticalNo = data.getColumnName();

                if (verticalNo == null || StringUtils.isBlank(verticalNo)) {
                    throw exception(ErrorCodeConstants.EXCEL_VERTICAL_EMPTY_ERRORTWO);
                }
                String riverBedElevation = data.getValue1();
                if (riverBedElevation == null || StringUtils.isBlank(riverBedElevation)) {
                    throw exception(ErrorCodeConstants.EXCEL_START_EMPTY_ERROR );
                }
                String startDistance = data.getColumnSecondName();
                if (startDistance == null || StringUtils.isBlank(startDistance)) {
                    throw exception(ErrorCodeConstants.EXCEL_WATER_EMPTY_ERROR );
                }
                SectionSurveyDetailDO detailDO = new SectionSurveyDetailDO();
                detailDO.setStationId(stationId);
                detailDO.setStationType(stationType);
                detailDO.setDataType(dataType);
                detailDO.setYear(updateReqVO.getYear());

                // 从YearForm中获取数据
                detailDO.setVerticalNo(data.getColumnName());  // 垂线号在columnName中

                // 附注行特殊处理
                detailDO.setStartDistance(BigDecimal.ZERO);        // 设置默认值
                detailDO.setRiverbedElevation(BigDecimal.ZERO);   // 设置默认值
                detailDO.setRemark(data.getValue1());           // 设置备注内容
                detailDO.setVerticalNo(data.getColumnName());
                detailDO.setStartDistance(new BigDecimal(data.getValue1())); // 起点距
                detailDO.setRiverbedElevation(new BigDecimal(data.getColumnSecondName())); // 河底高程

                detailDO.setLatest(LatestEnum.LATEST.getType());
                // 设置垂线号
                detailDO.setVerticalNo(data.getColumnName());
                detailDO.setVersion(nextVersion +1);
                detailDO.setPeriodType(periodType);  // 设置相同的periodType
                detailDO.setCreator(WebFrameworkUtils.getLoginUserId().toString());
                detailDO.setUpdater(WebFrameworkUtils.getLoginUserId().toString());
                sectionSurveyDetailService.save(detailDO);
            }
            // 5. 更新统计信息
            statisticalInfoService.updateIndexDataYearInfo(stationType, dataType, stationId);

            // 6. 添加数据日志记录
            StationDataLogAddModel logModel = new StationDataLogAddModel();
            logModel.setStationId(stationId);
            logModel.setDataType(dataType);
            logModel.setYear(String.valueOf(updateReqVO.getYear()));
            logModel.setCurrentVersion(nextVersion +1);
            stationDataLogService.addStationDataLog(logModel);
        } catch (ServiceException e) {
            // 如果是我们自定义的异常，直接往上抛
            throw e;
            } catch(Exception e){
                log.error("更新大断面基本信息失败", e);
                throw exception(ErrorCodeConstants.SECTION_SURVEY_UPDATE_ERROR);
            }
        }


    /**
     * 删除实测大断面成果
     *
     * @param id 成果ID
     */
    @Override
    public void deleteSectionSurvey(SectionSurveyDeleteReqVO deleteReqVO) {
            // 整表删除
            deleteSectionSurveyByYear(deleteReqVO.getYear(),
                    deleteReqVO.getStationId(),
                    deleteReqVO.getStationType(),
                    deleteReqVO.getDataType());

    }

    /**
     * 按年份删除实测大断面成果
     */
    private void deleteSectionSurveyByYear(Integer year, Long stationId, Integer stationType, Integer dataType) {
        // 1. 查询该年份下的所有基本信息记录
        SectionSurveyInfoPageReqVO pageReqVO = new SectionSurveyInfoPageReqVO();
        pageReqVO.setYear(year);
        pageReqVO.setStationId(stationId);
        pageReqVO.setDataType(dataType);
        List<SectionSurveyInfoDO> infoList = sectionSurveyInfoService.getSectionSurveyInfoList(pageReqVO);

        if (CollUtil.isEmpty(infoList)) {
            return;
        }
        // 2. 删除基本信息记录
        for (SectionSurveyInfoDO info : infoList) {
            // 保存当前的periodType
            Integer currentPeriodType = info.getPeriodType();
            sectionSurveyInfoService.deleteSectionSurveyInfo(info.getId());

            // 3. 删除关联的垂线明细数据
            LambdaQueryWrapper<SectionSurveyDetailDO> detailQueryWrapper = new LambdaQueryWrapper<>();
            detailQueryWrapper.eq(SectionSurveyDetailDO::getStationId, info.getStationId())
                    .eq(SectionSurveyDetailDO::getStationType, info.getStationType())
                    .eq(SectionSurveyDetailDO::getDataType, info.getDataType())
                    .eq(SectionSurveyDetailDO::getYear, info.getYear())
                    .eq(SectionSurveyDetailDO::getPeriodType, info.getPeriodType()); // 添加periodType条件
            List<SectionSurveyDetailDO> detailList = sectionSurveyDetailMapper.selectList(detailQueryWrapper);

            if (CollUtil.isNotEmpty(detailList)) {
                for (SectionSurveyDetailDO detail : detailList) {
                    // 只删除相同periodType的数据
                    if (detail.getPeriodType().equals(currentPeriodType)) {
                        sectionSurveyDetailService.deleteSectionSurveyDetail(detail.getId());
                    }
                }
            }
        }

        // 4. 使用专门的方法更新大断面统计信息
        updateSectionSurveyStatisticalInfo(stationType, stationId);
    }


    /**
     * 更新大断面成果表的统计信息
     */
    private void updateSectionSurveyStatisticalInfo(Integer stationType, Long stationId) {
        // 获取所有年份数据（包括汛前和汛后）
        List<Integer> listYear = new ArrayList<>();

        // 查询汛前数据
        List<Integer> preFloodYears = sectionSurveyDetailMapper.listYear(stationId, 220130);
        if (!CollectionUtils.isEmpty(preFloodYears)) {
            listYear.addAll(preFloodYears);
        }

        // 查询汛后数据
        List<Integer> postFloodYears = sectionSurveyDetailMapper.listYear(stationId, 220131);
        if (!CollectionUtils.isEmpty(postFloodYears)) {
            listYear.addAll(postFloodYears);
        }

        // 去重
        listYear = listYear.stream().distinct().collect(Collectors.toList());

        // 分别更新汛前和汛后的统计信息
        statisticalInfoService.updateIndexDataYearInfo(stationType, 220130, stationId);
        statisticalInfoService.updateIndexDataYearInfo(stationType, 220131, stationId);
    }

    /**
     * 获取实测大断面成果详情
     *
     * @param id 成果ID
     * @return 成果详情
     */
    @Override
    public SectionSurveyRespVO getSectionSurvey(Long id) {
        // 1. 查询基本信息
        SectionSurveyInfoDO info = sectionSurveyInfoService.getSectionSurveyInfo(id);
        if (info == null) {
            throw exception(ErrorCodeConstants.SECTION_SURVEY_NOT_EXISTS);
        }

        // 2. 查询垂线明细数据
        List<SectionSurveyDetailDO> detailList = sectionSurveyDetailService.getDetailListByStationAndYear(info);

        // 3. 组装返回数据 -
        SectionSurveyRespVO respVO = BeanUtils.toBean(info, SectionSurveyRespVO.class);

        // 如果有垂线明细数据，将第一条明细数据的字段值直接设置到respVO中
        if (CollUtil.isNotEmpty(detailList)) {
            SectionSurveyDetailDO detail = detailList.get(0);
            respVO.setVerticalNo(detail.getVerticalNo());
            respVO.setStartDistance(detail.getStartDistance());
            respVO.setRiverBedElevation(detail.getRiverbedElevation());
        }

        return respVO;
    }

    /**
     * 导出实测大断面成果Excel
     ** @param response  HTTP响应
     * @throws IOException IO异常
     */

//HttpServletResponse response, Long stationId, Integer dataType, String year,Long logId
    @Override
    public void exportSectionSurveyExcel(HttpServletResponse response, SectionSurveyExportPageReqVO exportReqVO) {
        try {
            // 1. 设置响应头
            // 根据dataType动态设置文件名和表头
            Integer dataType = exportReqVO.getDataType();
            String periodType = dataType == 220130 ? "汛前" : "汛后";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("实测大断面成果表(" + periodType + ")" + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);


            // 3. 获取最新版本数据
            if (exportReqVO.getLogId() != null) {
                // 根据logId获取版本号
                StationDataLogDO logDO = stationDataLogService.findById(exportReqVO.getLogId());
                if (logDO != null) {
                    exportReqVO.setVersion(logDO.getCurrentVersion());
                    exportReqVO.setLatest(null);
                }
            }else {
                exportReqVO.setLatest(LatestEnum.LATEST.getType());
                exportReqVO.setVersion(null);
            }

            // 2. 获取数据
            // 2. 获取数据
            SectionSurveyExportVO data = getSectionSurveyData(exportReqVO);
            if (data == null || CollectionUtils.isEmpty(data.getDataList())) {
                log.error("exportSectionSurvey--->没有数据，不导出。stationId={},logId={},year={}", exportReqVO.getStationId(), exportReqVO.getYear());
                return;
            }
//            // 1. 查询基本信息
//            SectionSurveyInfoDO info = sectionSurveyInfoService.getOne(
//                    new LambdaQueryWrapper<SectionSurveyInfoDO>()
//                            .eq(SectionSurveyInfoDO::getStationId, exportReqVO.getStationId())
//                            .eq(SectionSurveyInfoDO::getYear, Integer.valueOf(data.getYear()))
//                            .eq(SectionSurveyInfoDO::getDataType, dataType)
//                            .eq(SectionSurveyInfoDO::getLatest, 1)
//            );

            // 3. 准备填充数据
            Map<String, String> basicMap = new HashMap<>();
            basicMap.put("year", data.getYear());
            basicMap.put("surveyDate", data.getSurveyDate());
            basicMap.put("sectionName", data.getSectionName());
            basicMap.put("waterLevel", data.getWaterLevel()+ "m");


            // 4. 使用模板导出
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + StationDataTypeEnum.HYDROLOGIC_SECTION_SURVEY_DETAIL.getType() + PlanningDesignConstants.EXCEL_SUFFIX);

            // 5. 使用try-with-resources导出Excel
            try (InputStream templateStream = resource.getInputStream();
                 ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                         .withTemplate(templateStream)
                         .build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet(0).build();



                // 按顺序填充数据
                excelWriter.fill(basicMap, writeSheet);  // 填充基本信息
                List<SectionSurveyExportVO.VerticalLineData> dataList =data.getDataList();
                SectionSurveyExportVO.VerticalLineData ver=new SectionSurveyExportVO.VerticalLineData();
                ver.setVertical_no("附注");
                ver.setStart_distance(data.getRemark());
                dataList.add(ver);
                data.setDataList(dataList);
                excelWriter.fill(data.getDataList(), writeSheet);  // 填充垂线数据

            }

        } catch (Exception e) {
            log.error("exportSectionSurvey--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }

    private SectionSurveyExportVO getSectionSurveyData(SectionSurveyExportPageReqVO exportReqVO) {
        // 1. 查询有效的基本信息
        SectionSurveyInfoDO info = sectionSurveyInfoMapper.selectLatestValidInfo(
                exportReqVO.getStationId(),
                exportReqVO.getYear(),
                StationEnum.HYDROLOGIC.getType(),
                exportReqVO.getDataType(),
                exportReqVO.getVersion(),
                exportReqVO.getLatest()

        );

        if (info == null) {
            return null;
        }
        Integer periodType = determinePeriodType(exportReqVO.getDataType());

        // 2. 查询关联的垂线数据
        List<SectionSurveyDetailDO> details = sectionSurveyDetailMapper.getDetailListWithVersion(exportReqVO.getStationId(),
                exportReqVO.getYear(),
                StationEnum.HYDROLOGIC.getType(),
                exportReqVO.getDataType(), periodType,
                exportReqVO.getVersion(),
                exportReqVO.getLatest());

        // 3. 组装导出数据
        SectionSurveyExportVO exportVO = new SectionSurveyExportVO();
        exportVO.setYear(info.getYear() + "年");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M月d日");
        exportVO.setSurveyDate(info.getSurveyDate().format(formatter));
        exportVO.setSectionName(info.getSectionName());
        exportVO.setWaterLevel(info.getWaterLevel() + "");
        exportVO.setRemark(info.getRemark());

        // 转换垂线数据
        List<SectionSurveyExportVO.VerticalLineData> dataList = details.stream()
                .map(detail -> {
                    SectionSurveyExportVO.VerticalLineData data = new SectionSurveyExportVO.VerticalLineData();
                    data.setVertical_no(detail.getVerticalNo());
                    data.setStart_distance(detail.getStartDistance() + "");
                    data.setRiverbed_elevation(detail.getRiverbedElevation() + "");
                    data.setRemark(info.getRemark());
                    return data;
                })
                .collect(Collectors.toList());
        exportVO.setDataList(dataList);

        return exportVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importSectionSurveyExcel(MultipartFile file, Long stationId, Integer stationType,
                                         Integer dataType)  {
        // 1. 获取分布式锁
        RLock lock = redisService.acquireDistributedLock(
                RedisUtils.getLockKey(stationId, stationType, dataType)
        );

        try {
            // 2. 校验文件
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }

            // 3. 获取数据处理器
            DataProcessor dataProcessor = (DataProcessor) SpringUtils.getBean(
                    StationDataTypeEnumV2.fromCode(dataType).getImportModel()
            );

            // 4. 执行导入
            try {
                dataProcessor.importExcel(file, stationId, stationType, dataType);
            } catch (ServiceException se) {
                // 直接抛出业务异常
                throw se;
            } catch (IOException e) {
                // 记录日志并转换为业务异常
                log.error("importSectionSurveyExcel--->error.", e);
                throw exception(ErrorCodeConstants.POWER_DATA_ERROR, e.getMessage());
            }
        } finally {
            // 5. 释放锁
            if (lock != null) {
                lock.unlock();
            }
        }
    }

}
