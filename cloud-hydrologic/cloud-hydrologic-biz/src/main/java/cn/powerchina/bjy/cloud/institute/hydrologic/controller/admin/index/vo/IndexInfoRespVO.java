package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/7/9
 */
@Schema(description = "管理后台 - 首页信息列表 Response VO")
@Data
public class IndexInfoRespVO {

    @Schema(description = "搜索站点数量")
    private Integer selectNum;

    @Schema(description = "水文站信息")
    private List<HydrologicStation> HydrologicStationList;

    @Schema(description = "水文站信息")
    private List<HydrologicStation> SWHydrologicStationList;

    @Schema(description = "水文站信息")
    private List<HydrologicStation> SKHydrologicStationList;

    @Schema(description = "雨量站信息")
    private List<RainFallStation> rainFallStationList;

    @Schema(description = "气象站信息")
    private List<WeatherStation> weatherStationList;

    @Schema(description = "项目信息")
    private List<ProjectInfo> projectInfoList;

    @Schema(description = "资源站点信息")
    private List<ResourceSiteManagement> resourceSiteManagementList;

    @Schema(description = "国家数量")
    private List<CountryNumber> countryNumberList;

    @Schema(description = "省份数量")
    private List<CountryNumber> provinceNumberList;

    @Schema(description = "市数量")
    private List<CountryNumber> cityNumberList;


    @Schema(description = "管理后台 - 国家数量")
    @Data
    public static class CountryNumber {

        private String name;

        /**
         * 经度
         */
        private String longitude;
        /**
         * 纬度
         */
        private String latitude;

        private Integer number=0;

        private Integer swenumber=0;

        private Integer sknumber=0;

        private Integer swnumber=0;

        private Integer qxnumber=0;

        private Integer ylnumber=0;

        private Integer zynumber=0;

        private Integer xmnumber=0;
    }

    @Schema(description = "管理后台 - 水文站信息")
    @Data
    public static class HydrologicStation {

        @Schema(description = "站点类型")
        private String type;

        @Schema(description = "检索标识")
        private Integer indexNum;

        @Schema(description = "站点id")
        private Long stationId;

        @Schema(description = "站点名称")
        private String stationName;

        @Schema(description = "测站编码")
        private String hydrologicCode;

        @Schema(description = "水系")
        private String riverSystem;

        @Schema(description = "河名")
        private String riverName;

        @Schema(description = "断面地点")
        private String sectionAddress;

        @Schema(description = "经度")
        private String longitude;

        @Schema(description = "纬度")
        private String latitude;

        @Schema(description = "设立时间")
        private String establishDate;

        @Schema(description = "年限")
        private Integer dataAge;

        @Schema(description = "资料起止年份")
        private String dataYear;

        @Schema(description = "站别")
        private String hydrologicType;

    }

    @Schema(description = "管理后台 - 雨量站信息")
    @Data
    public static class RainFallStation {

        @Schema(description = "站点类型")
        private String type;

        @Schema(description = "检索标识")
        private Integer indexNum;

        @Schema(description = "站点id")
        private Long stationId;

        @Schema(description = "站点名称")
        private String stationName;

        @Schema(description = "测站编码")
        private String hydrologicCode;

        @Schema(description = "水系")
        private String riverSystem;

        @Schema(description = "河名")
        private String riverName;

        @Schema(description = "观测场地点")
        private String sectionAddress;

        @Schema(description = "经度")
        private String longitude;

        @Schema(description = "纬度")
        private String latitude;

        @Schema(description = "设立时间")
        private String establishDate;

        @Schema(description = "年限")
        private Integer dataAge;

        @Schema(description = "资料起止年份")
        private String dataYear;

    }

    @Schema(description = "管理后台 - 气象站信息")
    @Data
    public static class WeatherStation {

        @Schema(description = "站点类型")
        private String type;

        @Schema(description = "检索标识")
        private Integer indexNum;

        @Schema(description = "站点id")
        private Long stationId;

        @Schema(description = "站点名称")
        private String stationName;

        @Schema(description = "经度")
        private String longitude;

        @Schema(description = "纬度")
        private String latitude;

        @Schema(description = "海拔高度（米）")
        private String altitude;

        @Schema(description = "观测年限")
        private String observationTime;

        @Schema(description = "缺测时间段")
        private String missingTestingTimePeriod;

        @Schema(description = "观测方式")
        private String observationMethod;

        @Schema(description = "年限")
        private Integer dataAge;

        @Schema(description = "资料起止年份")
        private String dataYear;

        @Schema(description = "观测场地点")
        private String sectionAddress;

    }

    @Schema(description = "管理后台 - 项目信息")
    @Data
    public static class ProjectInfo {

        @Schema(description = "站点类型")
        private String type;

        @Schema(description = "检索标识")
        private Integer indexNum;

        @Schema(description = "文件资料树id")
        private Long id;

        @Schema(description = "项目id")
        private Long projectId;

        @Schema(description = "电站名称")
        private String powerStationName;

        @Schema(description = "省市区")
        private String area;

        @Schema(description = "经度")
        private String longitude;

        @Schema(description = "纬度")
        private String latitude;

        @Schema(description = "设计阶段")
        private String designStage;

        @Schema(description = "开发方式")
        private String developWay;
        /**
         * 总装机容量(MW)
         */
        @Schema(description = "总装机容量(MW)")
        private String totalCapacity;

        @Schema(description = "是否展示文件资料，0：不展示，1：展示")
        private Integer showFileTree;

    }


    @Schema(description = "管理后台 - 资源站点")
    @Data
    public static class ResourceSiteManagement {

        @Schema(description = "站点类型")
        private String type;

        @Schema(description = "检索标识")
        private Integer indexNum;

        /**
         * 主键
         */
        @Schema(description = "文件资源树Id")
        private Long id;

        @Schema(description = "资源站点id")
        private Long resourceSiteId;

        /**
         * 站址名称
         */
        @Schema(description = "站址名称")
        private String powerStationName;


        /**
         * 站点类型：抽水蓄能，常规水电，混合式抽蓄
         */
        @Schema(description = "站点类型")
        private Integer powerStationType;

        /**
         * 所属电网
         */
        @Schema(description = "所属电网")
        private String powerCode;

        /**
         * 国家名称：中国，其他
         */
        @Schema(description = "国家")
        private String country;

        /**
         * 省份
         */
        @Schema(description = "省")
        private String province;

        /**
         * 市
         */
        @Schema(description = "市")
        private String city;

        @Schema(description = "县")
        private String county;

        /**
         * 详细位置
         */
        @Schema(description = "详细位置")
        private String address;

        /**
         * 工作深度：规划，预可，三专，可研，详图，已建
         */
        @Schema(description = "工作深度")
        private String designStage;


        /**
         * 工作方式：纯蓄能，混合式
         */
        @Schema(description = "工作方式")
        private String developWay;


        /**
         * 是否纳规
         */
        @Schema(description = "是否纳规")
        private String complianceRegulations;

        /**
         * 经度
         */
        @Schema(description = "经度")
        private String longitude;

        /**
         * 纬度
         */
        @Schema(description = "纬度")
        private String latitude;

        /**
         * 总装机容量(MW)
         */
        @Schema(description = "总装机容量(MW)")
        private String totalCapacity;

        /**
         * 连续满发小时数(h)
         */
        @Schema(description = "连续满发小时数(h)")
        private String fullShippingHours;

        /**
         * 额定水头
         */
        @Schema(description = "额定水头")
        private String ratedHead;

        /**
         * 距高比
         */
        @Schema(description = "距高比")
        private String distanceToHeightRatio;

        /**
         * 单位千瓦静态投资(元/kW)
         */
        @Schema(description = "单位千瓦静态投资(元/kW)")
        private String staticInvestment;

        /**
         * 水源条件
         */
        @Schema(description = "水源条件")
        private String waterSourceConditions;

        /**
         * 重大敏感因素
         */
        @Schema(description = "重大敏感因素")
        private String significantSensitiveFactors;

        /**
         * 设计单位
         */
        @Schema(description = "设计单位")
        private String designUnit;

        /**
         * 投资单位
         */
        @Schema(description = "投资单位")
        private String lnvestmentUnit;

        /**
         * 数据来源
         */
        @Schema(description = "数据来源")
        private String dataSources;

        /**
         * 备注
         */
        @Schema(description = "备注")
        private String remarks;

        @Schema(description = "是否展示文件资料，0：不展示，1：展示")
        private Integer showFileTree;

    }


}
