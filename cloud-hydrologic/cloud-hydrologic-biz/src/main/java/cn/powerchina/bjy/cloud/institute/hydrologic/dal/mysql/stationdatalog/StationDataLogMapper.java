package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationdatalog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo.StationDataLogPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 站点数据更新记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StationDataLogMapper extends BaseMapperX<StationDataLogDO> {

    default PageResult<StationDataLogDO> selectPage(StationDataLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<StationDataLogDO>()
                .eqIfPresent(StationDataLogDO::getStationId, reqVO.getStationId())
                .eqIfPresent(StationDataLogDO::getDataType, reqVO.getDataType())
                .orderByDesc(StationDataLogDO::getId));
    }

    /**
     * 获取某个站点类型下某个站点的某个数据类型的最新版本
     *
     * @param stationId
     * @param stationType
     * @param dataType
     * @return
     */
    @Select("select * from hydrologic_station_data_log where station_type = #{stationType} and station_id = #{stationId} and data_type = #{dataType} and deleted =0 order by current_version desc limit 1")
    StationDataLogDO selectStationDataLogDOLatestByStationType(@Param("stationId") Long stationId, @Param("stationType") Integer stationType, @Param("dataType") Integer dataType);

    /**
     * 获取某个站点类型下某个站点的某个数据类型的最新版本
     *
     * @param stationId
     * @param stationType
     * @param dataType
     * @param year
     * @return
     */
    @Select("select * from hydrologic_station_data_log where station_type = #{stationType} and station_id = #{stationId} and data_type = #{dataType} and year = #{year} and deleted =0 order by current_version desc limit 1")
    StationDataLogDO selectStationDataLogDOLatestByStationTypeAndYear(@Param("stationId") Long stationId, @Param("stationType") Integer stationType, @Param("dataType") Integer dataType, @Param("year") String year);

    /**
     * 获取年鉴拥有的年
     *
     * @param stationId
     * @param stationType
     * @param dataType
     * @return
     */
    @Select("select distinct(year) from hydrologic_station_data_log where station_type = #{stationType} and station_id = #{stationId} and data_type = #{dataType} and deleted =0 ")
    List<String> selectStationDataLogYear(@Param("stationId") Long stationId, @Param("stationType") Integer stationType, @Param("dataType") Integer dataType);

    List<StationDataLogDO> listYearLogByStationIds(@Param("stationIds") List<Long> stationIds);

    int insertList(@Param("list")List<StationDataLogDO> list);

    List<StationDataLogDO> listByStationId(@Param("stationId")Long stationId);


    StationDataLogDO getLatest(@Param("stationId") Long stationId, @Param("dataType") Integer dataType);

}