package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.iceextract;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.iceextract.vo.IceExtractPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.iceextract.IceExtractDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.HydrologicChecker;
import org.apache.ibatis.annotations.Mapper;

import java.util.Arrays;

/**
 * 冰情摘录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface IceExtractMapper extends BaseMapperX<IceExtractDO>, HydrologicChecker<IceExtractDO> {

    default PageResult<IceExtractDO> selectPage(IceExtractPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<IceExtractDO>()
                .eqIfPresent(IceExtractDO::getStationId, reqVO.getStationId())
                .eqIfPresent(IceExtractDO::getYear, reqVO.getYear())
                .eqIfPresent(IceExtractDO::getDay, reqVO.getDay())
                .eqIfPresent(IceExtractDO::getHoursMinute, reqVO.getHoursMinute())
                .eqIfPresent(IceExtractDO::getIceSituation, reqVO.getIceSituation())
                .eqIfPresent(IceExtractDO::getIceThickness, reqVO.getIceThickness())
                .eqIfPresent(IceExtractDO::getDeepSnowOnIce, reqVO.getDeepSnowOnIce())
                .eqIfPresent(IceExtractDO::getShoreTemperature, reqVO.getShoreTemperature())
                .eqIfPresent(IceExtractDO::getWaterLevel, reqVO.getWaterLevel())
                .eqIfPresent(IceExtractDO::getVersion, reqVO.getVersion())
                .eqIfPresent(IceExtractDO::getLatest, reqVO.getLatest())
                .betweenIfPresent(IceExtractDO::getCurrentDay, reqVO.getCurrentDay())
                .betweenIfPresent(IceExtractDO::getCreateTime, reqVO.getCreateTime())
                .inIfPresent(IceExtractDO::getMonth, reqVO.getMonth() == null ? null : Arrays.asList(reqVO.getMonth()))
                .orderByAsc(IceExtractDO::getYear)
                .orderByAsc(IceExtractDO::getMonth)
                .orderByAsc(IceExtractDO::getDay));
    }

}