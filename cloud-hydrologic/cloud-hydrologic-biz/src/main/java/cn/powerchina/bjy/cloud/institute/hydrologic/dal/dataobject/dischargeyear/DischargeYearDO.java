package cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.dischargeyear;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 水文站-输沙率-冰情水温年化 DO
 *
 * <AUTHOR>
 */
@TableName("hydrologic_discharge_year")
@KeySequence("hydrologic_discharge_year_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DischargeYearDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 水文站id
     */
    private Long stationId;
    /**
     * 年
     */
    private Integer year;
    /**
     * 年平均输沙率(kg/s)
     */
    private String averageValue;
    /**
     * 最大日输沙率(kg/s)
     */
    private String maxValue;
    /**
     * 最大日输沙率出现日期
     */
    private String maxValueDate;
    /**
     * 年输沙量(万t)
     */
    private String totalValue;
    /**
     * 输沙模数(t/km2)
     */
    private String dischargeModulus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本（根据原型待定）
     */
    private Integer version;
    /**
     * 最新版本（1：最新，0：历史版本，默认为1）
     */
    private Integer latest;

}