package cn.powerchina.bjy.cloud.institute.hydrologic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 站点枚举
 */
@Getter
@AllArgsConstructor
public enum MonitoringItemEnum {

    RAIN_TYPE(1, "6,7", "雨量站"),
    HYDROLOGIC_RESERVOIR(20, "1,2,3,4,5,6,7,8", "水库站"),
    HYDROLOGIC_WATER_LEVEL(21, "1,5,6,7,9", "水位站"),
    HYDROLOGIC_HYDROLOGIC(22, "1,2,3,4,5,6,7,8,9", "水文站"),
    WEATHER_TYPE(3, "10", "气象站");

    private final Integer type;
    private final String code;
    private final String desc;

    public static String getCodeByType(Integer type) {
        for (MonitoringItemEnum e : MonitoringItemEnum.values()) {
            if (e.type.equals(type)) {
                return e.code;
            }
        }
        return null;
    }

    public static MonitoringItemEnum fromCode(String code){
        for (MonitoringItemEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
