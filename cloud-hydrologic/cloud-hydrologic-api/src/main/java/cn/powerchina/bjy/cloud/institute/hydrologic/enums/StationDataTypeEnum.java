package cn.powerchina.bjy.cloud.institute.hydrologic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 站点数据类型枚举
 */
@Getter
@AllArgsConstructor
@Deprecated
public enum StationDataTypeEnum {

    STATION_RAIN_YEAR_BOOK(10100, "逐日降水量表(年鉴形式)", "降水-逐日降水量年鉴"),
    STATION_RAIN_DAY(10101, "逐日降水量表(序列形式)", "降水-逐日降水量序列"),
    STATION_RAIN_MONTH(10102, "月降水特征值表(序列形式)", "降水-月降水特征值"),
    STATION_RAIN_YEAR(10103, "年降水特征值表(序列形式)", "降水-年降水特征值"),
    STATION_RAIN_EXCERPT(10104, "降水量摘录表", "降水-降水量摘录表"),
    STATION_RAIN_PERIOD_DAY(10105, "各时段最大降水量表(日时段)", "降水-各时段最大降水-按日"),
    STATION_RAIN_PERIOD_HOUR(10106, "各时段最大降水量表(小时时段)", "降水-各时段最大降水-按小时"),
    STATION_RAIN_PERIOD_MINUTE(10107, "各时段最大降水量表(分钟时段)", "降水-各时段最大降水-按分钟"),

    STATION_EVAPORATE_YEAR_BOOK(10108, "逐日水面蒸发量表(年鉴格式)", "蒸发-逐日水面蒸发年鉴"),
    STATION_EVAPORATE_DAY(10109, "逐日水面蒸发量表(序列形式)", "蒸发-逐日水面蒸发序列"),
    STATION_EVAPORATE_MONTH(10110, "月水面蒸发量特征值表(序列形式)", "蒸发-月水面蒸发特征值"),
    STATION_EVAPORATE_YEAR(10111, "年水面蒸发量特征值表(序列形式)", "蒸发-年水面蒸发特征值"),


    HYDROLOGIC_STATION_ICE_STATIC(20100, "冰情统计表", "水文站-冰情-冰情统计表"),
    HYDROLOGIC_STATION_ICE_EXCERPT(20101, "冰情摘录表", "水文站-冰情-冰情摘录表"),
    HYDROLOGIC_STATION_ICE_DAY(20102, "逐日水温表(序列形式)", "水文站-冰情-逐日水温表(序列形式)"),
    HYDROLOGIC_STATION_ICE_MONTH(20103, "月水温特征值表(序列形式)", "水文站-冰情-月水温特征值表"),
    HYDROLOGIC_STATION_ICE_YEAR(20104, "年水温特征值表(序列形式)", "水文站-冰情-年水温特征值表"),
    HYDROLOGIC_STATION_ICE_BOOK(20106, "逐日水温表(年鉴格式)", "水文站-冰情-逐日水温表(年鉴格式)"),
    HYDROLOGIC_STATION_GRAIN_INFO_MONTH(20107, "月平均悬移质颗粒级配表", "水文站-颗粒级配-月平均悬移质颗粒级配表"),
    HYDROLOGIC_STATION_GRAIN_RESULT(20108, "实测悬移质颗粒级配成果表", "水文站-颗粒级配-实测悬移质颗粒级配表"),
    HYDROLOGIC_STATION_GRAIN_UNIT_RESULT(20109, "实测悬移质单位水样颗粒级配成果表", "水文站-颗粒级配-实测悬移质单位水样颗粒级配表"),
    HYDROLOGIC_STATION_FLOW_DAY(20110, "逐日平均流量表(序列形式)", "水文站-流量-逐日流量特征值(序列形式)"),
    HYDROLOGIC_STATION_FLOW_MONTH(20111, "逐月流量特征值(序列形式)", "水文站-流量-逐月流量特征表"),
    HYDROLOGIC_STATION_FLOW_YEAR(20112, "年流量特征值表(序列形式)", "水文站-流量-年流量特征表"),
    HYDROLOGIC_STATION_FLOW_FLOOD(20113, "洪水水文要素摘录表", "水文站-流量-洪水水文要素摘录表"),
    HYDROLOGIC_STATION_FLOW_WATER(20141, "水库水文要素摘录表", "水文站-流量-水库水文要素摘录表"),
    HYDROLOGIC_STATION_FLOW_RESULT(20114, "实测流量成果表", "水文站-流量-实测流量成果表"),
    HYDROLOGIC_STATION_FLOW_BOOK(20115, "逐日平均流量表(年鉴格式)", "水文站-流量-逐日平均流量表(年鉴格式)"),
    HYDROLOGIC_STATION_WATER_LEVEL_DAY(20116, "逐日平均水位表(序列形式)", "水文站-水位-逐日平均水位表(序列形式)"),
    HYDROLOGIC_STATION_WATER_LEVEL_MONTH(20117, "月水位特征值表(序列形式)", "水文站-水位-月水位特征值表"),
    HYDROLOGIC_STATION_WATER_LEVEL_YEAR(20118, "年水位特征值表(序列形式)", "水文站-水位-年水位特征值表"),
    HYDROLOGIC_STATION_WATER_LEVEL_BOOK(20119, "逐日平均水位表(年鉴格式)", "水文站-水位-逐日平均水位表(年鉴)"),
    HYDROLOGIC_STATION_DISCHARGE_BOOK(20120, "逐日平均悬移质输沙率表(年鉴格式)", "水文站-逐日平均悬移质输沙率表(年鉴格式)"),
    HYDROLOGIC_STATION_DISCHARGE_DAY(20121, "逐日平均悬移质输沙率表(序列形式)", "水文站-逐日平均悬移质输沙率表(序列形式)"),
    HYDROLOGIC_STATION_DISCHARGE_MONTH(20122, "月输沙率特征值(序列形式)", "水文站-月输沙率特征值(序列形式)"),
    HYDROLOGIC_STATION_DISCHARGE_YEAR(20123, "年输沙率特征值表(序列形式)", "水文站-年输沙率特征值表(序列形式)"),
    HYDROLOGIC_STATION_DISCHARGE_RESULT(20124, "实测悬移质输沙率成果表", "水文站-实测悬移质输沙率成果表"),
    HYDROLOGIC_STATION_SAND_BOOK(20125, "逐日平均含沙量表(年鉴格式)", "水文站-含沙量-逐日平均表(年鉴)"),
    HYDROLOGIC_STATION_SAND_DAY(20126, "逐日平均含沙量表(序列形式)", "水文站-含沙量-逐日平均表(序列)"),
    HYDROLOGIC_STATION_SAND_MONTH(20127, "月含沙量特征值(序列形式)", "水文站-含沙量-月含沙量特征表"),
    HYDROLOGIC_STATION_SAND_YEAR(20128, "年含沙量特征值表(序列形式)", "水文站-含沙量-年含沙量特征表"),
    HYDROLOGIC_STATION_GRAIN_INFO_YEAR(20129, "年平均悬移质颗粒级配表", "水文站-颗粒级配-年平均悬移质颗粒级配表"),
//    HYDROLOGIC_STATION_FLOW_WATER("水库水文要素摘录表", 220141,SuspendedGradingResultProcessorImpl .class, true, false,StationEnum.HYDROLOGIC.getType()),

    HYDROLOGIC_SECTION_SURVEY_DETAIL(220130, "实测大断面成果表(汛前)","水文站-流量-实测大断面成果表(汛前)"),

    WEATHER_STATION_DAY(30100, "逐日气温表", "气象站-逐日气温表"),
    WEATHER_STATION_DATA(30101, "气象资料统计表", "气象站-气象资料统计表"),

    TEMPLATE_STATION_WEATHER_STATION(40100, "气象站信息", "站点-气象站"),
    TEMPLATE_STATION_RAINFALL_STATION(40101, "雨量站信息", "站点-雨量站"),
    TEMPLATE_STATION_HYDROLOGIC_STATION(40102, "水位站信息", "站点-水文站"),
    TEMPLATE_PROJECT_ITEM_STATION(40103, "项目信息", "项目信息模板"),

    ;

    private final Integer type;
    private final String desc;
    private final String remark;

    public static String getConfigKey(String type) {
        if (null == type) {
            return null;
        }
        Integer value = Integer.valueOf(type);
        for (StationDataTypeEnum item : StationDataTypeEnum.values()) {
            if (item.type.equals(value)) {
                return "bjyplan-" + type;
            }
        }
        return null;
    }

    public static StationDataTypeEnum getInstance(Integer type) {
        for (StationDataTypeEnum dataTypeEnum : values()) {
            if (dataTypeEnum.getType().equals(type)) {
                return dataTypeEnum;
            }
        }
        return null;
    }
}
