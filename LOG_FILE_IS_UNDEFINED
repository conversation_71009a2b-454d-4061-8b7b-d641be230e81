2025-07-06 12:57:40.612 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:57:40.614 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:57:45.619 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:57:45.621 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:57:46.122 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.807+000.000BA
2025-07-06 12:57:46.124 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 12:57:46.625 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.33363
2025-07-06 12:57:46.627 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:57:51.633 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:57:51.635 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:57:56.642 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:57:56.644 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:57:59.147 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.06+0999475F5
2025-07-06 12:57:59.147 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777879147,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.814",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.3573",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994755"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.06"
  } ]
}
2025-07-06 12:57:59.149 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:57:59.151 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777879147, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.814, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.3573, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994755, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.06, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9C56000031C2}
2025-07-06 12:57:59.151 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751633641420,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:57:59.151 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:57:59.651 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.812+000.000AF
2025-07-06 12:57:59.653 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 12:58:00.000 | [34m INFO 33140[0;39m | [1;33mquartzScheduler_Worker-3 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.impl.EdgeSyncServiceImpl  [0;39m | The iot-edge cloud-link-edge_mp5doY0Sp is heart beat
2025-07-06 12:58:00.005 | [34m INFO 33140[0;39m | [1;33mquartzScheduler_Worker-3 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_sync,  message = EdgeSyncUpDTO(tenant=1, name=物联网边缘网关-01, service=cloud-link-edge_mp5doY0Sp, host=**************, port=11085, remark=物联网边缘网关-01-备注, client=cloud-link-edge_mp5doY0Sp, macAddr=C8-F7-50-76-87-58, node=mp5doY0Sp, currentTime=1751777880000, syncType=2, resourceSpaceId=1830490145091092480), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9C57000031C3}
2025-07-06 12:58:00.154 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.37026
2025-07-06 12:58:00.156 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:58:02.184 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.05+0999475F6
2025-07-06 12:58:02.184 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777882184,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.812",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.37026",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994756"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.05"
  } ]
}
2025-07-06 12:58:02.186 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-131 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:58:02.188 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777882184, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.812, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.37026, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994756, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.05, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9C59000031C4}
2025-07-06 12:58:02.188 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748841253,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:58:02.188 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:58:07.287 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-131 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:58:07.289 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:58:09.342 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.02+0999475F9
2025-07-06 12:58:09.342 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777889342,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.812",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.3098",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994759"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.02"
  } ]
}
2025-07-06 12:58:09.344 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:58:09.345 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777889342, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.812, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.3098, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994759, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.02, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9C60000031C5}
2025-07-06 12:58:09.346 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748841272,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:58:09.346 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:58:14.474 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:58:14.476 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:58:19.579 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:58:19.581 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-128 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:58:24.697 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-128 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:58:24.699 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:58:25.211 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.813+000.000AE
2025-07-06 12:58:25.213 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:58:30.296 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:58:30.298 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:58:30.798 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.809+000.000B8
2025-07-06 12:58:30.800 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 12:58:31.301 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.3573F
2025-07-06 12:58:31.303 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:58:33.808 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.06+0999475F5
2025-07-06 12:58:33.808 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777913808,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.809",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.3573",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994755"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.06"
  } ]
}
2025-07-06 12:58:33.810 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:58:33.814 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777913808, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.809, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.3573, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994755, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.06, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9C79000031C6}
2025-07-06 12:58:33.814 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748841311,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:58:33.814 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:58:38.816 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:58:38.818 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:58:43.825 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:58:43.827 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:58:44.327 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.811+000.000B0
2025-07-06 12:58:44.329 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 12:58:44.830 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.34551
2025-07-06 12:58:44.832 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:58:47.335 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.03+0999475F8
2025-07-06 12:58:47.335 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777927335,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.811",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.34551",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994758"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.03"
  } ]
}
2025-07-06 12:58:47.337 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 12:58:47.339 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777927335, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.811, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.34551, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994758, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.03, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9C86000031C7}
2025-07-06 12:58:47.339 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748901248,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:58:47.339 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:58:47.838 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.33363
2025-07-06 12:58:47.840 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:58:50.344 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.01+0999475FA
2025-07-06 12:58:50.344 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777930344,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.81",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.33363",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "0999475"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.01"
  } ]
}
2025-07-06 12:58:50.346 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-139 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:58:50.348 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777930344, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.81, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.33363, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=0999475, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.01, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9C89000031C8}
2025-07-06 12:58:50.348 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748901282,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:58:50.348 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:58:50.847 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-139 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.816+000.000AB
2025-07-06 12:58:50.849 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:58:55.855 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:58:55.857 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:59:00.863 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:59:00.865 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:59:01.365 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.809+000.000B8
2025-07-06 12:59:01.367 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 12:59:01.868 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.34551
2025-07-06 12:59:01.870 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:59:04.374 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.02+0999475F9
2025-07-06 12:59:04.374 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777944374,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.809",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.34551",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994759"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.02"
  } ]
}
2025-07-06 12:59:04.376 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:59:04.377 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777944374, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.809, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.34551, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994759, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.02, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9C97000031C9}
2025-07-06 12:59:04.378 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748901311,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:59:04.378 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:59:09.382 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:59:09.384 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:59:14.390 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:59:14.392 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:59:14.893 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.812+000.000AF
2025-07-06 12:59:14.895 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 12:59:15.396 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.3573F
2025-07-06 12:59:15.398 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:59:17.901 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.05+0999475F6
2025-07-06 12:59:17.901 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777957901,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.812",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.3573",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994756"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.05"
  } ]
}
2025-07-06 12:59:17.903 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:59:17.904 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777957901, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.812, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.3573, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994756, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.05, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CA5000031CA}
2025-07-06 12:59:17.905 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748901347,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:59:17.905 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:59:20.406 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.01+0999475FA
2025-07-06 12:59:20.406 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777960406,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.807",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.34551",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "0999475"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.01"
  } ]
}
2025-07-06 12:59:20.408 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-122 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:59:20.410 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777960406, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.807, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.34551, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=0999475, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.01, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CA7000031CB}
2025-07-06 12:59:20.410 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748961319,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:59:20.410 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:59:22.912 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-122 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.01+0999475FA
2025-07-06 12:59:22.912 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-122 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777962912,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.808",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.3573",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "0999475"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.01"
  } ]
}
2025-07-06 12:59:22.914 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:59:22.916 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-122 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777962912, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.808, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.3573, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=0999475, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.01, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CAA000031CC}
2025-07-06 12:59:22.916 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-122 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748961344,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:59:22.916 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-122 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:59:27.922 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:59:27.924 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:59:32.931 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:59:32.933 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:59:33.433 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-129 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.810+000.000B1
2025-07-06 12:59:33.436 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:59:38.445 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:59:38.447 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:59:43.454 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:59:43.456 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:59:43.956 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.812+000.000AF
2025-07-06 12:59:43.958 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 12:59:44.459 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.32175
2025-07-06 12:59:44.461 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 12:59:46.964 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.03+0999475F8
2025-07-06 12:59:46.964 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751777986964,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.812",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.32175",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994758"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.03"
  } ]
}
2025-07-06 12:59:46.966 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:59:46.967 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751777986964, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.812, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.32175, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994758, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.03, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CC2000031CD}
2025-07-06 12:59:46.968 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748961379,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 12:59:46.968 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-118 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 12:59:51.973 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:59:51.975 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 12:59:56.980 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 12:59:56.982 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 12:59:57.483 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.810+000.000B1
2025-07-06 12:59:57.485 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 12:59:57.986 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.33363
2025-07-06 12:59:57.988 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:00:00.000 | [34m INFO 33140[0;39m | [1;33mquartzScheduler_Worker-7 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.impl.EdgeSyncServiceImpl  [0;39m | The iot-edge cloud-link-edge_mp5doY0Sp is heart beat
2025-07-06 13:00:00.000 | [34m INFO 33140[0;39m | [1;33mquartzScheduler_Worker-8 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:00:00.000 | [34m INFO 33140[0;39m | [1;33mquartzScheduler_Worker-5 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:00:00.006 | [34m INFO 33140[0;39m | [1;33mquartzScheduler_Worker-7 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_sync,  message = EdgeSyncUpDTO(tenant=1, name=物联网边缘网关-01, service=cloud-link-edge_mp5doY0Sp, host=**************, port=11085, remark=物联网边缘网关-01-备注, client=cloud-link-edge_mp5doY0Sp, macAddr=C8-F7-50-76-87-58, node=mp5doY0Sp, currentTime=1751778000000, syncType=2, resourceSpaceId=1830490145091092480), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CCF000031CE}
2025-07-06 13:00:00.491 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.03+0999475F8
2025-07-06 13:00:00.491 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778000491,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.81",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.33363",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994758"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.03"
  } ]
}
2025-07-06 13:00:00.493 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-139 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:00:00.495 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778000491, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.81, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.33363, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994758, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.03, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CCF000031CF}
2025-07-06 13:00:00.496 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751748961416,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:00:00.496 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-133 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:00:00.994 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-139 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.38214
2025-07-06 13:00:00.996 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:00:06.002 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:00:06.004 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:00:11.011 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:00:11.012 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:00:11.513 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.810+000.000B1
2025-07-06 13:00:11.515 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:00:12.016 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.37026
2025-07-06 13:00:12.018 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:00:14.521 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.05+0999475F6
2025-07-06 13:00:14.521 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778014521,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.81",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.37026",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994756"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.05"
  } ]
}
2025-07-06 13:00:14.523 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:00:14.525 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778014521, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.81, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.37026, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994756, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.05, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CDD000031D0}
2025-07-06 13:00:14.525 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749021360,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:00:14.525 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-100 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:00:19.529 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:00:19.531 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:00:24.540 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:00:24.542 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:00:25.042 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.814+000.000AD
2025-07-06 13:00:25.044 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:00:25.545 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.3573F
2025-07-06 13:00:25.547 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:00:28.051 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.05+0999475F6
2025-07-06 13:00:28.051 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778028051,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.814",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.3573",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994756"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.05"
  } ]
}
2025-07-06 13:00:28.053 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:00:28.054 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778028051, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.814, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.3573, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994756, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.05, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CEB000031D1}
2025-07-06 13:00:28.055 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749021390,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:00:28.055 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-120 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:00:33.059 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:00:33.061 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:00:38.068 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:00:38.070 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:00:38.571 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.809+000.000B8
2025-07-06 13:00:38.573 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:00:39.074 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.32175
2025-07-06 13:00:39.076 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:00:41.578 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.05+0999475F6
2025-07-06 13:00:41.578 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778041578,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.809",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.32175",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994756"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.05"
  } ]
}
2025-07-06 13:00:41.580 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:00:41.582 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778041578, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.809, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.32175, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994756, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.05, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CF8000031D2}
2025-07-06 13:00:41.583 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749021428,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:00:41.583 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-104 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:00:42.081 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.33363
2025-07-06 13:00:42.083 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:00:44.586 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.02+0999475F9
2025-07-06 13:00:44.586 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778044586,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.813",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.33363",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994759"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.02"
  } ]
}
2025-07-06 13:00:44.588 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-128 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:00:44.591 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778044586, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.813, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.33363, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994759, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.02, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9CFB000031D3}
2025-07-06 13:00:44.591 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749021466,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:00:44.591 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-138 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:00:49.594 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-128 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:00:49.596 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-128 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:00:50.097 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-128 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.810+000.000B1
2025-07-06 13:00:50.099 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:00:50.600 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.815+000.000AC
2025-07-06 13:00:50.602 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:00:51.102 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.39402
2025-07-06 13:00:51.104 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:00:53.608 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.05+0999475F6
2025-07-06 13:00:53.608 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778053608,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.815",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.39402",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994756"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.05"
  } ]
}
2025-07-06 13:00:53.610 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:00:53.613 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778053608, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.815, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.39402, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994756, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.05, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9D05000031D4}
2025-07-06 13:00:53.613 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749081313,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:00:53.613 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-107 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:00:58.617 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:00:58.619 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:00:59.119 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.809+000.000B8
2025-07-06 13:00:59.121 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:00:59.622 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.32175
2025-07-06 13:00:59.624 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:01:02.127 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.01+0999475FA
2025-07-06 13:01:02.127 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778062127,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.809",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.32175",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "0999475"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.01"
  } ]
}
2025-07-06 13:01:02.129 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:01:02.131 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778062127, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.809, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.32175, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=0999475, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.01, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9D0D000031D5}
2025-07-06 13:01:02.131 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749081354,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:01:02.131 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-110 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:01:07.136 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:01:07.138 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:01:12.145 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:01:12.147 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:01:12.648 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.813+000.000AE
2025-07-06 13:01:12.650 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:01:13.151 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.37026
2025-07-06 13:01:13.153 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:01:15.657 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.02+0999475F9
2025-07-06 13:01:15.657 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778075657,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.813",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.37026",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994759"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.02"
  } ]
}
2025-07-06 13:01:15.659 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-131 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:01:15.661 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778075657, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.813, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.37026, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994759, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.02, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9D1B000031D6}
2025-07-06 13:01:15.661 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749081369,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:01:15.661 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-115 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:01:20.665 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-131 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:01:20.667 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-131 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:01:21.168 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-131 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.809+000.000B8
2025-07-06 13:01:21.170 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:01:26.176 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:01:26.178 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:01:31.184 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:01:31.186 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:01:31.687 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-105 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.815+000.000AC
2025-07-06 13:01:31.689 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:01:36.695 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:01:36.697 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:01:41.704 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:01:41.706 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:01:42.206 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.807+000.000BA
2025-07-06 13:01:42.208 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:01:42.710 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.34551
2025-07-06 13:01:42.712 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:01:45.215 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.05+0999475F6
2025-07-06 13:01:45.215 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778105215,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.807",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.34551",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994756"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.05"
  } ]
}
2025-07-06 13:01:45.217 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:01:45.219 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778105215, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.807, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.34551, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994756, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.05, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9D38000031D7}
2025-07-06 13:01:45.219 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749141237,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:01:45.219 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-137 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:01:50.224 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:01:50.226 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:01:50.726 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.812+000.000AF
2025-07-06 13:01:50.728 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:01:51.229 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.3573F
2025-07-06 13:01:51.231 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:01:53.734 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.03+0999475F8
2025-07-06 13:01:53.734 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778113734,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.812",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.3573",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994758"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.03"
  } ]
}
2025-07-06 13:01:53.736 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:01:53.738 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778113734, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.812, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.3573, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994758, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.03, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9D41000031D8}
2025-07-06 13:01:53.738 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749141265,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:01:53.738 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:01:58.741 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:01:58.743 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:01:59.244 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.807+000.000BA
2025-07-06 13:01:59.246 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:01:59.748 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.33363
2025-07-06 13:01:59.750 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:02:00.000 | [34m INFO 33140[0;39m | [1;33mquartzScheduler_Worker-6 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.impl.EdgeSyncServiceImpl  [0;39m | The iot-edge cloud-link-edge_mp5doY0Sp is heart beat
2025-07-06 13:02:00.004 | [34m INFO 33140[0;39m | [1;33mquartzScheduler_Worker-6 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_sync,  message = EdgeSyncUpDTO(tenant=1, name=物联网边缘网关-01, service=cloud-link-edge_mp5doY0Sp, host=**************, port=11085, remark=物联网边缘网关-01-备注, client=cloud-link-edge_mp5doY0Sp, macAddr=C8-F7-50-76-87-58, node=mp5doY0Sp, currentTime=1751778120000, syncType=2, resourceSpaceId=1830490145091092480), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9D47000031D9}
2025-07-06 13:02:02.253 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.03+0999475F8
2025-07-06 13:02:02.253 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778122253,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.807",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.33363",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "09994758"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.03"
  } ]
}
2025-07-06 13:02:02.255 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:02:02.256 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778122253, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.807, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.33363, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=09994758, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.03, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9D49000031DA}
2025-07-06 13:02:02.257 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749141325,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:02:02.257 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:02:04.759 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.01+0999475FA
2025-07-06 13:02:04.759 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778124759,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.807",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.33363",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "0999475"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.01"
  } ]
}
2025-07-06 13:02:04.761 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-124 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->发送的数据为：:1412FF

2025-07-06 13:02:04.763 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778124759, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.807, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.33363, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=0999475, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.01, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9D4C000031DB}
2025-07-06 13:02:04.763 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749141356,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:02:04.763 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:02:05.261 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-124 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1412FF
--->收到的数据为: :1412+041.3573F
2025-07-06 13:02:05.263 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-124 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->发送的数据为：:142115FF

2025-07-06 13:02:07.766 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-124 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::142115FF
--->收到的数据为: :142115+2978.00+0999475FB
2025-07-06 13:02:07.766 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-124 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.s.i.EdgeSenderServiceImpl   [0;39m | propertyValueSender:{
  "currentTime" : 1751778127766,
  "edgeCode" : "mp5doY0Sp",
  "propertyValueList" : [ {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "R",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "f",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_voltage",
    "thingValue" : "12.814",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1736133939029192",
    "thingIdentity" : "mainboard_temperature",
    "thingValue" : "41.3573",
    "slaveId" : "14"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "temperature_frequency",
    "thingValue" : "0999475"
  }, {
    "deviceCode" : "D1746686019160716",
    "mcuChannel" : "15",
    "thingIdentity" : "infiltration_frequency",
    "thingValue" : "2978.00"
  } ]
}
2025-07-06 13:02:07.768 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:02:07.769 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-124 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.mq.RocketMQv5Client   [0;39m | 顺序消息发送完成：topic=topic_device_property_result,  message = EdgeReadPropertyValue(currentTime=1751778127766, edgeCode=mp5doY0Sp, propertyValueList=[EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=R, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=f, thingValue=null, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_voltage, thingValue=12.814, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1736133939029192, mcuChannel=null, thingIdentity=mainboard_temperature, thingValue=41.3573, slaveId=14), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=temperature_frequency, thingValue=0999475, slaveId=null), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO(deviceCode=D1746686019160716, mcuChannel=15, thingIdentity=infiltration_frequency, thingValue=2978.00, slaveId=null)]), sendReceipt = SendReceiptImpl{messageId=01C8F7507687588174087B9D4F000031DC}
2025-07-06 13:02:07.770 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-124 [TID: N/A][0;39m [1;32mc.p.b.c.l.e.l.EdgePropertyReceiver      [0;39m | receiver message {"currentTime":1751749201495,"driverCode":"JIKANG","edgeCode":"mp5doY0Sp","productCode":"CP1726102887459180","slaveId":"14","connectType":1,"extra":"{\"dataBit\": \"8\", \"stopBit\": \"1\", \"baudRate\": \"9600\", \"checkBit\": \"无\", \"serialPort\": \"COM3\"}","deviceCode":"D1736133939029192","devicePropertyDTOList":[{"productCode":"CP1726104393957824","deviceCode":"D1746686019160716","thingIdentityMap":{"temperature_frequency":2,"infiltration_frequency":1},"mcuChannel":"15"}],"thingIdentityMap":{"R":0,"mainboard_temperature":0,"f":0,"mainboard_voltage":0}}
2025-07-06 13:02:07.770 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-124 [TID: N/A][0;39m [1;32m.b.c.l.e.s.i.d.m.ModBusDriverServiceImpl[0;39m | Modbus Connection Info: COM3
2025-07-06 13:02:12.774 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:02:12.776 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:02:17.782 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:02:17.784 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:02:18.284 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-136 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.808+000.000B9
2025-07-06 13:02:18.286 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:02:23.294 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:02:23.296 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

2025-07-06 13:02:28.357 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->没有收到数据
2025-07-06 13:02:28.359 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->发送的数据为：:1411FF

2025-07-06 13:02:28.870 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-126 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::1411FF
--->收到的数据为: :1411+012.810+000.000B1
2025-07-06 13:02:28.872 | [34m INFO 33140[0;39m | [1;33mRocketmqMessageConsumption-2-135 [TID: N/A][0;39m [1;32mc.p.b.c.link.edge.sdk.modbus.RtuMaster  [0;39m | WriteAndResponse---command::14nullFF
--->发送的数据为：:14nullFF

