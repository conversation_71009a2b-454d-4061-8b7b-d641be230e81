package cn.powerchina.bjy.cloud.institute.essz.controller.admin.keydatadisplay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 电源发展规划 Response VO")
@Data
public class KeyDataPowerDevelopmentPlan {
    @Schema(description = "电源发展规划数据类型（1-省内电源、2-跨区受电、3-跨区送电）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer dataType;

    @Schema(description = "年类型（1-现状年、2-水平年）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer yearTypeCode;
    
    @Schema(description = "基础数据选项/万kW", requiredMode = Schema.RequiredMode.REQUIRED, example = "3680")
    private BigDecimal baseDataInstalledCapacity;

    @Schema(description = "现状年 或 水平年 的装机容量/万kW", requiredMode = Schema.RequiredMode.REQUIRED, example = "4030")
    private BigDecimal yearInstalledCapacity;
    
    @Schema(description = "年的字面量", requiredMode = Schema.RequiredMode.REQUIRED, example = "2022")
    private String year;

    @Schema(description = "电站相关信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<KeyDataStationVO> station;
}
