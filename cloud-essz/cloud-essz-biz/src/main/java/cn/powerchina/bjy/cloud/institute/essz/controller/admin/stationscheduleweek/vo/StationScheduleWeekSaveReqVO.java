package cn.powerchina.bjy.cloud.institute.essz.controller.admin.stationscheduleweek.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 电源-周-计划安排新增/修改 Request VO")
@Data
public class StationScheduleWeekSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private Long id;

    @Schema(description = "电源ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotNull(message = "电源ID不能为空")
    private Long stationId;

    @Schema(description = "周数(1-52)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "周数(1-52)不能为空")
    private Integer week;

    @Schema(description = "受阻系数(煤电、气电、光热、生物质发电、其他)")
    private BigDecimal blockedFactor;

    @Schema(description = "检修安排(煤电、气电、核电、常规水电、抽水蓄能、光热、生物质发电、新型储能、其他):0-工作、1-检修")
    private Integer verhaulSchedule;

    @Schema(description = "调峰幅度(煤电、气电、核电、常规水电、抽水蓄能、光热、生物质发电、新型储能、其他)")
    private BigDecimal peakAmplitude;

    @Schema(description = "预想出力系数(常规水电)")
    private BigDecimal expectedOutputFactor;

    @Schema(description = "平均出力系数(常规水电)")
    private BigDecimal averageOutputFactor;

    @Schema(description = "强迫出力系数(常规水电)")
    private BigDecimal forcedOutputFactor;

}