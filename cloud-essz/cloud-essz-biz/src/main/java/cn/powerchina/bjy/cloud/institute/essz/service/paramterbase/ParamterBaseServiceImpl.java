package cn.powerchina.bjy.cloud.institute.essz.service.paramterbase;

import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.QueryWrapperX;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.managementtcenter.vo.EssDeleteDataEditVo;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterbase.vo.ParamterBasePageReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterbase.vo.ParamterBaseSaveReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterinvest.vo.ParamterInvestSaveReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramteryear.vo.ParamterYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.paramterbase.ParamterBaseDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.paramterbase.ParamterBaseMapper;
import cn.powerchina.bjy.cloud.institute.essz.constants.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.essz.service.mc.EssRedisService;
import cn.powerchina.bjy.cloud.institute.essz.service.paramterinvest.ParamterInvestService;
import cn.powerchina.bjy.cloud.institute.essz.service.paramteryear.ParamterYearService;
import cn.powerchina.bjy.cloud.institute.essz.util.ess.EssThreadLocalrHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static  cn.powerchina.bjy.cloud.institute.essz.constants.ErrorCodeConstants.ESS_PARAMTER_BASE_NOT_EXISTS;

/**
 * 电源经济性参数(基础) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ParamterBaseServiceImpl implements ParamterBaseService {
    private final ParamterBaseMapper paramterBaseMapper;
    private final ParamterInvestService paramterInvestService;
    private final ParamterYearService paramterYearService;
    private final EssRedisService essRedisService;

    public ParamterBaseServiceImpl(ParamterBaseMapper paramterBaseMapper, ParamterInvestService paramterInvestService, ParamterYearService paramterYearService, EssRedisService essRedisService) {
        this.paramterBaseMapper = paramterBaseMapper;
        this.paramterInvestService = paramterInvestService;
        this.paramterYearService = paramterYearService;
        this.essRedisService = essRedisService;
    }

    @Override
    public Long createParamterBase(ParamterBaseSaveReqVO createReqVO) {
        // 插入
        ParamterBaseDO paramterBase = BeanUtils.toBean(createReqVO, ParamterBaseDO.class);
        Long userId = EssThreadLocalrHolder.getUserId();
        if (StringUtils.isEmpty(paramterBase.getCreator()) && Objects.nonNull(userId)) {
            paramterBase.setCreator(userId.toString());
            paramterBase.setUpdater(userId.toString());
        }
        paramterBaseMapper.insert(paramterBase);
        // 返回
        return paramterBase.getId();
    }

    @Override
    public void updateParamterBase(ParamterBaseSaveReqVO updateReqVO) {
        // 校验存在
        validateParamterBaseExists(updateReqVO.getId());
        // 更新
        ParamterBaseDO updateObj = BeanUtils.toBean(updateReqVO, ParamterBaseDO.class);
        paramterBaseMapper.updateById(updateObj);
    }

    @Override
    public void deleteParamterBase(Long id) {
        // 校验存在
        validateParamterBaseExists(id);
        // 删除
        paramterBaseMapper.deleteById(id);
    }

    private void validateParamterBaseExists(Long id) {
        if (paramterBaseMapper.selectById(id) == null) {
            throw exception(ESS_PARAMTER_BASE_NOT_EXISTS);
        }
    }

    @Override
    public ParamterBaseDO getParamterBase(Long projectId) {
        ParamterBaseDO paramterBaseDO = paramterBaseMapper.selectOne(new LambdaQueryWrapperX<ParamterBaseDO>()
                .eqIfPresent(ParamterBaseDO::getProjectId, projectId)
                .apply("deleted = {0}", 0));
        if (Objects.nonNull(paramterBaseDO)) {
            BigDecimal discountRate = paramterBaseDO.getDiscountRate();
            discountRate = discountRate.setScale(2, RoundingMode.HALF_UP);
            paramterBaseDO.setDiscountRate(discountRate);
        }
        return paramterBaseDO;
    }

    @Override
    public PageResult<ParamterBaseDO> getParamterBasePage(ParamterBasePageReqVO pageReqVO) {
        return paramterBaseMapper.selectPage(pageReqVO);
    }

    @Override
    public Boolean saveEconomicIndicator(ParamterBaseSaveReqVO paramterBaseSaveReqVO, List<ParamterInvestSaveReqVO> investSaveReqVOList, List<ParamterYearSaveReqVO> yearParemterList) {
        Long projectId = paramterBaseSaveReqVO.getProjectId();

        //先查询并记录旧的数据的id，再插入新数据
        QueryWrapperX<ParamterBaseDO> queryWrapper = new QueryWrapperX<>();
        queryWrapper.eq("project_id", projectId);
        ParamterBaseDO paramterBaseDO;
        try {
            paramterBaseDO = paramterBaseMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("原经济性参数数据不正确请检查",e);
            throw new ServiceException(ErrorCodeConstants.EXCEL_ERROR);
        }
        if (Objects.nonNull(paramterBaseDO)) {
            essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                    .projectId(projectId)
                    .paramterBaseId(paramterBaseDO.getId())
                    .build());
        }

        //使用统一事务之后获取不到生成的Id，没有使用统一事务，因此需要redis每个事务提交之后记录一次，
        Long paramterBaseId = this.createParamterBase(paramterBaseSaveReqVO);
        essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                .projectId(projectId)
                .newParamterBaseId(paramterBaseId)
                .build());
        paramterInvestService.batchInsert(investSaveReqVOList, paramterBaseId);
        paramterYearService.batchInsert(yearParemterList, paramterBaseId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataById(Long paramBaseId) {
        paramterBaseMapper.deleteParamterBaseById(paramBaseId);
        paramterInvestService.deleteParamterInvestByParentId(paramBaseId);
        paramterYearService.deleteParamterYearByParentId(paramBaseId);
    }

}