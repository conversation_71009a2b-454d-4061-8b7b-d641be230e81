package cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.transfer;

import cn.powerchina.bjy.cloud.framework.common.util.ext.SafeConverter;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 跨区域输送电 DO
 *
 * <AUTHOR>
 */
@TableName("ess_transfer")
@KeySequence("ess_transfer_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 工程ID
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 投运年
     */
    private String serviceYear;
    /**
     * 退役年
     */
    private String retireYear;
    /**
     * 状态（1-规划、2-在建、3-投运、4-退役、5-其他）
     */
    private Integer status;
    /**
     * 输送类型（1-送电、2-受电）
     */
    private Integer typeCode;
    /**
     * 通道能力/万kW
     */
    private BigDecimal channelCapacity;
    /**
     * 通道电量/亿kWh
     */
    private BigDecimal channelPower;

    /**
     * 年利用小时数/h
     */
    private BigDecimal annualUtilizationHour;

    /**
     * 调节系数/%
     */
    private BigDecimal adjustFactor;
    /**
     * 送出位置
     */
    private String outLocation;
    /**
     * 送入位置
     */
    private String inLocation;

    /**
     * 不能用状态要根据水平年判断
     * 投运年<=水平年<=退役年符合
     *
     * @return
     */
    public boolean matchActive(int levelYear) {
        return SafeConverter.toInt(serviceYear) <= levelYear && levelYear <= SafeConverter.toInt(retireYear);
    }

}