package cn.powerchina.bjy.cloud.institute.essz.service.station;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.ext.SafeConverter;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.QueryWrapperX;
import cn.powerchina.bjy.cloud.institute.essz.constants.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.essz.constants.EssConstant;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.managementtcenter.vo.EssDeleteDataEditVo;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.station.vo.EssStationPageReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.station.vo.EssStationSaveReqVO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.project.EssProjectDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.station.EssStationDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.stationenergycurve.StationEnergyCurveDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.stationschedulehour.StationScheduleHourDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.stationscheduleweek.StationScheduleWeekDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.station.EssStationMapper;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.stationenergycurve.StationEnergyCurveMapper;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.stationschedulehour.StationScheduleHourMapper;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.stationscheduleweek.StationScheduleWeekMapper;
import cn.powerchina.bjy.cloud.institute.essz.enums.StationStatusEnum;
import cn.powerchina.bjy.cloud.institute.essz.enums.StationTypeEnum;
import cn.powerchina.bjy.cloud.institute.essz.service.mc.EssRedisService;
import cn.powerchina.bjy.cloud.institute.essz.util.ess.EssThreadLocalrHolder;
import cn.powerchina.bjy.cloud.institute.essz.util.ess.HourDataCalcUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.essz.constants.ErrorCodeConstants.ESS_STATION_NOT_EXISTS;

/**
 * 电源信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class EssStationServiceImpl implements EssStationService {

    @Resource
    private EssStationMapper essStationMapper;

    @Autowired
    private StationEnergyCurveMapper stationEnergyCurveMapper;
    @Autowired
    private StationScheduleWeekMapper stationScheduleWeekMapper;
    @Autowired
    private StationScheduleHourMapper stationScheduleHourMapper;
    @Autowired
    private EssRedisService essRedisService;

    @Override
    public Long createStation(EssStationSaveReqVO createReqVO) {
        // 插入
        EssStationDO station = BeanUtils.toBean(createReqVO, EssStationDO.class);
        vaildEssStationDO(station);
        essStationMapper.insert(station);
        // 返回
        return station.getId();
    }

    @Override
    public void updateStation(EssStationSaveReqVO updateReqVO) {
        // 校验存在
        validateStationExists(updateReqVO.getId());
        // 更新
        EssStationDO updateObj = BeanUtils.toBean(updateReqVO, EssStationDO.class);
        vaildEssStationDO(updateObj);
        essStationMapper.updateById(updateObj);
    }

    @Override
    public void deleteStation(Long id) {
        // 校验存在
        validateStationExists(id);
        // 删除
        essStationMapper.deleteById(id);
    }

    private void validateStationExists(Long id) {
        if (essStationMapper.selectById(id) == null) {
            throw exception(ESS_STATION_NOT_EXISTS);
        }
    }

    @Override
    public EssStationDO getStation(Long id) {
        return essStationMapper.selectById(id);
    }

    @Override
    public PageResult<EssStationDO> getStationPage(EssStationPageReqVO pageReqVO) {
        return essStationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<EssStationDO> getStationList(EssStationPageReqVO pageReqVO) {
        return essStationMapper.selectList(EssStationDO::getCategoryCode, pageReqVO.getCategoryCode());
    }

    @Override
    public boolean batchSaveOfCoal(EssProjectDO project, String s, Map<String, Map> baseMap, Map<String, Map> listMap, Integer categoryCode) {
        EssStationDO stationSaveReqVO = new EssStationDO();
        stationSaveReqVO.setProjectId(project.getId());
        stationSaveReqVO.setName(s);
        stationSaveReqVO.setServiceYear(baseMap.get("投运年").get(s).toString());
        stationSaveReqVO.setRetireYear(baseMap.get("退役年").get(s).toString());
        stationSaveReqVO.setCategoryCode(categoryCode);
        stationSaveReqVO.setTypeCode(StationTypeEnum.getTypeCode(baseMap.get("类型").get(s).toString()));
        stationSaveReqVO.setStatus(StationStatusEnum.getStatusCode(baseMap.get("状态").get(s).toString()));
        stationSaveReqVO.setInstalledCapacity((BigDecimal) baseMap.get("装机容量/万kW").get(s));
        stationSaveReqVO.setUnitNum(Integer.valueOf(baseMap.get("机组台数/n").get(s).toString()));
        stationSaveReqVO.setAnnualEnergyOutput((BigDecimal) baseMap.get("年发电量/亿kWh").get(s));
        stationSaveReqVO.setAnnualUtilizationHour((BigDecimal) baseMap.get("年利用小时数/h").get(s));
        stationSaveReqVO.setOverhaulWeek(Integer.valueOf(baseMap.get("检修周数/n").get(s).toString()));
        stationSaveReqVO.setMaxOverhaulCapacity((BigDecimal) baseMap.get("最大检修能力").get(s));
        stationSaveReqVO.setMinTechOutput((BigDecimal) baseMap.get("最小技术出力").get(s));
        stationSaveReqVO.setClimbConstraint(baseMap.get("爬坡约束").get(s).equals("是") ? 1 : 0);
        stationSaveReqVO.setClimbAbility((BigDecimal) baseMap.get("爬坡能力").get(s));
        stationSaveReqVO.setAverageFuelRate((BigDecimal) baseMap.get("平均煤耗率（g/kWh)").get(s));
        stationSaveReqVO.setLocation(baseMap.get("电站位置").get(s).toString());

        //校验数据是否满足要求
        vaildEssStationDO(stationSaveReqVO);

        Long stationId = this.insertStation(stationSaveReqVO);
        essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                .projectId(project.getId())
                .stationId(stationId)
                .build());

        String userId = EssThreadLocalrHolder.getUserId().toString();

        saveEnergyCurve(s, listMap, stationId, userId, "煤耗曲线（g/kWh)");

        Map<String, ArrayList<BigDecimal>> blockedFactorMap = listMap.get("受阻系数");
        ArrayList<BigDecimal> blockedFactorList = blockedFactorMap.get(s);

        Map<String, ArrayList<BigDecimal>> verhaulScheduleMap = listMap.get("检修安排");
        ArrayList<BigDecimal> verhaulScheduleList = verhaulScheduleMap.get(s);
        //校验检修安排数据
        vaildVerHaulList(verhaulScheduleList);

        Map<String, ArrayList<BigDecimal>> peakAmplitudeMap = listMap.get("调峰幅度（%）");
        ArrayList<BigDecimal> peakAmplitudeMapList = peakAmplitudeMap.get(s);

        List<StationScheduleWeekDO> scheduleWeekDOList = new ArrayList<>(52);
        for (int i = 0; i < 52; i++) {
            StationScheduleWeekDO stationScheduleWeekDO = new StationScheduleWeekDO();
            stationScheduleWeekDO.setStationId(stationId);
            stationScheduleWeekDO.setWeek(i + 1);
            stationScheduleWeekDO.setBlockedFactor(blockedFactorList.get(i));
            stationScheduleWeekDO.setVerhaulSchedule(verhaulScheduleList.get(i).intValue());
            stationScheduleWeekDO.setPeakAmplitude(peakAmplitudeMapList.get(i).multiply(BigDecimal.valueOf(0.01)));
            stationScheduleWeekDO.setForcedOutputFactor(stationSaveReqVO.getMinTechOutput());
            stationScheduleWeekDO.setCreator(userId);
            stationScheduleWeekDO.setUpdater(userId);
            //校验周数据是否符合要求
            vaildStationScheduleWeekDO(stationScheduleWeekDO, i);
            scheduleWeekDOList.add(stationScheduleWeekDO);

        }
        stationScheduleWeekMapper.insertBatch(scheduleWeekDOList);
        log.info("{} 数据入库完成", s);

        return true;
    }

    @Override
    public Long insertStation(EssStationDO stationDo) {
        String userId = EssThreadLocalrHolder.getUserId().toString();
        LocalDateTime now = LocalDateTime.now();

        stationDo.setCreator(userId);
        stationDo.setUpdater(userId);
        stationDo.setCreateTime(now);
        stationDo.setUpdateTime(now);

        essStationMapper.insert(stationDo);
        return stationDo.getId();
    }

    /**
     * 根据工程Id 电源类型删除电源数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByProjectId(Long projectId, Integer categoryCode) {
        List<EssStationDO> essStationList = selectListByProjectIdAndCategoryCodeAndStatus(projectId, categoryCode, null);
        if (CollectionUtil.isNotEmpty(essStationList)) {
            List<Long> stationIdList = essStationList.stream().map(EssStationDO::getId).collect(Collectors.toList());
            essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                    .projectId(projectId)
                    .stationIdList(stationIdList)
                    .build());
        }
    }


    @Override
    public boolean batchSaveOfGas(EssProjectDO project, String s, Map<String, Map> baseMap, Map<String, Map> listMap, Integer categoryCode) {
        EssStationDO essStationDO = new EssStationDO();
        essStationDO.setProjectId(project.getId());
        essStationDO.setName(s);
        essStationDO.setServiceYear(baseMap.get("投运年").get(s).toString());
        essStationDO.setRetireYear(baseMap.get("退役年").get(s).toString());
        essStationDO.setCategoryCode(categoryCode);
        essStationDO.setTypeCode(StationTypeEnum.getTypeCode(baseMap.get("类型").get(s).toString()));
        essStationDO.setStatus(StationStatusEnum.getStatusCode(baseMap.get("状态").get(s).toString()));
        essStationDO.setInstalledCapacity((BigDecimal) baseMap.get("装机容量/万kW").get(s));
        essStationDO.setUnitNum(Integer.valueOf(baseMap.get("机组台数/n").get(s).toString()));
        essStationDO.setAnnualEnergyOutput((BigDecimal) baseMap.get("年发电量/亿kWh").get(s));
        essStationDO.setAnnualUtilizationHour((BigDecimal) baseMap.get("年利用小时数/h").get(s));
        essStationDO.setOverhaulWeek(Integer.valueOf(baseMap.get("检修周数/n").get(s).toString()));
        essStationDO.setMaxOverhaulCapacity((BigDecimal) baseMap.get("最大检修能力").get(s));
        essStationDO.setMinTechOutput((BigDecimal) baseMap.get("最小技术出力").get(s));
        essStationDO.setClimbConstraint(baseMap.get("爬坡约束").get(s).equals("是") ? 1 : 0);
        essStationDO.setClimbAbility((BigDecimal) baseMap.get("爬坡能力").get(s));
        essStationDO.setAverageFuelRate((BigDecimal) baseMap.get("平均气耗率（m³/kWh)").get(s));
        Object location = baseMap.get("电站位置").get(s);
        if (Objects.nonNull(location)) {
            essStationDO.setLocation(location.toString());
        }
        //校验数据是否满足要求
        vaildEssStationDO(essStationDO);

        Long stationId = this.insertStation(essStationDO);
        essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                .projectId(project.getId())
                .stationId(stationId)
                .build());

        String userId = EssThreadLocalrHolder.getUserId().toString();

        Map<String, ArrayList<BigDecimal>> coalPowerCurveMap = listMap.get("气耗曲线（m³/kWh)");
        ArrayList<BigDecimal> coalPowerList = coalPowerCurveMap.get(s);
        List<StationEnergyCurveDO> list = new ArrayList<>(11);
        BigDecimal usedFactor = BigDecimal.ZERO;
        for (int i = 0; i < coalPowerList.size(); i++) {
            StationEnergyCurveDO energyCurveDO = new StationEnergyCurveDO();
            energyCurveDO.setStationId(stationId);
            energyCurveDO.setUsedFactor(usedFactor);
            energyCurveDO.setLoadFactor(coalPowerList.get(i));
            energyCurveDO.setCreateTime(LocalDateTime.now());
            energyCurveDO.setUpdateTime(LocalDateTime.now());
            energyCurveDO.setCreator(userId);
            energyCurveDO.setUpdater(userId);

            list.add(energyCurveDO);
            usedFactor = usedFactor.add(BigDecimal.valueOf(0.1));
        }
        this.stationEnergyCurveMapper.insertBatch(list);


        Map<String, ArrayList<BigDecimal>> blockedFactorMap = listMap.get("受阻系数");
        ArrayList<BigDecimal> blockedFactorList = blockedFactorMap.get(s);

        Map<String, ArrayList<BigDecimal>> verhaulScheduleMap = listMap.get("检修安排");
        ArrayList<BigDecimal> verhaulScheduleList = verhaulScheduleMap.get(s);
        //校验检修安排
        vaildVerHaulList(verhaulScheduleList);

        Map<String, ArrayList<BigDecimal>> peakAmplitudeMap = listMap.get("调峰幅度（%）");
        ArrayList<BigDecimal> peakAmplitudeMapList = peakAmplitudeMap.get(s);

        List<StationScheduleWeekDO> scheduleWeekDOList = new ArrayList<>(52);
        for (int i = 0; i < 52; i++) {
            StationScheduleWeekDO stationScheduleWeekDO = new StationScheduleWeekDO();
            stationScheduleWeekDO.setStationId(stationId);
            stationScheduleWeekDO.setWeek(i + 1);
            stationScheduleWeekDO.setBlockedFactor(blockedFactorList.get(i));
            stationScheduleWeekDO.setVerhaulSchedule(verhaulScheduleList.get(i).intValue());
            stationScheduleWeekDO.setPeakAmplitude(peakAmplitudeMapList.get(i).multiply(BigDecimal.valueOf(0.01)));
            stationScheduleWeekDO.setCreator(userId);
            stationScheduleWeekDO.setUpdater(userId);
            //校验周数据是否符合要求
            vaildStationScheduleWeekDO(stationScheduleWeekDO, i);
            scheduleWeekDOList.add(stationScheduleWeekDO);
        }
        stationScheduleWeekMapper.insertBatch(scheduleWeekDOList);
        log.info("{} 数据入库完成", s);

        return true;
    }

    private static void vaildVerHaulList(ArrayList<BigDecimal> verhaulScheduleList) {
        BigDecimal[] bigDecimals = new BigDecimal[2];
        bigDecimals[0] = new BigDecimal(0);
        bigDecimals[1] = new BigDecimal(1);
        long count = verhaulScheduleList.stream().filter(item -> !ArrayUtils.contains(bigDecimals, item)).count();
        if(count>0){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ESS_STATION_SCHEDULE_WEEK_OVERHAUL_IS_FALSE);
        }
    }

    @Override
    public boolean batchSaveOfNuclear(EssProjectDO project, String s, Map<String, Map> baseMap, Map<String, Map> listMap, Integer categoryCode) {
        EssStationDO essStationDO = new EssStationDO();
        essStationDO.setProjectId(project.getId());
        essStationDO.setName(s);
        essStationDO.setCategoryCode(categoryCode);
        essStationDO.setServiceYear(baseMap.get("投运年").get(s).toString());
        essStationDO.setRetireYear(baseMap.get("退役年").get(s).toString());
        essStationDO.setStatus(StationStatusEnum.getStatusCode(baseMap.get("状态").get(s).toString()));
        essStationDO.setTypeCode(StationTypeEnum.getTypeCode(baseMap.get("类型").get(s).toString()));
        essStationDO.setInstalledCapacity((BigDecimal) baseMap.get("装机容量/万Kw").get(s));
        essStationDO.setUnitNum(Integer.valueOf(baseMap.get("机组台数/n").get(s).toString()));
        essStationDO.setAnnualEnergyOutput((BigDecimal) baseMap.get("年发电量/亿kWh").get(s));
        essStationDO.setAnnualUtilizationHour((BigDecimal) baseMap.get("年利用小时数/h").get(s));
        essStationDO.setOverhaulWeek(Integer.valueOf(baseMap.get("检修周数/n").get(s).toString()));
        essStationDO.setMaxOverhaulCapacity((BigDecimal) baseMap.get("最大检修能力").get(s));
        essStationDO.setMinTechOutput((BigDecimal) baseMap.get("最小技术出力").get(s));
        essStationDO.setClimbConstraint(baseMap.get("爬坡约束").get(s).equals("是") ? 1 : 0);
        essStationDO.setClimbAbility((BigDecimal) baseMap.get("爬坡能力").get(s));
        essStationDO.setAverageFuelRate((BigDecimal) baseMap.get("平均燃料消耗率（g/kWh)").get(s));
        Object location = baseMap.get("电站位置").get(s);
        if (Objects.nonNull(location)) {
            essStationDO.setLocation(location.toString());
        }

        //校验数据是否满足要求
        vaildEssStationDO(essStationDO);

        Long stationId = this.insertStation(essStationDO);
        essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                .projectId(project.getId())
                .stationId(stationId)
                .build());

        String userId = EssThreadLocalrHolder.getUserId().toString();

        Map<String, ArrayList<BigDecimal>> verhaulScheduleMap = listMap.get("检修安排");
        ArrayList<BigDecimal> verhaulScheduleList = verhaulScheduleMap.get(s);
        //校验检修安排数据
        vaildVerHaulList(verhaulScheduleList);

        Map<String, ArrayList<BigDecimal>> peakAmplitudeMap = listMap.get("调峰幅度（%）");
        ArrayList<BigDecimal> peakAmplitudeMapList = peakAmplitudeMap.get(s);

        List<StationScheduleWeekDO> scheduleWeekDOList = new ArrayList<>(52);
        for (int i = 0; i < 52; i++) {
            StationScheduleWeekDO stationScheduleWeekDO = new StationScheduleWeekDO();
            stationScheduleWeekDO.setStationId(stationId);
            stationScheduleWeekDO.setWeek(i + 1);
            stationScheduleWeekDO.setVerhaulSchedule(verhaulScheduleList.get(i).intValue());
            stationScheduleWeekDO.setPeakAmplitude(peakAmplitudeMapList.get(i).multiply(BigDecimal.valueOf(0.01)));;
            stationScheduleWeekDO.setCreator(userId);
            stationScheduleWeekDO.setUpdater(userId);
            //校验周数据是否符合要求
            vaildStationScheduleWeekDO(stationScheduleWeekDO, i);
            scheduleWeekDOList.add(stationScheduleWeekDO);
        }
        stationScheduleWeekMapper.insertBatch(scheduleWeekDOList);
        log.info("{} 数据入库完成", s);

        return true;
    }

    private void vaildStationScheduleWeekDO(StationScheduleWeekDO stationScheduleWeekDO,Integer week) {
        if(stationScheduleWeekDO.getPeakAmplitude().compareTo(BigDecimal.valueOf(100))>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_PEAKSHAVINGAMPLIDUDE_IS_FALSE,week+1,stationScheduleWeekDO.getPeakAmplitude());
        }

    }

    @Override
    public boolean batchSaveOfHydro(EssProjectDO project, String s, Map<String, Map> baseMap, Map<String, Map> listMap, Integer categoryCode) {
        EssStationDO essStationDO = new EssStationDO();
        essStationDO.setProjectId(project.getId());
        essStationDO.setName(s);
        essStationDO.setCategoryCode(categoryCode);
        essStationDO.setServiceYear(baseMap.get("投运年").get(s).toString());
        essStationDO.setRetireYear(baseMap.get("退役年").get(s).toString());
        essStationDO.setStatus(StationStatusEnum.getStatusCode(baseMap.get("状态").get(s).toString()));
        essStationDO.setTypeCode(StationTypeEnum.getTypeCode(baseMap.get("类型").get(s).toString()));
        essStationDO.setInstalledCapacity((BigDecimal) baseMap.get("装机容量/万kW").get(s));
        essStationDO.setUnitNum(Integer.valueOf(baseMap.get("机组台数/n").get(s).toString()));
        essStationDO.setAnnualEnergyOutput((BigDecimal) baseMap.get("年发电量/亿kWh").get(s));
        essStationDO.setAnnualUtilizationHour((BigDecimal) baseMap.get("年利用小时数/h").get(s));
        essStationDO.setOverhaulWeek(Integer.valueOf(baseMap.get("检修周数/n").get(s).toString()));
        essStationDO.setMaxOverhaulCapacity((BigDecimal) baseMap.get("最大检修能力系数").get(s));
        essStationDO.setMaxReserveFactor((BigDecimal) baseMap.get("最大备用系数").get(s));
        essStationDO.setMinReserveFactor((BigDecimal) baseMap.get("最小备用系数").get(s));
        Object location = baseMap.get("电站位置").get(s);
        if (Objects.nonNull(location)) {
            essStationDO.setLocation(location.toString());
        }
        //校验数据是否满足要求
        vaildEssStationDO(essStationDO);
        Long stationId = this.insertStation(essStationDO);
        essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                .projectId(project.getId())
                .stationId(stationId)
                .build());

        String userId = EssThreadLocalrHolder.getUserId().toString();

        Map<String, ArrayList<BigDecimal>> verhaulScheduleMap = listMap.get("检修安排");
        ArrayList<BigDecimal> verhaulScheduleList = verhaulScheduleMap.get(s);
        //校验检修安排数据
        vaildVerHaulList(verhaulScheduleList);

        Map<String, ArrayList<BigDecimal>> expectedOutputFactorMap = listMap.get("预想出力系数");
        ArrayList<BigDecimal> expectedOutputFactorList = expectedOutputFactorMap.get(s);
        if (expectedOutputFactorList.stream().anyMatch(Objects::isNull)) {
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_EXPECTE_DOUTPUT_FACTOR_IS_FALSE);
        }

        Map<String, ArrayList<BigDecimal>> averageOutputFactorMap = listMap.get("平均出力系数");
        ArrayList<BigDecimal> averageOutputFactorList = averageOutputFactorMap.get(s);
        if (averageOutputFactorList.stream().anyMatch(Objects::isNull)) {
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_AVERAGE_DOUTPUT_FACTOR_IS_FALSE);
        }

        Map<String, ArrayList<BigDecimal>> forcedOutputFactorMap = listMap.get("强迫出力系数");
        ArrayList<BigDecimal> forcedOutputFactorList = forcedOutputFactorMap.get(s);
        if (forcedOutputFactorList.stream().anyMatch(Objects::isNull)) {
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_FORCED_DOUTPUT_FACTOR_IS_FALSE);
        }

        Map<String, ArrayList<BigDecimal>> peakAmplitudeMap = listMap.get("调峰幅度（%）");
        ArrayList<BigDecimal> peakAmplitudeMapList = peakAmplitudeMap.get(s);


        List<StationScheduleWeekDO> scheduleWeekDOList = new ArrayList<>(52);
        for (int i = 0; i < 52; i++) {
            StationScheduleWeekDO stationScheduleWeekDO = new StationScheduleWeekDO();
            stationScheduleWeekDO.setStationId(stationId);
            stationScheduleWeekDO.setWeek(i + 1);
            stationScheduleWeekDO.setVerhaulSchedule(verhaulScheduleList.get(i).intValue());
            stationScheduleWeekDO.setAverageOutputFactor(averageOutputFactorList.get(i));
            stationScheduleWeekDO.setForcedOutputFactor(forcedOutputFactorList.get(i));
            stationScheduleWeekDO.setExpectedOutputFactor(expectedOutputFactorList.get(i));
            stationScheduleWeekDO.setCreator(userId);
            stationScheduleWeekDO.setUpdater(userId);
            stationScheduleWeekDO.setPeakAmplitude(peakAmplitudeMapList.get(i).multiply(BigDecimal.valueOf(0.01)));;
            //校验周数据是否符合要求
            vaildStationScheduleWeekDO(stationScheduleWeekDO, i);
            scheduleWeekDOList.add(stationScheduleWeekDO);
        }
        stationScheduleWeekMapper.insertBatch(scheduleWeekDOList);
        log.info("{} 数据入库完成", s);

        return true;
    }

    private void vaildEssStationDO(EssStationDO essStationDO) {
        //状态
        if (Objects.isNull(essStationDO.getStatus())){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_STATUS_IS_FALSE);
        }
        //类型
        if (Objects.isNull(essStationDO.getTypeCode())){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_TYPE_CODE_IS_FALSE);
        }
        //装机容量
        if (Objects.nonNull(essStationDO.getInstalledCapacity())&&essStationDO.getInstalledCapacity().compareTo(new BigDecimal(100000))>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_INSTALLED_CAPACITY_IS_FALSE,essStationDO.getInstalledCapacity());
        }
        //机组台数
        if (Objects.nonNull(essStationDO.getUnitNum())&&essStationDO.getUnitNum().compareTo(100000)>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_UNIT_NUM_IS_FALSE,essStationDO.getUnitNum());
        }
        //年发电量
        if (Objects.nonNull(essStationDO.getAnnualEnergyOutput())&&essStationDO.getAnnualEnergyOutput().compareTo(new BigDecimal(100000))>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_YEAR_ENERGY_IS_FALSE,essStationDO.getAnnualEnergyOutput());
        }
        //年利用小时数
        if (Objects.nonNull(essStationDO.getAnnualUtilizationHour())&&essStationDO.getAnnualUtilizationHour().compareTo(new BigDecimal(8760))>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_YEAR_HOUR_IS_FALSE,essStationDO.getAnnualUtilizationHour());
        }
        //检修周数
        if (Objects.nonNull(essStationDO.getOverhaulWeek())&&essStationDO.getOverhaulWeek().compareTo(52)>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_WEEK_IS_FALSE,essStationDO.getOverhaulWeek());
        }
        //最小备用系数
        if (Objects.nonNull(essStationDO.getMinReserveFactor())&&essStationDO.getMinReserveFactor().compareTo(BigDecimal.valueOf(1))>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_MINRESERVEFACTOR_IS_FALSE,essStationDO.getMinReserveFactor());
        }
        //最大备用系数
        if (Objects.nonNull(essStationDO.getMaxReserveFactor())&&essStationDO.getMaxReserveFactor().compareTo(BigDecimal.valueOf(1))>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_MAXRESERVEFACTOR_IS_FALSE,essStationDO.getMaxReserveFactor());
        }
        //爬坡能力
        if (Objects.nonNull(essStationDO.getClimbAbility())&&essStationDO.getClimbAbility().compareTo(BigDecimal.valueOf(1))>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_ABILITYCLIMBGRADES_IS_FALSE,essStationDO.getClimbAbility());
        }
        //最小技术出力
        if (Objects.nonNull(essStationDO.getMinTechOutput())&&essStationDO.getMinTechOutput().compareTo(BigDecimal.valueOf(1))>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_MINTECHOUTPUT_IS_FALSE,essStationDO.getMinTechOutput());
        }
        //最大检修能力
        if (Objects.nonNull(essStationDO.getMaxOverhaulCapacity())&&essStationDO.getMaxOverhaulCapacity().compareTo(BigDecimal.valueOf(1))>0){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_MAXOVERHAULCAPACITY_IS_FALSE,essStationDO.getMaxOverhaulCapacity());
        }
        //电站位置
        if (Objects.nonNull(essStationDO.getLocation())&&essStationDO.getLocation().length()>100){
            throw exception(ErrorCodeConstants.STATION_IMPORY_EXCEL_LOCATION_IS_FALSE);
        }
    }

    @Override
    public boolean batchSaveOfPumpedStorage(EssProjectDO project, String s, Map<String, Map> baseMap, Map<String, Map> listMap, Integer categoryCode) {
        EssStationDO essStationDO = new EssStationDO();
        essStationDO.setProjectId(project.getId());
        essStationDO.setName(s);
        essStationDO.setCategoryCode(categoryCode);
        essStationDO.setServiceYear(baseMap.get("投运年").get(s).toString());
        essStationDO.setRetireYear(baseMap.get("退役年").get(s).toString());
        essStationDO.setStatus(StationStatusEnum.getStatusCode(baseMap.get("状态").get(s).toString()));
        essStationDO.setTypeCode(StationTypeEnum.getTypeCode(baseMap.get("类型").get(s).toString()));
        essStationDO.setInstalledCapacity((BigDecimal) baseMap.get("装机容量/万kW").get(s));
        essStationDO.setUnitNum(Integer.valueOf(baseMap.get("机组台数/n").get(s).toString()));
        essStationDO.setOverhaulWeek(Integer.valueOf(baseMap.get("检修周数/n").get(s).toString()));
        essStationDO.setMaxOverhaulCapacity((BigDecimal) baseMap.get("最大检修能力").get(s));
        essStationDO.setMaxReserveFactor((BigDecimal) baseMap.get("最大备用系数").get(s));
        essStationDO.setMinReserveFactor((BigDecimal) baseMap.get("最小备用系数").get(s));

        essStationDO.setOverallEfficiency(((BigDecimal) baseMap.get("综合效率/%").get(s)).multiply(BigDecimal.valueOf(0.01)));
        essStationDO.setChargeFactor((BigDecimal) baseMap.get("充电系数").get(s));
        essStationDO.setGenerateFactor((BigDecimal) baseMap.get("发电系数").get(s));
        essStationDO.setFullGenerateHour((BigDecimal) baseMap.get("连续满发小时数/h").get(s));

        Object location = baseMap.get("电站位置").get(s);
        if (Objects.nonNull(location)) {
            essStationDO.setLocation(location.toString());
        }

        //校验数据是否满足要求
        vaildEssStationDO(essStationDO);

        Long stationId = this.insertStation(essStationDO);
        essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                .projectId(project.getId())
                .stationId(stationId)
                .build());

        String userId = EssThreadLocalrHolder.getUserId().toString();

        Map<String, ArrayList<BigDecimal>> verhaulScheduleMap = listMap.get("检修安排");
        ArrayList<BigDecimal> verhaulScheduleList = verhaulScheduleMap.get(s);
        //校验检修安排数据
        vaildVerHaulList(verhaulScheduleList);

        Map<String, ArrayList<BigDecimal>> peakAmplitudeMap = listMap.get("调峰幅度（%）");
        ArrayList<BigDecimal> peakAmplitudeMapList = peakAmplitudeMap.get(s);


        List<StationScheduleWeekDO> scheduleWeekDOList = new ArrayList<>(52);
        for (int i = 0; i < 52; i++) {
            StationScheduleWeekDO stationScheduleWeekDO = new StationScheduleWeekDO();
            stationScheduleWeekDO.setStationId(stationId);
            stationScheduleWeekDO.setWeek(i + 1);
            stationScheduleWeekDO.setVerhaulSchedule(verhaulScheduleList.get(i).intValue());
            stationScheduleWeekDO.setPeakAmplitude(peakAmplitudeMapList.get(i).multiply(BigDecimal.valueOf(0.01)));;
            stationScheduleWeekDO.setCreator(userId);
            stationScheduleWeekDO.setUpdater(userId);
            //校验周数据是否符合要求
            vaildStationScheduleWeekDO(stationScheduleWeekDO, i);
            scheduleWeekDOList.add(stationScheduleWeekDO);
        }
        stationScheduleWeekMapper.insertBatch(scheduleWeekDOList);
        log.info("{} 数据入库完成", s);


        return true;
    }

    @Override
    public boolean batchSaveOfWindOrPv(EssProjectDO project, String s, Map<String, Map> baseMap, Map<String, Map> listMap, Integer categoryCode) {
        EssStationDO essStationDO = new EssStationDO();
        essStationDO.setProjectId(project.getId());
        essStationDO.setName(s);
        essStationDO.setCategoryCode(categoryCode);
        essStationDO.setServiceYear(baseMap.get("投运年").get(s).toString());
        essStationDO.setRetireYear(baseMap.get("退役年").get(s).toString());
        essStationDO.setStatus(StationStatusEnum.getStatusCode(baseMap.get("状态").get(s).toString()));
        essStationDO.setTypeCode(StationTypeEnum.getTypeCode(baseMap.get("类型").get(s).toString()));
        essStationDO.setInstalledCapacity((BigDecimal) baseMap.get("装机容量/万kW").get(s));

        essStationDO.setUnitNum(Integer.valueOf(baseMap.get("风机数量/n").get(s).toString()));
        essStationDO.setAnnualEnergyOutput((BigDecimal) baseMap.get("年发电量/亿kWh").get(s));
        essStationDO.setAnnualUtilizationHour((BigDecimal) baseMap.get("年利用小时数/h").get(s));
        essStationDO.setSimultaneityRate((BigDecimal) baseMap.get("同时率").get(s));

        Object location = baseMap.get("电站位置").get(s);
        if (Objects.nonNull(location)) {
            essStationDO.setLocation(location.toString());
        }

        //校验数据是否满足要求
        vaildEssStationDO(essStationDO);

        Long stationId = this.insertStation(essStationDO);
        essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                .projectId(project.getId())
                .stationId(stationId)
                .build());


        Map<String, ArrayList<BigDecimal>> map = listMap.get("出力过程");
        ArrayList<BigDecimal> list = map.get(s);

        List<StationScheduleHourDO> scheduleHourDOList = HourDataCalcUtils.processHourData(list, stationId, s, project.getLevelYear(), EssStationServiceImpl::buildHourDo);
        List<StationScheduleHourDO> subLoadHourDOS = new ArrayList<>(EssConstant.BATCH_SIZE);
        for (int i = 1; i <= scheduleHourDOList.size(); i++) {
            subLoadHourDOS.add(scheduleHourDOList.get(i - 1));
            if (i % EssConstant.BATCH_SIZE == 0 || i == scheduleHourDOList.size()) {
                //检测到异常终止
                if (StringUtils.isNotEmpty(essRedisService.hasErrorV2(project.getId()))) {
                    log.error("风电执行中-检测到其他线程异常任务终止");
                    break;
                }
                stationScheduleHourMapper.batchSaveStationSchedulerHour(subLoadHourDOS);
                subLoadHourDOS = new ArrayList<>(EssConstant.BATCH_SIZE);
            }
        }

        log.info("{} 数据入库完成", s);

        return true;
    }

    @Override
    public boolean batchSaveOfPv(EssProjectDO project, String s, Map<String, Map> baseMap, Map<String, Map> listMap, Integer categoryCode) {
        EssStationDO essStationDO = new EssStationDO();
        essStationDO.setProjectId(project.getId());
        essStationDO.setName(s);
        essStationDO.setCategoryCode(categoryCode);
        essStationDO.setServiceYear(baseMap.get("投运年").get(s).toString());
        essStationDO.setRetireYear(baseMap.get("退役年").get(s).toString());
        essStationDO.setStatus(StationStatusEnum.getStatusCode(baseMap.get("状态").get(s).toString()));
        essStationDO.setTypeCode(StationTypeEnum.getTypeCode(baseMap.get("类型").get(s).toString()));
        essStationDO.setInstalledCapacity((BigDecimal) baseMap.get("装机容量/万kW").get(s));

        essStationDO.setUnitNum(Integer.valueOf(baseMap.get("光伏机组数量/n").get(s).toString()));
        essStationDO.setAnnualEnergyOutput((BigDecimal) baseMap.get("年发电量/亿kWh").get(s));
        essStationDO.setAnnualUtilizationHour((BigDecimal) baseMap.get("年利用小时数/h").get(s));
        essStationDO.setSimultaneityRate((BigDecimal) baseMap.get("同时率").get(s));

        Object location = baseMap.get("电站位置").get(s);
        if (Objects.nonNull(location)) {
            essStationDO.setLocation(location.toString());
        }

        //校验数据是否满足要求
        vaildEssStationDO(essStationDO);

        Long stationId = this.insertStation(essStationDO);
        essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                .projectId(project.getId())
                .stationId(stationId)
                .build());


        Map<String, ArrayList<BigDecimal>> map = listMap.get("出力过程");
        ArrayList<BigDecimal> list = map.get(s);

        List<StationScheduleHourDO> scheduleHourDOList = HourDataCalcUtils.processHourData(list, stationId, s, project.getLevelYear(), EssStationServiceImpl::buildHourDo);
        List<StationScheduleHourDO> subLoadHourDOS = new ArrayList<>(EssConstant.BATCH_SIZE);
        for (int i = 1; i <= scheduleHourDOList.size(); i++) {
            subLoadHourDOS.add(scheduleHourDOList.get(i - 1));
            if (i % EssConstant.BATCH_SIZE == 0 || i == scheduleHourDOList.size()) {
                //检测到异常终止
                if (StringUtils.isNotEmpty(essRedisService.hasErrorV2(project.getId()))) {
                    log.error("风电执行中-检测到其他线程异常任务终止");
                    break;
                }
                stationScheduleHourMapper.batchSaveStationSchedulerHour(subLoadHourDOS);
                subLoadHourDOS = new ArrayList<>(EssConstant.BATCH_SIZE);
            }
        }

        log.info("{} 数据入库完成", s);

        return true;
    }

    @Override
    public boolean batchSaveOfCspOrBiomassOrOther(EssProjectDO project, String s, Map<String, Map> baseMap, Map<String, Map> listMap, Integer categoryCode) {
        EssStationDO essStationDO = new EssStationDO();
        essStationDO.setProjectId(project.getId());
        essStationDO.setName(s);
        essStationDO.setCategoryCode(categoryCode);
        essStationDO.setServiceYear(baseMap.get("投运年").get(s).toString());
        essStationDO.setRetireYear(baseMap.get("退役年").get(s).toString());
        essStationDO.setStatus(StationStatusEnum.getStatusCode(baseMap.get("状态").get(s).toString()));
        essStationDO.setTypeCode(StationTypeEnum.getTypeCode(baseMap.get("类型").get(s).toString()));
        essStationDO.setInstalledCapacity((BigDecimal) baseMap.get("装机容量/万kW").get(s));
        essStationDO.setUnitNum(Integer.valueOf(baseMap.get("机组台数/n").get(s).toString()));
        essStationDO.setAnnualEnergyOutput((BigDecimal) baseMap.get("年发电量/亿kWh").get(s));
        essStationDO.setAnnualUtilizationHour((BigDecimal) baseMap.get("年利用小时数/h").get(s));
        essStationDO.setOverhaulWeek(Integer.valueOf(baseMap.get("检修周数/n").get(s).toString()));
        essStationDO.setMaxOverhaulCapacity((BigDecimal) baseMap.get("最大检修能力").get(s));
        essStationDO.setMinTechOutput((BigDecimal) baseMap.get("最小技术出力").get(s));
        essStationDO.setClimbConstraint(baseMap.get("爬坡约束").get(s).equals("是") ? 1 : 0);
        essStationDO.setClimbAbility((BigDecimal) baseMap.get("爬坡能力").get(s));
        essStationDO.setAverageFuelRate((BigDecimal) baseMap.get("平均燃料消耗率（g/kWh)").get(s));
        Object location = baseMap.get("电站位置").get(s);

        if (Objects.nonNull(location)) {
            essStationDO.setLocation(location.toString());
        }

        //校验数据是否满足要求
        vaildEssStationDO(essStationDO);

        Long stationId = this.insertStation(essStationDO);
        essRedisService.dataChangeInfoRecord(EssDeleteDataEditVo.builder()
                .projectId(project.getId())
                .stationId(stationId)
                .build());

        String userId = EssThreadLocalrHolder.getUserId().toString();

        //折算煤耗曲线
        saveEnergyCurve(s, listMap, stationId, userId, "折算煤耗曲线（g/kWh)");

        Map<String, ArrayList<BigDecimal>> blockedFactorMap = listMap.get("受阻系数");
        ArrayList<BigDecimal> blockedFactorList = blockedFactorMap.get(s);

        Map<String, ArrayList<BigDecimal>> verhaulScheduleMap = listMap.get("检修安排");
        ArrayList<BigDecimal> verhaulScheduleList = verhaulScheduleMap.get(s);
        //校验检修安排数据
        vaildVerHaulList(verhaulScheduleList);

        Map<String, ArrayList<BigDecimal>> peakAmplitudeMap = listMap.get("调峰幅度（%）");
        ArrayList<BigDecimal> peakAmplitudeMapList = peakAmplitudeMap.get(s);


        List<StationScheduleWeekDO> scheduleWeekDOList = new ArrayList<>(52);
        for (int i = 0; i < 52; i++) {
            StationScheduleWeekDO stationScheduleWeekDO = new StationScheduleWeekDO();
            stationScheduleWeekDO.setStationId(stationId);
            stationScheduleWeekDO.setWeek(i + 1);
            stationScheduleWeekDO.setBlockedFactor(blockedFactorList.get(i));
            stationScheduleWeekDO.setVerhaulSchedule(verhaulScheduleList.get(i).intValue());
            stationScheduleWeekDO.setPeakAmplitude(peakAmplitudeMapList.get(i).multiply(BigDecimal.valueOf(0.01)));
            ;
            stationScheduleWeekDO.setCreator(userId);
            stationScheduleWeekDO.setUpdater(userId);
            //校验周数据是否符合要求
            vaildStationScheduleWeekDO(stationScheduleWeekDO, i);
            scheduleWeekDOList.add(stationScheduleWeekDO);
        }
        stationScheduleWeekMapper.insertBatch(scheduleWeekDOList);

        log.info("{} 数据入库完成", s);

        return true;
    }


    /**
     * 解析并保存煤耗曲线
     */
    private void saveEnergyCurve(String s, Map<String, Map> listMap, Long stationId, String userId, String key) {
        Map<String, ArrayList<BigDecimal>> coalPowerCurveMap = listMap.get(key);
        ArrayList<BigDecimal> coalPowerList = coalPowerCurveMap.get(s);
        List<StationEnergyCurveDO> list = new ArrayList<>(11);
        BigDecimal usedFactor = BigDecimal.ZERO;
        if (CollectionUtil.isEmpty(coalPowerList)) {
            return;
        }
        for (int i = 0; i < coalPowerList.size(); i++) {
            StationEnergyCurveDO energyCurveDO = new StationEnergyCurveDO();
            energyCurveDO.setStationId(stationId);
            energyCurveDO.setUsedFactor(usedFactor);
            energyCurveDO.setLoadFactor(coalPowerList.get(i));
            energyCurveDO.setCreateTime(LocalDateTime.now());
            energyCurveDO.setUpdateTime(LocalDateTime.now());
            energyCurveDO.setCreator(userId);
            energyCurveDO.setUpdater(userId);

            list.add(energyCurveDO);
            usedFactor = usedFactor.add(BigDecimal.valueOf(0.1));
        }
        this.stationEnergyCurveMapper.insertBatch(list);
    }

    @Override
    public List<EssStationDO> selectListByProjectIdAndCategoryCodeAndStatus(Long projectId, Integer categoryCode, Integer status) {
        QueryWrapperX<EssStationDO> queryWrapper = new QueryWrapperX<>();
        queryWrapper.eq("project_id", projectId);
        if (Objects.nonNull(categoryCode)) {
            queryWrapper.eq("category_code", categoryCode);
        }
        if (Objects.nonNull(status)) {
            queryWrapper.eq("status", status);
        }
        return essStationMapper.selectList(queryWrapper);
    }

    @Override
    public Map<Long, EssStationDO>  loadMatchLevelYear(Long projectId, String levelYear) {
        QueryWrapperX<EssStationDO> queryWrapper = new QueryWrapperX<>();
        queryWrapper.eq("project_id", projectId);
        return essStationMapper.selectList(queryWrapper).stream()
                .filter(o -> o.matchActive(SafeConverter.toInt(levelYear, -1)))
                .collect(Collectors.toMap(EssStationDO::getId, Function.identity()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStationByStationIds(List<Long> stationIdList) {
        essStationMapper.deleteByStationIds(stationIdList);
        stationEnergyCurveMapper.deleteByStationIds(stationIdList);
        stationScheduleWeekMapper.deleteByStationIds(stationIdList);
        stationScheduleHourMapper.deleteByStationIds(stationIdList);
    }

    @Override
    public List<EssStationDO> selectListByIds(List<Long> stationIdList) {
        return essStationMapper.selectBatchIds(stationIdList);
    }


    public static StationScheduleHourDO buildHourDo(Long stationId, int weekOfYear, int dayOfMonth, int month, int hour, BigDecimal value, String s, String levelYear) {
        LocalDateTime dateTime = LocalDateTime.of(Integer.parseInt(levelYear), month, dayOfMonth, hour - 1, 0);
        // 格式化日期时间
        String loadDate = dateTime.format(EssConstant.FORMATTER);

        String userId = EssThreadLocalrHolder.getUserId().toString();
        StationScheduleHourDO scheduleHourDO = new StationScheduleHourDO();
        scheduleHourDO.setStationId(stationId);
        scheduleHourDO.setOutputDate(loadDate);
        scheduleHourDO.setMonth(month);
        scheduleHourDO.setWeek(weekOfYear);
        scheduleHourDO.setDay(dayOfMonth);
        scheduleHourDO.setHour(String.valueOf(hour));
        scheduleHourDO.setFactor(value);

        scheduleHourDO.setCreator(userId);
        scheduleHourDO.setCreateTime(LocalDateTime.now());
        scheduleHourDO.setUpdater(userId);
        scheduleHourDO.setUpdateTime(LocalDateTime.now());
        scheduleHourDO.setDeleted(false);
        return scheduleHourDO;
    }
}