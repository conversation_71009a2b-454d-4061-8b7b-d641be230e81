package cn.powerchina.bjy.cloud.institute.essz.dal.mysql.paramterbase;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterbase.vo.ParamterBasePageReqVO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.paramterbase.ParamterBaseDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 电源经济性参数(基础) Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ParamterBaseMapper extends BaseMapperX<ParamterBaseDO> {

    default PageResult<ParamterBaseDO> selectPage(ParamterBasePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ParamterBaseDO>()
                .eqIfPresent(ParamterBaseDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ParamterBaseDO::getDiscountRate, reqVO.getDiscountRate())
                .eqIfPresent(ParamterBaseDO::getCalcServiceYear, reqVO.getCalcServiceYear())
                .eqIfPresent(ParamterBaseDO::getReceivedPrice, reqVO.getReceivedPrice())
                .betweenIfPresent(ParamterBaseDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ParamterBaseDO::getId));
    }

    @Delete("delete from ess_paramter_base where id=#{paramBaseId}")
    void deleteParamterBaseById(@Param("paramBaseId") Long paramBaseId);
}