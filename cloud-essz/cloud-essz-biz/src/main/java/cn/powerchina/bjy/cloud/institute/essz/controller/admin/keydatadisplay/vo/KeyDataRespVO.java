package cn.powerchina.bjy.cloud.institute.essz.controller.admin.keydatadisplay.vo;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 关键数据 Response VO")
@Data
public class KeyDataRespVO {
    @Schema(description = "年类型（1-现状年、2-水平年）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer yearTypeCode;

    @Schema(description = "最大负荷/万kW", requiredMode = Schema.RequiredMode.REQUIRED, example = "3680")
    private BigDecimal maxLoad;

    @Schema(description = "全社会用电量/亿kWh", requiredMode = Schema.RequiredMode.REQUIRED, example = "3680")
    private BigDecimal usePower;
    
    @Schema(description = "电站相关信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<KeyDataStationVO> station;
}
