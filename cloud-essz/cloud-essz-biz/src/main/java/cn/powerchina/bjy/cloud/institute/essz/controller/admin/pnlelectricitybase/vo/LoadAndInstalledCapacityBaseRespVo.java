package cn.powerchina.bjy.cloud.institute.essz.controller.admin.pnlelectricitybase.vo;

import cn.powerchina.bjy.cloud.institute.essz.config.BigDecimalFormatRound2dpSerializer;
import cn.powerchina.bjy.cloud.institute.essz.service.pnlelectricitypower.annotation.FieldName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LoadAndInstalledCapacityBaseRespVo {

    /**
     * 煤电(供热)  可用/损耗/开机
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "煤电(供热)", type = "可用容量")
    @Schema(description = "煤电(供热)")
    private BigDecimal heatingCoalPower;
    /**
     * 煤电(常规)  可用/损耗/开机
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "煤电(常规)", type = "可用容量")
    @Schema(description = "煤电(常规)")
    private BigDecimal nonHeatingCoalPower;

    /**
     * 煤电整体 可用/损耗/开机
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @Schema(description = "煤电整体可用容量")
    private BigDecimal coalPower;

    /**
     * 气电  可用/损耗/开机
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "气电", type = "可用容量")
    @Schema(description = "气电")
    private BigDecimal gasPower;
    /**
     * 核电  可用/损耗/开机
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "核电", type = "可用容量")
    @Schema(description = "核电")
    private BigDecimal nuclearPower;
    /**
     * 常规水电 可用/损耗/开机
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "常规水电", type = "可用容量")
    @Schema(description = "常规水电")
    private BigDecimal hydroPower;
    /**
     * 抽水蓄能  可用/损耗/开机
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "抽水蓄能", type = "可用容量")
    @Schema(description = "抽水蓄能")
    private BigDecimal pumpedStoragePower;
    /**
     * 风电
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "风电", type = "可用容量")
    @Schema(description = "风电")
    private BigDecimal windPower;
    /**
     * 光伏
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "光伏", type = "可用容量")
    @Schema(description = "光伏")
    private BigDecimal solarPower;
    /**
     * 光热
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "光热", type = "可用容量")
    @Schema(description = "光热")
    private BigDecimal solarThermalPower;
    /**
     * 生物质发电
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "生物质发电", type = "可用容量")
    @Schema(description = "生物质发电")
    private BigDecimal biomassPower;
    /**
     * 新型蓄能  可用/损耗/开机
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "新型蓄能", type = "可用容量")
    @Schema(description = "新型蓄能")
    private BigDecimal newEnergyStorage;
    /**
     * 其他
     */
    @JsonSerialize(using = BigDecimalFormatRound2dpSerializer.class)
    @FieldName(value = "其他", type = "可用容量")
    @Schema(description = "其他")
    private BigDecimal other;
}
