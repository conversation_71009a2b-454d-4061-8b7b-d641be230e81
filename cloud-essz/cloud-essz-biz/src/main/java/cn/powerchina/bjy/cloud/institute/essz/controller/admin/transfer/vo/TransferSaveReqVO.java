package cn.powerchina.bjy.cloud.institute.essz.controller.admin.transfer.vo;

import cn.powerchina.bjy.cloud.institute.essz.config.BigDecimalFormatRound2dpSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 跨区域输送电新增/修改 Request VO")
@Data
public class TransferSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private Integer id;

    @Schema(description = "工程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotNull(message = "工程ID不能为空")
    private Integer projectId;

    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "项目名称不能为空")
    private String name;

    @Schema(description = "投运年", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "投运年不能为空")
    private String serviceYear;

    @Schema(description = "退役年", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "退役年不能为空")
    private String retireYear;

    @Schema(description = "状态（1-规划、2-在建、3-投运、4-退役、5-其他）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态（1-规划、2-在建、3-投运、4-退役、5-其他）不能为空")
    private Integer status;

    @Schema(description = "输送类型（1-送电、2-受电）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "输送类型（1-送电、2-受电）不能为空")
    private Integer typeCode;

    @Schema(description = "通道能力/万kW")
    private BigDecimal channelCapacity;

    @Schema(description = "通道电量/亿kWh")
    private BigDecimal channelPower;

    @Schema(description = "年利用小时数/h")
    private BigDecimal annualUtilizationHour;

    @Schema(description = "调节系数/%")
    private BigDecimal adjustFactor;

    @Schema(description = "送出位置")
    private String outLocation;

    @Schema(description = "送入位置")
    private String inLocation;

}