package cn.powerchina.bjy.cloud.institute.essz.util.ess;

import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;

/**
 * <AUTHOR>
 * @desc excel工具类
 * @time 2024/7/10 19:17
 */
@Slf4j
public class ExcelUtils {

    public static AnalysisEventListener createListener(Class<? extends AnalysisEventListener> clazz, Object... args) {
        try {
            Class<?>[] parameterTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                parameterTypes[i] = args[i].getClass();
            }
            return clazz.getDeclaredConstructor(parameterTypes).newInstance(args);
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            log.error("未知异常：",e);
            return null;
        }
    }
}
