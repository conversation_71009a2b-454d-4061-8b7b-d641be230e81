package cn.powerchina.bjy.cloud.institute.essz.service.paramterinvest;

import java.util.List;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterinvest.vo.ParamterInvestPageReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterinvest.vo.ParamterInvestSaveReqVO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.paramterinvest.ParamterInvestDO;
import jakarta.validation.*;

/**
 * 电源经济性参数(投资) Service 接口
 *
 * <AUTHOR>
 */
public interface ParamterInvestService {

    /**
     * 创建电源经济性参数(投资)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createParamterInvest(@Valid ParamterInvestSaveReqVO createReqVO);

    /**
     * 更新电源经济性参数(投资)
     *
     * @param updateReqVO 更新信息
     */
    void updateParamterInvest(@Valid ParamterInvestSaveReqVO updateReqVO);

    /**
     * 删除电源经济性参数(投资)
     *
     * @param id 编号
     */
    void deleteParamterInvest(Long id);

    /**
     * 获得电源经济性参数(投资)
     *
     * @param id 编号
     * @return 电源经济性参数(投资)
     */
    ParamterInvestDO getParamterInvest(Long id);

    /**
     * 获得电源经济性参数(投资)分页
     *
     * @param pageReqVO 分页查询
     * @return 电源经济性参数(投资)分页
     */
    PageResult<ParamterInvestDO> getParamterInvestPage(ParamterInvestPageReqVO pageReqVO);

    /**
     * 获得某项目所有电源经济性参数(投资)
     *
     * @param projectId 工程ID
     * @return 电源经济性参数(投资)
     */
    List<ParamterInvestDO> listParamterInvest(Long projectId);

    List<Long> batchInsert(List<ParamterInvestSaveReqVO> investSaveReqVOList, Long paramterBaseId);

    List<ParamterInvestDO> selectList(Long projectId);

    void deleteParamterInvestByParentId(Long paramBaseId);
}