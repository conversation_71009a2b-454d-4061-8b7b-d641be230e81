package cn.powerchina.bjy.cloud.institute.essz.util.ess;

import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * <AUTHOR>
 * @desc 登陆用户相关
 * @time 2024/7/17 15:46
 */
public class EssThreadLocalrHolder {
    /** 当前用户ID */
    private static final ThreadLocal<Long> USER_ID = new TransmittableThreadLocal<>();

    public static void removeUserId(){
        USER_ID.remove();
    }


    public static void setUserId(Long userId){
        USER_ID.set(userId);
    }

    public static Long getUserId(){
        return USER_ID.get();
    }



}
