package cn.powerchina.bjy.cloud.institute.essz.service.pnlelectricitypower.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldName {
    String value() default "";

    String type() default "";

    String pName() default "";

    boolean isShow() default true;
}
