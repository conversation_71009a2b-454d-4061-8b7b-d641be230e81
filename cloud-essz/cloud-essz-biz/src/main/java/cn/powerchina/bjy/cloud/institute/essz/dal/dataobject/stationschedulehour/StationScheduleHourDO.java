package cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.stationschedulehour;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 电源-小时-出力计划 DO
 *
 * <AUTHOR>
 */
@TableName("ess_station_schedule_hour")
@KeySequence("ess_station_schedule_hour_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StationScheduleHourDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 电源ID
     */
    private Long stationId;
    /**
     * 出力时间
     */
    private String outputDate;
    /**
     * 月数
     */
    private Integer month;

    /**
     * 周
     */
    private Integer week;

    /**
     * 天
     */
    private Integer day;
    /**
     * 小时
     */
    private String hour;
    /**
     * 系数(风电、光伏)
     * 0.222  没有乘以100%
     */
    private BigDecimal factor;

}