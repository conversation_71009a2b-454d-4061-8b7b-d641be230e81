package cn.powerchina.bjy.cloud.institute.essz.dal.mysql.loadyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.loadyear.vo.LoadYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.loadyear.LoadYearDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 电力网内负荷-现状年、水平年 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LoadYearMapper extends BaseMapperX<LoadYearDO> {

    default PageResult<LoadYearDO> selectPage(LoadYearPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LoadYearDO>()
                .eqIfPresent(LoadYearDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(LoadYearDO::getYear, reqVO.getYear())
                .eqIfPresent(LoadYearDO::getTypeCode, reqVO.getTypeCode())
                .eqIfPresent(LoadYearDO::getMaxLoad, reqVO.getMaxLoad())
                .eqIfPresent(LoadYearDO::getUsePower, reqVO.getUsePower())
                .eqIfPresent(LoadYearDO::getNewEnergy, reqVO.getNewEnergy())
                .eqIfPresent(LoadYearDO::getWindEnergy, reqVO.getWindEnergy())
                .eqIfPresent(LoadYearDO::getPvEnergy, reqVO.getPvEnergy())
                .eqIfPresent(LoadYearDO::getNewCurtailRate, reqVO.getNewCurtailRate())
                .eqIfPresent(LoadYearDO::getWindCurtailRate, reqVO.getWindCurtailRate())
                .eqIfPresent(LoadYearDO::getPvCurtailRate, reqVO.getPvCurtailRate())
                .eqIfPresent(LoadYearDO::getLineLossRate, reqVO.getLineLossRate())
                .eqIfPresent(LoadYearDO::getLoadSideFactor, reqVO.getLoadSideFactor())
                .eqIfPresent(LoadYearDO::getLoadReserveFactor, reqVO.getLoadReserveFactor())
                .eqIfPresent(LoadYearDO::getOverhaulReserveFactor, reqVO.getOverhaulReserveFactor())
                .eqIfPresent(LoadYearDO::getAccidentReserveFactor, reqVO.getAccidentReserveFactor())
                .betweenIfPresent(LoadYearDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LoadYearDO::getId));
    }


    @Select("select * from ess_load_year where project_id = #{projectId} and deleted = 0")
    List<LoadYearDO> listByProjectAndDeleted(@Param("projectId") Long projectId);

    @Delete("delete from ess_load_year where project_id = #{projectId} and type_code=#{typeCode}")
    int deleteByProjectIdAndTypeCode(@Param("projectId") Long projectId,@Param("typeCode") Integer typeCode);

    @Select("SELECT * FROM ess_load_year WHERE project_id = #{projectId} AND year = #{levelYear} AND type_code = 2  AND deleted = 0")
    LoadYearDO selectByProjectIdAndLevelYear(@Param("projectId") Long projectId, @Param("levelYear") String levelYear);
    
    void deleteBatchYearIds(@Param("yearIdList") List<Long> yearIdList);

    @Select("select * from ess_load_year where project_id=#{projectId} and type_code=#{typeCode} and year=#{year} and deleted = 0 ")
    LoadYearDO getLoadYearByProjectAndYearAndTypeCode(@Param("projectId") Long projectId, @Param("year") String levelYear, @Param("typeCode") Integer typeCode);

    @Select("select * from ess_load_year where project_id=#{projectId} and year=#{year} and deleted = 0 ")
    LoadYearDO getLoadYearByProjectIdAndLevelYear(@Param("projectId")Long id, @Param("year") String levelYear);

    void updateIgnoreNull(@Param("loadYear") LoadYearDO loadYear);
}