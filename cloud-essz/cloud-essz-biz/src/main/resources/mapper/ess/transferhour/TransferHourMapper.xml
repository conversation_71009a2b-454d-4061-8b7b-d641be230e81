<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.essz.dal.mysql.transferhour.TransferHourMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <!-- Mapper XML 文件中的批量插入语句 -->

    <!-- 批量插入操作 -->
    <insert id="batchSaveTransferHour" parameterType="java.util.List">
        INSERT INTO ess_transfer_hour (transfer_id, transfer_date, year, month,week, day, hour, factor, creator, create_time, updater, update_time, deleted)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.transferId}, #{item.transferDate}, #{item.year}, #{item.month},#{item.week},
            #{item.day}, #{item.hour}, #{item.factor}, #{item.creator},
            #{item.createTime}, #{item.updater}, #{item.updateTime},
            #{item.deleted}
            )
        </foreach>
    </insert>

    <delete id="deleteByTransferIds" parameterType="java.util.List">
        DELETE FROM ess_transfer_hour
        WHERE transfer_id IN
        <foreach collection="transferIdList" item="transferId" open="(" separator="," close=")">
            #{transferId}
        </foreach>
    </delete>
    <select id="selectSyntheticCurves"
            resultType="cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.transferhour.TransferHourDO">
        select sum(ess_transfer_hour.factor * transfer.channel_capacity) / sum(transfer.channel_capacity) as factor,
               ess_transfer_hour.transfer_date
        from ess_transfer_hour
                 left join (Select id, channel_capacity from ess_transfer) as transfer
                           on transfer.id = ess_transfer_hour.transfer_id
        where ess_transfer_hour.transfer_id in
        <foreach collection="transferIds" item = "transferId" open="(" separator="," close=")">
            #{transferId}
        </foreach>
        group by transfer_date

    </select>
    <select id="selectByTaransferIdsGroupByYearMonthDay"
            resultType="cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.transferhour.TransferHourDO">
        select transfer_date, month, day, hour, week, sum(factor) as factor
        from ess_transfer_hour
        where transfer_id in
        <foreach collection="transferIds"  item = "transferId" open="(" separator="," close=")">
            #{transferId}
        </foreach>
          and deleted = 0
        group by month, day, hour,week,transfer_date

    </select>

</mapper>